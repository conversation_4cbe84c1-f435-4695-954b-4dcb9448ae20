# TypeScript Fixes

This file contains all the fixes needed to resolve TypeScript errors in the project.

## 1. Fix for components/theme-provider.tsx

The error is:
```
components/theme-provider.tsx:29:6 - error TS2322: Type '{ children: string | number | bigint | boolean | Element | Iterable<ReactNode> | Promise<AwaitedReactNode> | null | undefined; ... 10 more ...; scriptProps?: ScriptProps | undefined; }' is not assignable to type 'ThemeProviderProps'.
  Types of property 'attribute' are incompatible.
    Type 'string | Attribute[]' is not assignable to type 'Attribute | Attribute[] | undefined'.
      Type 'string' is not assignable to type 'Attribute | Attribute[] | undefined'.
```

Fix:
```tsx
// Change
<NextThemesProvider {...defaultProps}>
  {children}
</NextThemesProvider>

// To
<NextThemesProvider 
  attribute={defaultProps.attribute as "class" | "data-theme"} 
  defaultTheme={defaultProps.defaultTheme}
  enableSystem={defaultProps.enableSystem}
  disableTransitionOnChange={defaultProps.disableTransitionOnChange}
  storageKey={defaultProps.storageKey}
>
  {children}
</NextThemesProvider>
```

## 2. Fix for components/admin/user-import-form.tsx

The error is:
```
components/admin/user-import-form.tsx:10:10 - error TS2305: Module '"lucide-react"' has no exported member 'FileUpload'.
```

Fix:
```tsx
// Change
import { FileUpload as FileUploadIcon, Users, Database, Upload, Loader2 } from "lucide-react"

// To
import { Upload as FileUploadIcon, Users, Database, Upload, Loader2 } from "lucide-react"
```

## 3. Fix for components/development-plan.tsx

The error is:
```
components/development-plan.tsx:10:15 - error TS2305: Module '"@/lib/actions/assessment"' has no exported member 'DevelopmentPlan'.
```

Fix:
```tsx
// Create a new interface in lib/actions/assessment.ts
export interface DevelopmentPlan {
  id: string;
  userId: string;
  name: string;
  goals: Array<{
    id: string;
    description: string;
    targetDate: string;
    status: 'not_started' | 'in_progress' | 'completed';
  }>;
  createdAt: string;
  updatedAt: string;
}
```

## 4. Fix for lib/actions/admin.ts

The error is:
```
lib/actions/admin.ts:182:23 - error TS2304: Cannot find name 'UserSummary'.
lib/actions/admin.ts:183:34 - error TS2304: Cannot find name 'UserSummary'.
lib/actions/admin.ts:408:26 - error TS2304: Cannot find name 'UserSummary'.
```

Fix:
```ts
// Add this interface at the top of the file
interface UserSummary {
  id: string;
  name: string;
  email: string;
  businessUnit: string;
  careerLevel: string;
  jobRole: string;
  lastLogin?: string;
  lastAssessment?: string;
  assessmentCount: number;
}
```

## 5. Fix for lib/services/settings-service.ts

The errors are:
```
lib/services/settings-service.ts:209:48 - error TS2322: Type 'number | boolean | never[]' is not assignable to type 'number'.
  Type 'boolean' is not assignable to type 'number'.
lib/services/settings-service.ts:231:47 - error TS2322: Type 'number | boolean | never[]' is not assignable to type 'boolean'.
  Type 'number' is not assignable to type 'boolean'.
```

Fix:
```ts
// Change
return typeof months === 'number' ? months : DEFAULT_SETTINGS[SETTINGS_KEYS.ASSESSMENT_INTERVAL_MONTHS];

// To
return typeof months === 'number' ? months : DEFAULT_SETTINGS[SETTINGS_KEYS.ASSESSMENT_INTERVAL_MONTHS] as number;

// Change
return typeof allow === 'boolean' ? allow : DEFAULT_SETTINGS[SETTINGS_KEYS.ALLOW_ASSESSMENT_UPDATES];

// To
return typeof allow === 'boolean' ? allow : DEFAULT_SETTINGS[SETTINGS_KEYS.ALLOW_ASSESSMENT_UPDATES] as boolean;
```

## 6. Fix for implicit any parameters

For all the implicit any parameter errors, add explicit type annotations:

```ts
// In lib/actions/admin-skills.ts
const serializedTemplates = templates.map((template: any) => serializeBUSkillTemplate(template));

// In lib/actions/admin-users.ts
data: profiles.map((profile: any) => serializeUserProfile(profile)),

// In lib/actions/assessment-cycles.ts
data: cycles.map((cycle: any) => serializeAssessmentCycle(cycle)),
cycle.teamMembers.forEach((member: any) => {
member.assessments.peers.forEach((peer: any) => {
assessments: assessments.map((assessment: any) => serializeAssessment(assessment)),
cycles: cycles.map((cycle: any) => serializeAssessmentCycle(cycle))
const selfAssessment = assessments.find((a: any) => a.relationshipType === 'self');
const managerAssessment = assessments.find((a: any) => a.relationshipType === 'manager');
const peerAssessments = assessments.filter((a: any) => a.relationshipType === 'peer');
peers: peerAssessments.map((assessment: any) => serializeAssessment(assessment)),
```
