{"name": "skills-assessment", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "TURBOPACK=1 NODE_OPTIONS='--max-old-space-size=4096' next dev --turbopack", "dev:turbo:fast": "TURBOPACK=1 NODE_OPTIONS='--max-old-space-size=4096' NEXT_TURBO_FAST_REFRESH=true next dev --turbopack", "dev:m3": "./scripts/dev-m3-optimized.sh", "build": "next build", "start": "next start", "lint": "next lint", "docker:dev": "docker-compose up app-dev", "docker:prod": "docker-compose --profile prod up app-prod", "docker:build": "docker-compose --profile prod build app-prod", "docker:down": "docker-compose down", "prod:stop": "./scripts/stop-prod.sh", "clear:cache": "rm -rf .next && rm -rf node_modules/.cache && rm -rf .turbo"}, "dependencies": {"@auth/core": "latest", "@aws-sdk/credential-providers": "latest", "@hookform/resolvers": "^3.9.1", "@mongodb-js/zstd": "latest", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "autoprefixer": "^10.4.20", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "critters": "^0.0.25", "date-fns": "latest", "embla-carousel-react": "8.5.1", "gcp-metadata": "latest", "input-otp": "1.4.1", "kerberos": "latest", "lucide-react": "^0.454.0", "mongodb": "latest", "mongodb-client-encryption": "latest", "next": "15.2.4", "next-auth": "latest", "next-themes": "^0.4.4", "nodemailer": "latest", "react": "^19", "react-chartjs-2": "^5.3.0", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "snappy": "latest", "socks": "latest", "sonner": "^1.7.1", "swr": "^2.3.3", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.24.0", "eslint-config-next": "^15.3.0", "nodemon": "^3.0.1", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}