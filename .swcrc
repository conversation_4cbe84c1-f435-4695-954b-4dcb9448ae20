{"$schema": "https://json.schemastore.org/swcrc", "jsc": {"parser": {"syntax": "typescript", "tsx": true, "decorators": false, "dynamicImport": true}, "transform": {"react": {"runtime": "automatic", "pragma": "React.createElement", "pragmaFrag": "React.Fragment", "throwIfNamespace": true, "development": false, "useBuiltins": true}}, "target": "es2020", "loose": true, "externalHelpers": true, "keepClassNames": false}, "minify": true, "module": {"type": "es6"}}