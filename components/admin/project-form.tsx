"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { createProject, updateProject } from "@/lib/actions/projects"
import { businessUnitOptions } from "@/lib/models/user-profile"
import { projectStatusOptions } from "@/lib/models/project"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2 } from "lucide-react"

interface ProjectFormProps {
  adminEmail: string
  project?: any
}

export default function ProjectForm({ adminEmail, project }: ProjectFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ text: string; type: 'success' | 'error' } | null>(null)

  const [formData, setFormData] = useState({
    name: project?.name || "",
    description: project?.description || "",
    businessUnit: project?.businessUnit || "",
    status: project?.status || "planned",
  })

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setMessage(null)

    try {
      // Validate required fields
      if (!formData.name || !formData.businessUnit) {
        setMessage({ text: "Name and Business Unit are required", type: "error" })
        setIsLoading(false)
        return
      }

      let result

      if (project?.id) {
        // Update existing project
        result = await updateProject(
          project.id,
          {
            name: formData.name,
            description: formData.description,
            businessUnit: formData.businessUnit,
            status: formData.status as any,
          },
          adminEmail
        )
      } else {
        // Create new project
        result = await createProject(
          {
            name: formData.name,
            description: formData.description,
            businessUnit: formData.businessUnit,
            status: formData.status as any,
          },
          adminEmail
        )
      }

      if (result.success) {
        setMessage({ text: result.message, type: "success" })

        // Redirect after successful creation
        // Use type assertion to handle the id property
        const resultWithId = result as { success: boolean; message: string; id?: string };
        if (!project?.id && resultWithId.id) {
          setTimeout(() => {
            router.push(`/admin/projects/${resultWithId.id}`)
          }, 1500)
        }
      } else {
        setMessage({ text: result.message || "An error occurred", type: "error" })
      }
    } catch (error) {
      console.error("Error saving project:", error)
      setMessage({ text: "An unexpected error occurred", type: "error" })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit}>
      <Card>
        <CardContent className="pt-6">
          {message && (
            <Alert className={`mb-6 ${message.type === 'success' ? 'bg-green-50 text-green-800 border-green-200' : 'bg-red-50 text-red-800 border-red-200'}`}>
              <div className="flex items-center gap-2">
                {message.type === 'success' ? (
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription>{message.text}</AlertDescription>
              </div>
            </Alert>
          )}

          <div className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Project Name <span className="text-red-500">*</span></Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleChange("name", e.target.value)}
                placeholder="Enter project name"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleChange("description", e.target.value)}
                placeholder="Enter project description"
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="businessUnit">Business Unit <span className="text-red-500">*</span></Label>
              <Select
                value={formData.businessUnit}
                onValueChange={(value) => handleChange("businessUnit", value)}
              >
                <SelectTrigger id="businessUnit">
                  <SelectValue placeholder="Select business unit" />
                </SelectTrigger>
                <SelectContent>
                  {businessUnitOptions.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option.charAt(0).toUpperCase() + option.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleChange("status", value)}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {projectStatusOptions.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option.charAt(0).toUpperCase() + option.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/admin/projects")}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : project?.id ? "Update Project" : "Create Project"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </form>
  )
}
