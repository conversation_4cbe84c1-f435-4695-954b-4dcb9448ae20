"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

import { saveBUSkillTemplate } from "@/lib/actions/admin-skills"
import { ratingScale } from "@/lib/skills-data"
import { ArrowLeft, Save, Plus, Trash } from "lucide-react"
interface SkillTemplateEditorProps {
  template: any
}

export default function SkillTemplateEditor({ template }: SkillTemplateEditorProps) {
  const router = useRouter()
  const { data: session } = useSession()
  const [isSaving, setIsSaving] = useState(false)
  const [message, setMessage] = useState<{ text: string; type: "success" | "error" } | null>(null)

  // Template state
  const [templateData, setTemplateData] = useState<any>({
    businessUnit: template.businessUnit,
    name: template.name,
    coreSkills: template.coreSkills || [],
    projectSkills: template.projectSkills || [],
    projectName: template.projectName || ''
  })

  // New skill state
  const [newSkill, setNewSkill] = useState({
    category: "",
    description: "",
    targetCL2: 1,
    targetCL3: 2,
    targetCL4: 3,
    targetCL5: 3,
    targetCL6: 4
  })

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTemplateData({
      ...templateData,
      name: e.target.value
    })
  }



  const handleSkillChange = (index: number, field: string, value: any) => {
    const skills = [...templateData.coreSkills]
    skills[index] = {
      ...skills[index],
      [field]: value
    }

    setTemplateData({
      ...templateData,
      coreSkills: skills
    })
  }

  const handleAddSkill = () => {
    // Generate a unique ID
    const id = `core-${Date.now()}`

    const newSkillWithId = {
      ...newSkill,
      id,
      currentLevel: null
    }

    setTemplateData({
      ...templateData,
      coreSkills: [...templateData.coreSkills, newSkillWithId]
    })

    // Reset new skill form
    setNewSkill({
      category: "",
      description: "",
      targetCL2: 1,
      targetCL3: 2,
      targetCL4: 3,
      targetCL5: 3,
      targetCL6: 4
    })
  }

  const handleRemoveSkill = (index: number) => {
    const skills = [...templateData.coreSkills]
    skills.splice(index, 1)
    setTemplateData({
      ...templateData,
      coreSkills: skills
    })
  }

  const handleSaveTemplate = async () => {
    if (!session?.user?.email) {
      setMessage({ text: "You must be signed in as an admin to save templates", type: "error" })
      return
    }

    if (!templateData.name.trim()) {
      setMessage({ text: "Please enter a template name", type: "error" })
      return
    }

    setIsSaving(true)

    const result = await saveBUSkillTemplate(templateData, session.user.email)

    setIsSaving(false)

    if (result.success) {
      setMessage({ text: result.message || "Template saved successfully", type: "success" })
      // Redirect after a short delay
      setTimeout(() => {
        router.push("/admin/skills")
        router.refresh()
      }, 1500)
    } else {
      setMessage({ text: result.message || "Failed to save template", type: "error" })
    }
  }

  return (
    <div className="space-y-6">
      <Button
        variant="outline"
        onClick={() => router.push("/admin/skills")}
        className="mb-4"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Templates
      </Button>

      {message && (
        <div
          className={`p-4 rounded-lg border ${message.type === "success" ? "bg-green-50 border-green-200 text-green-800" : "bg-red-50 border-red-200 text-red-800"
            }`}
        >
          {message.text}
        </div>
      )}

      <Card className="p-6 shadow-md border-primary/20 bg-card">
        <div className="space-y-4">
          <div>
            <Label htmlFor="templateName" className="text-sm font-medium mb-1.5 block">
              Template Name
            </Label>
            <Input
              id="templateName"
              value={templateData.name}
              onChange={handleNameChange}
              placeholder="Enter template name"
              className="max-w-md"
            />
          </div>

          <div>
            <Label className="text-sm font-medium mb-1.5 block">
              Business Unit
            </Label>
            <div className="bg-muted/20 px-3 py-2 rounded-md max-w-md">
              {templateData.businessUnit}
            </div>
          </div>
        </div>
      </Card>

      <Card className="overflow-hidden shadow-md border-primary/20 bg-card">
        <div className="p-4 bg-muted/30 border-b">
          <h2 className="text-lg font-medium text-primary">Core Competency Skills</h2>
          <p className="text-sm text-muted-foreground mt-1">
            These are the foundational skills that all engineers in this business unit should be assessed on. Focus on core competencies rather than project-specific tools.
          </p>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-muted/50 border-b">
                <th className="px-4 py-3 text-left font-medium text-primary/90">Skill Category</th>
                <th className="px-4 py-3 text-left font-medium text-primary/90">Description</th>
                <th className="px-4 py-3 text-center font-medium text-primary/90">CL2</th>
                <th className="px-4 py-3 text-center font-medium text-primary/90">CL3</th>
                <th className="px-4 py-3 text-center font-medium text-primary/90">CL4</th>
                <th className="px-4 py-3 text-center font-medium text-primary/90">CL5</th>
                <th className="px-4 py-3 text-center font-medium text-primary/90">CL6</th>
                <th className="px-4 py-3 text-center font-medium text-primary/90 w-16">Actions</th>
              </tr>
            </thead>
            <tbody>
              {templateData.coreSkills.map((skill: any, index: number) => (
                <tr key={skill.id || index} className="border-b hover:bg-muted/10 transition-colors">
                  <td className="px-4 py-4">
                    <Input
                      value={skill.category}
                      onChange={(e) => handleSkillChange(index, "category", e.target.value)}
                      className="w-full"
                    />
                  </td>
                  <td className="px-4 py-4">
                    <div>
                      <Input
                        value={skill.description}
                        onChange={(e) => handleSkillChange(index, "description", e.target.value.slice(0, 200))}
                        className="w-full"
                        maxLength={200}
                      />
                      <div className="text-xs text-right mt-1 text-muted-foreground">
                        {skill.description.length}/200
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-4 text-center">
                    <Input
                      type="number"
                      min="1"
                      max="6"
                      value={skill.targetCL2}
                      onChange={(e) => handleSkillChange(index, "targetCL2", parseInt(e.target.value))}
                      className="w-16 mx-auto text-center"
                    />
                  </td>
                  <td className="px-4 py-4 text-center">
                    <Input
                      type="number"
                      min="1"
                      max="6"
                      value={skill.targetCL3}
                      onChange={(e) => handleSkillChange(index, "targetCL3", parseInt(e.target.value))}
                      className="w-16 mx-auto text-center"
                    />
                  </td>
                  <td className="px-4 py-4 text-center">
                    <Input
                      type="number"
                      min="1"
                      max="6"
                      value={skill.targetCL4}
                      onChange={(e) => handleSkillChange(index, "targetCL4", parseInt(e.target.value))}
                      className="w-16 mx-auto text-center"
                    />
                  </td>
                  <td className="px-4 py-4 text-center">
                    <Input
                      type="number"
                      min="1"
                      max="6"
                      value={skill.targetCL5}
                      onChange={(e) => handleSkillChange(index, "targetCL5", parseInt(e.target.value))}
                      className="w-16 mx-auto text-center"
                    />
                  </td>
                  <td className="px-4 py-4 text-center">
                    <Input
                      type="number"
                      min="1"
                      max="6"
                      value={skill.targetCL6}
                      onChange={(e) => handleSkillChange(index, "targetCL6", parseInt(e.target.value))}
                      className="w-16 mx-auto text-center"
                    />
                  </td>
                  <td className="px-4 py-4 text-center">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveSkill(index)}
                      className="text-destructive hover:text-destructive/80 hover:bg-destructive/10"
                    >
                      <Trash className="h-4 w-4" />
                      <span className="sr-only">Remove</span>
                    </Button>
                  </td>
                </tr>
              ))}

              {/* Add new skill row */}
              <tr className="border-b bg-muted/5">
                <td className="px-4 py-4">
                  <Input
                    value={newSkill.category}
                    onChange={(e) => setNewSkill({ ...newSkill, category: e.target.value })}
                    placeholder="New skill category"
                    className="w-full"
                  />
                </td>
                <td className="px-4 py-4">
                  <div>
                    <Input
                      value={newSkill.description}
                      onChange={(e) => setNewSkill({ ...newSkill, description: e.target.value.slice(0, 200) })}
                      placeholder="Skill description"
                      className="w-full"
                      maxLength={200}
                    />
                    <div className="text-xs text-right mt-1 text-muted-foreground">
                      {newSkill.description.length}/200
                    </div>
                  </div>
                </td>
                <td className="px-4 py-4 text-center">
                  <Input
                    type="number"
                    min="1"
                    max="6"
                    value={newSkill.targetCL2}
                    onChange={(e) => setNewSkill({ ...newSkill, targetCL2: parseInt(e.target.value) })}
                    className="w-16 mx-auto text-center"
                  />
                </td>
                <td className="px-4 py-4 text-center">
                  <Input
                    type="number"
                    min="1"
                    max="6"
                    value={newSkill.targetCL3}
                    onChange={(e) => setNewSkill({ ...newSkill, targetCL3: parseInt(e.target.value) })}
                    className="w-16 mx-auto text-center"
                  />
                </td>
                <td className="px-4 py-4 text-center">
                  <Input
                    type="number"
                    min="1"
                    max="6"
                    value={newSkill.targetCL4}
                    onChange={(e) => setNewSkill({ ...newSkill, targetCL4: parseInt(e.target.value) })}
                    className="w-16 mx-auto text-center"
                  />
                </td>
                <td className="px-4 py-4 text-center">
                  <Input
                    type="number"
                    min="1"
                    max="6"
                    value={newSkill.targetCL5}
                    onChange={(e) => setNewSkill({ ...newSkill, targetCL5: parseInt(e.target.value) })}
                    className="w-16 mx-auto text-center"
                  />
                </td>
                <td className="px-4 py-4 text-center">
                  <Input
                    type="number"
                    min="1"
                    max="6"
                    value={newSkill.targetCL6}
                    onChange={(e) => setNewSkill({ ...newSkill, targetCL6: parseInt(e.target.value) })}
                    className="w-16 mx-auto text-center"
                  />
                </td>
                <td className="px-4 py-4 text-center">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleAddSkill()}
                    disabled={!newSkill.category.trim()}
                    className="text-primary hover:text-primary/80 hover:bg-primary/10"
                  >
                    <Plus className="h-4 w-4" />
                    <span className="sr-only">Add</span>
                  </Button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </Card>

      <div className="flex justify-between items-center pt-4">
        <div className="text-sm text-muted-foreground">
          <div className="p-3 bg-muted/20 rounded-lg">
            <h4 className="font-medium mb-2 text-primary">Rating Scale Reference</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3 text-sm">
              {Object.entries(ratingScale).map(([level, description]) => {
                // Define a color for each level with better contrast
                const levelColors = {
                  '1': 'bg-red-700',
                  '2': 'bg-orange-600',
                  '3': 'bg-amber-500',
                  '4': 'bg-green-600',
                  '5': 'bg-blue-600',
                  '6': 'bg-purple-700'
                };
                const bgColor = levelColors[level as keyof typeof levelColors] || 'bg-primary';

                return (
                  <div key={level} className="flex items-center gap-2">
                    <div className={`w-6 h-6 rounded-full ${bgColor} flex items-center justify-center text-white text-xs font-medium shadow-md border border-white/20`}>{level}</div>
                    <span className="font-medium">{description}</span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        <Button
          onClick={handleSaveTemplate}
          disabled={isSaving || !templateData.name.trim()}
          className="bg-primary hover:bg-blue-500 shadow-md border border-primary/20 font-medium transition-colors duration-200"
        >
          <Save className="mr-2 h-4 w-4" />
          {isSaving ? (
            <span className="flex items-center gap-2">
              <div className="h-4 w-4 rounded-full border-2 border-white/30 border-t-white animate-spin"></div>
              Saving...
            </span>
          ) : (
            "Save Template"
          )}
        </Button>
      </div>
    </div >
  )
}
