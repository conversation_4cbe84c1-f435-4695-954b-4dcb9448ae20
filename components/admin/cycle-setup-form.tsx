"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  ArrowLeft,
  Users,
  UserCheck,
  UserPlus,
  Play,
  Search,
  Check,
  X
} from "lucide-react"
import {
  SerializedAssessmentCycle,
  CycleTeamMember,
  PeerReviewer,
  AssessmentStatus
} from "@/lib/models/assessment-cycle"
import {
  assignTeamMembers,
  assignManagers,
  assignPeerReviewers,
  activateAssessmentCycle
} from "@/lib/actions/assessment-cycles"
import Link from "next/link"

interface CycleSetupFormProps {
  cycle: SerializedAssessmentCycle
  users: any[]
  cycleId: string
}

export default function CycleSetupForm({ cycle, users, cycleId }: CycleSetupFormProps) {
  const router = useRouter()
  const { data: session } = useSession()
  const [activeTab, setActiveTab] = useState("team-members")
  const [message, setMessage] = useState<{ text: string; type: "success" | "error" } | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  // Team members state
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [teamMembers, setTeamMembers] = useState<CycleTeamMember[]>(cycle.teamMembers || [])

  // Manager assignments state
  const [managerAssignments, setManagerAssignments] = useState<Record<string, { userId: string; name: string; email: string }>>({})

  // Peer reviewer assignments state
  const [peerAssignments, setPeerAssignments] = useState<Record<string, PeerReviewer[]>>({})
  const [autoAssignPeers, setAutoAssignPeers] = useState(false)

  // Initialize manager assignments from existing data
  useEffect(() => {
    const initialManagerAssignments: Record<string, { userId: string; name: string; email: string }> = {};

    cycle.teamMembers.forEach(member => {
      if (member.assessments.manager && member.assessments.manager.userId) {
        initialManagerAssignments[member.userId] = {
          userId: member.assessments.manager.userId,
          name: member.assessments.manager.name,
          email: member.assessments.manager.email
        };
      }
    });

    setManagerAssignments(initialManagerAssignments);

    // Initialize peer assignments
    const initialPeerAssignments: Record<string, PeerReviewer[]> = {};

    cycle.teamMembers.forEach(member => {
      if (member.assessments.peers && member.assessments.peers.length > 0) {
        initialPeerAssignments[member.userId] = member.assessments.peers;
      }
    });

    setPeerAssignments(initialPeerAssignments);
  }, [cycle]);

  // Filter users based on search query
  const filteredUsers = searchQuery.trim() === "" ? users : users.filter(user => {
    const searchLower = searchQuery.toLowerCase();
    return (
      (user.name && user.name.toLowerCase().includes(searchLower)) ||
      (user.email && user.email.toLowerCase().includes(searchLower)) ||
      (user.businessUnit && user.businessUnit.toLowerCase().includes(searchLower)) ||
      (user.careerLevel && user.careerLevel.toLowerCase().includes(searchLower)) ||
      (user.jobRole && user.jobRole.toLowerCase().includes(searchLower))
    );
  });

  // Handle team member selection
  const handleUserSelection = (userId: string) => {
    if (selectedUsers.includes(userId)) {
      setSelectedUsers(selectedUsers.filter(id => id !== userId));
    } else {
      setSelectedUsers([...selectedUsers, userId]);
    }
  };

  // Handle team member assignment
  const handleAssignTeamMembers = async () => {
    if (!session?.user?.email) {
      setMessage({ text: "You must be signed in to assign team members", type: "error" });
      return;
    }

    setIsLoading(true);

    // Create team member objects for the selected users
    const newTeamMembers: CycleTeamMember[] = selectedUsers.map(userId => {
      const user = users.find(u => u.id === userId);

      // Check if this user is already a team member
      const existingMember = teamMembers.find(m => m.userId === userId);
      if (existingMember) {
        return existingMember;
      }

      return {
        userId: user.id,
        name: user.name,
        email: user.email,
        businessUnit: user.businessUnit || "",
        careerLevel: user.careerLevel || "",
        assessments: {
          self: "pending" as AssessmentStatus,
          manager: {
            userId: "",
            name: "",
            email: "",
            status: "pending" as AssessmentStatus
          },
          peers: []
        }
      };
    });

    // Combine with existing team members
    const updatedTeamMembers = [
      ...teamMembers.filter(member => !selectedUsers.includes(member.userId)),
      ...newTeamMembers
    ];

    const result = await assignTeamMembers(cycleId, updatedTeamMembers, session.user.email);

    setIsLoading(false);

    if (result.success) {
      setMessage({ text: result.message || "Team members assigned successfully", type: "success" });
      setTeamMembers(updatedTeamMembers);
      setSelectedUsers([]);

      // Move to the next tab if this is the first time adding team members
      if (teamMembers.length === 0 && newTeamMembers.length > 0) {
        setActiveTab("managers");
      }

      router.refresh();
    } else {
      setMessage({ text: result.message || "Failed to assign team members", type: "error" });
    }
  };

  // Handle manager assignment
  const handleManagerAssignment = (teamMemberId: string, managerId: string) => {
    if (!managerId) {
      // Remove manager assignment
      const newAssignments = { ...managerAssignments };
      delete newAssignments[teamMemberId];
      setManagerAssignments(newAssignments);
      return;
    }

    const manager = users.find(u => u.id === managerId);
    if (manager) {
      setManagerAssignments({
        ...managerAssignments,
        [teamMemberId]: {
          userId: manager.id,
          name: manager.name,
          email: manager.email
        }
      });
    }
  };

  // Handle saving manager assignments
  const handleSaveManagerAssignments = async () => {
    if (!session?.user?.email) {
      setMessage({ text: "You must be signed in to assign managers", type: "error" });
      return;
    }

    setIsLoading(true);

    const result = await assignManagers(cycleId, managerAssignments, session.user.email);

    setIsLoading(false);

    if (result.success) {
      setMessage({ text: result.message || "Managers assigned successfully", type: "success" });

      // Move to the next tab if peer reviews are enabled
      if (cycle.assessmentTypes.peer) {
        setActiveTab("peers");
      }

      router.refresh();
    } else {
      setMessage({ text: result.message || "Failed to assign managers", type: "error" });
    }
  };

  // Handle peer reviewer assignment
  const handlePeerAssignment = (teamMemberId: string, peerIndex: number, peerId: string) => {
    if (!peerId) {
      // Remove peer assignment at this index
      const currentPeers = peerAssignments[teamMemberId] || [];
      const newPeers = [...currentPeers];
      newPeers.splice(peerIndex, 1);

      setPeerAssignments({
        ...peerAssignments,
        [teamMemberId]: newPeers
      });
      return;
    }

    const peer = users.find(u => u.id === peerId);
    if (!peer) return;

    const currentPeers = peerAssignments[teamMemberId] || [];

    // Check if this peer is already assigned to this team member
    if (currentPeers.some(p => p.userId === peerId)) {
      setMessage({ text: "This peer is already assigned to this team member", type: "error" });
      return;
    }

    // Check if this is the team member themselves
    if (peerId === teamMemberId) {
      setMessage({ text: "Team members cannot review themselves", type: "error" });
      return;
    }

    const newPeer: PeerReviewer = {
      userId: peer.id,
      name: peer.name,
      email: peer.email,
      status: "pending" as AssessmentStatus
    };

    // If we're updating an existing peer
    if (peerIndex < currentPeers.length) {
      const newPeers = [...currentPeers];
      newPeers[peerIndex] = newPeer;

      setPeerAssignments({
        ...peerAssignments,
        [teamMemberId]: newPeers
      });
    } else {
      // We're adding a new peer
      setPeerAssignments({
        ...peerAssignments,
        [teamMemberId]: [...currentPeers, newPeer]
      });
    }
  };

  // Auto-assign peers
  const handleAutoAssignPeers = () => {
    if (teamMembers.length < 3) {
      setMessage({ text: "You need at least 3 team members to auto-assign peers", type: "error" });
      return;
    }

    const newPeerAssignments: Record<string, PeerReviewer[]> = {};

    // For each team member, assign random peers
    teamMembers.forEach(member => {
      // Get all potential peers (excluding the team member themselves)
      const potentialPeers = teamMembers.filter(peer => peer.userId !== member.userId);

      // Shuffle the potential peers
      const shuffledPeers = [...potentialPeers].sort(() => Math.random() - 0.5);

      // Take the first N peers based on the cycle configuration
      const selectedPeers = shuffledPeers.slice(0, cycle.assessmentTypes.peerReviewsPerUser);

      // Create peer reviewer objects
      newPeerAssignments[member.userId] = selectedPeers.map(peer => ({
        userId: peer.userId,
        name: peer.name,
        email: peer.email,
        status: "pending" as AssessmentStatus
      }));
    });

    setPeerAssignments(newPeerAssignments);
  };

  // Handle saving peer assignments
  const handleSavePeerAssignments = async () => {
    if (!session?.user?.email) {
      setMessage({ text: "You must be signed in to assign peer reviewers", type: "error" });
      return;
    }

    setIsLoading(true);

    const result = await assignPeerReviewers(cycleId, peerAssignments, session.user.email);

    setIsLoading(false);

    if (result.success) {
      setMessage({ text: result.message || "Peer reviewers assigned successfully", type: "success" });
      router.refresh();
    } else {
      setMessage({ text: result.message || "Failed to assign peer reviewers", type: "error" });
    }
  };

  // Handle activating the cycle
  const handleActivateCycle = async () => {
    if (!session?.user?.email) {
      setMessage({ text: "You must be signed in to activate the cycle", type: "error" });
      return;
    }

    // Validate that we have team members
    if (teamMembers.length === 0) {
      setMessage({ text: "You must assign team members before activating the cycle", type: "error" });
      return;
    }

    // Validate that managers are assigned if manager assessments are enabled
    if (cycle.assessmentTypes.manager) {
      const missingManagers = teamMembers.filter(member =>
        !managerAssignments[member.userId] || !managerAssignments[member.userId].userId
      );

      if (missingManagers.length > 0) {
        setMessage({
          text: `${missingManagers.length} team members are missing manager assignments`,
          type: "error"
        });
        return;
      }
    }

    // Validate that peer reviewers are assigned if peer assessments are enabled
    if (cycle.assessmentTypes.peer) {
      const missingPeers = teamMembers.filter(member =>
        !peerAssignments[member.userId] ||
        peerAssignments[member.userId].length < cycle.assessmentTypes.peerReviewsPerUser
      );

      if (missingPeers.length > 0) {
        setMessage({
          text: `${missingPeers.length} team members have fewer than ${cycle.assessmentTypes.peerReviewsPerUser} peer reviewers assigned`,
          type: "error"
        });
        return;
      }
    }

    setIsLoading(true);

    const result = await activateAssessmentCycle(cycleId, session.user.email);

    setIsLoading(false);

    if (result.success) {
      setMessage({ text: result.message || "Assessment cycle activated successfully", type: "success" });

      // Redirect to the cycle details page
      setTimeout(() => {
        router.push(`/admin/assessments/cycles/${cycleId}`);
        router.refresh();
      }, 1500);
    } else {
      setMessage({ text: result.message || "Failed to activate assessment cycle", type: "error" });
    }
  };

  // Get available managers (all users except the team member)
  const getAvailableManagers = (teamMemberId: string) => {
    return users.filter(user => user.id !== teamMemberId);
  };

  // Get available peer reviewers (all team members except the team member)
  const getAvailablePeers = (teamMemberId: string) => {
    return teamMembers.filter(member => member.userId !== teamMemberId);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Link href="/admin/assessments/cycles">
          <Button variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Cycles
          </Button>
        </Link>

        <Button
          onClick={handleActivateCycle}
          disabled={isLoading || cycle.status !== "draft" || teamMembers.length === 0}
          className="bg-green-600 hover:bg-green-700"
        >
          <Play className="mr-2 h-4 w-4" />
          Activate Cycle
        </Button>
      </div>

      {message && (
        <div
          className={`p-4 rounded-lg border ${message.type === "success" ? "bg-green-50 border-green-200 text-green-800" : "bg-red-50 border-red-200 text-red-800"
            }`}
        >
          {message.text}
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>{cycle.name}</CardTitle>
          <CardDescription>
            Configure team members and reviewers for this assessment cycle
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="team-members" className="flex items-center">
                <Users className="mr-2 h-4 w-4" />
                Team Members
              </TabsTrigger>
              <TabsTrigger
                value="managers"
                className="flex items-center"
                disabled={teamMembers.length === 0 || !cycle.assessmentTypes.manager}
              >
                <UserCheck className="mr-2 h-4 w-4" />
                Managers
              </TabsTrigger>
              <TabsTrigger
                value="peers"
                className="flex items-center"
                disabled={teamMembers.length === 0 || !cycle.assessmentTypes.peer}
              >
                <UserPlus className="mr-2 h-4 w-4" />
                Peer Reviewers
              </TabsTrigger>
            </TabsList>

            <TabsContent value="team-members">
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Assign Team Members</h3>
                  <div className="relative w-64">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search users..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>

                <div className="border rounded-md">
                  <div className="bg-muted/50 p-3 border-b flex justify-between items-center">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="select-all"
                        checked={selectedUsers.length > 0 && selectedUsers.length === filteredUsers.length}
                        onChange={() => {
                          if (selectedUsers.length === filteredUsers.length) {
                            setSelectedUsers([]);
                          } else {
                            setSelectedUsers(filteredUsers.map(user => user.id));
                          }
                        }}
                        className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <Label htmlFor="select-all" className="ml-2 text-sm font-medium">
                        Select All
                      </Label>
                    </div>

                    <Button
                      onClick={handleAssignTeamMembers}
                      disabled={isLoading || selectedUsers.length === 0}
                      size="sm"
                    >
                      Assign Selected
                    </Button>
                  </div>

                  <div className="max-h-96 overflow-y-auto">
                    {users.length === 0 ? (
                      <div className="p-6 text-center text-muted-foreground">
                        No users found in the system. Please add users in the Admin Users section first.
                      </div>
                    ) : filteredUsers.length > 0 ? (
                      filteredUsers.map(user => {
                        const isSelected = selectedUsers.includes(user.id);
                        const isTeamMember = teamMembers.some(member => member.userId === user.id);

                        return (
                          <div
                            key={user.id}
                            className={`flex items-center justify-between p-3 border-b last:border-0 hover:bg-muted/20 ${isTeamMember ? 'bg-blue-50' : ''
                              }`}
                          >
                            <div className="flex items-center">
                              <input
                                type="checkbox"
                                id={`user-${user.id}`}
                                checked={isSelected}
                                onChange={() => handleUserSelection(user.id)}
                                disabled={isTeamMember}
                                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                              />
                              <div className="ml-3">
                                <p className="font-medium">{user.name}</p>
                                <p className="text-sm text-muted-foreground">{user.email}</p>
                              </div>
                            </div>

                            <div className="flex items-center space-x-2">
                              {user.businessUnit && (
                                <Badge variant="outline">{user.businessUnit}</Badge>
                              )}
                              {user.careerLevel && (
                                <Badge variant="outline">{user.careerLevel}</Badge>
                              )}
                              {isTeamMember && (
                                <Badge className="bg-blue-500">Team Member</Badge>
                              )}
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className="p-6 text-center text-muted-foreground">
                        No users found matching your search criteria
                      </div>
                    )}
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-4">Current Team Members</h3>

                  {teamMembers.length > 0 ? (
                    <div className="border rounded-md">
                      <div className="bg-muted/50 p-3 border-b grid grid-cols-4 gap-4">
                        <div className="font-medium text-sm">Name</div>
                        <div className="font-medium text-sm">Email</div>
                        <div className="font-medium text-sm">Business Unit</div>
                        <div className="font-medium text-sm">Career Level</div>
                      </div>

                      <div className="max-h-96 overflow-y-auto">
                        {teamMembers.map(member => (
                          <div key={member.userId} className="grid grid-cols-4 gap-4 p-3 border-b last:border-0 hover:bg-muted/20">
                            <div className="font-medium">{member.name}</div>
                            <div className="text-sm text-muted-foreground">{member.email}</div>
                            <div>{member.businessUnit || "-"}</div>
                            <div>{member.careerLevel || "-"}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="p-6 text-center text-muted-foreground border rounded-md">
                      No team members assigned yet
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="managers">
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Assign Managers</h3>

                  <Button
                    onClick={handleSaveManagerAssignments}
                    disabled={isLoading || teamMembers.length === 0}
                  >
                    Save Manager Assignments
                  </Button>
                </div>

                {teamMembers.length > 0 ? (
                  <div className="border rounded-md">
                    <div className="bg-muted/50 p-3 border-b grid grid-cols-3 gap-4">
                      <div className="font-medium text-sm">Team Member</div>
                      <div className="font-medium text-sm">Manager</div>
                      <div className="font-medium text-sm">Status</div>
                    </div>

                    <div className="max-h-96 overflow-y-auto">
                      {teamMembers.map(member => (
                        <div key={member.userId} className="grid grid-cols-3 gap-4 p-3 border-b last:border-0 hover:bg-muted/20">
                          <div>
                            <p className="font-medium">{member.name}</p>
                            <p className="text-sm text-muted-foreground">{member.email}</p>
                          </div>

                          <div>
                            <select
                              value={managerAssignments[member.userId]?.userId || ""}
                              onChange={(e) => handleManagerAssignment(member.userId, e.target.value)}
                              className="w-full p-2 border rounded-md"
                            >
                              <option value="">Select a manager</option>
                              {getAvailableManagers(member.userId).map(manager => (
                                <option key={manager.id} value={manager.id}>
                                  {manager.name} ({manager.email})
                                </option>
                              ))}
                            </select>
                          </div>

                          <div>
                            {managerAssignments[member.userId]?.userId ? (
                              <Badge className="bg-green-500">
                                <Check className="mr-1 h-3 w-3" />
                                Assigned
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="text-red-500 border-red-200">
                                <X className="mr-1 h-3 w-3" />
                                Not Assigned
                              </Badge>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="p-6 text-center text-muted-foreground border rounded-md">
                    No team members assigned yet
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="peers">
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Assign Peer Reviewers</h3>

                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      onClick={handleAutoAssignPeers}
                      disabled={isLoading || teamMembers.length < 3}
                    >
                      Auto-Assign Peers
                    </Button>

                    <Button
                      onClick={handleSavePeerAssignments}
                      disabled={isLoading || teamMembers.length === 0}
                    >
                      Save Peer Assignments
                    </Button>
                  </div>
                </div>

                {teamMembers.length > 0 ? (
                  <div className="space-y-6">
                    {teamMembers.map(member => {
                      const currentPeers = peerAssignments[member.userId] || [];
                      const peerSlots = Array.from({ length: cycle.assessmentTypes.peerReviewsPerUser });

                      return (
                        <Card key={member.userId} className="overflow-hidden">
                          <CardHeader className="bg-muted/20 py-3">
                            <CardTitle className="text-base">{member.name}</CardTitle>
                            <CardDescription>{member.email}</CardDescription>
                          </CardHeader>
                          <CardContent className="p-4">
                            <div className="space-y-3">
                              <Label className="text-sm font-medium">Peer Reviewers</Label>

                              {peerSlots.map((_, index) => (
                                <div key={index} className="flex items-center space-x-2">
                                  <select
                                    value={currentPeers[index]?.userId || ""}
                                    onChange={(e) => handlePeerAssignment(member.userId, index, e.target.value)}
                                    className="w-full p-2 border rounded-md"
                                  >
                                    <option value="">Select a peer reviewer</option>
                                    {getAvailablePeers(member.userId).map(peer => (
                                      <option
                                        key={peer.userId}
                                        value={peer.userId}
                                        disabled={currentPeers.some(p => p.userId === peer.userId && p !== currentPeers[index])}
                                      >
                                        {peer.name} ({peer.email})
                                      </option>
                                    ))}
                                  </select>

                                  {currentPeers[index]?.userId ? (
                                    <Badge className="bg-green-500 whitespace-nowrap">
                                      <Check className="mr-1 h-3 w-3" />
                                      Assigned
                                    </Badge>
                                  ) : (
                                    <Badge variant="outline" className="text-red-500 border-red-200 whitespace-nowrap">
                                      <X className="mr-1 h-3 w-3" />
                                      Not Assigned
                                    </Badge>
                                  )}
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                ) : (
                  <div className="p-6 text-center text-muted-foreground border rounded-md">
                    No team members assigned yet
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
