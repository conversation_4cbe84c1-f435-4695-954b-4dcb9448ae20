"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON>, FileText, TrendingUp, TrendingDown, UserCheck, UserX, UserPlus, Database } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import { AdminDashboardData } from "@/lib/models/admin-dashboard"
import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"

interface AdminDashboardStatsProps {
  data: AdminDashboardData | undefined
}

export default function AdminDashboardStats({ data }: AdminDashboardStatsProps) {
  const [mounted, setMounted] = useState(false);
  const [avgSkillLevel, setAvgSkillLevel] = useState(0);

  useEffect(() => {
    setMounted(true);

    if (data) {
      // Calculate average skill level across all skills
      const skillValues = Object.values(data.skillAverages || {}) as number[];
      const avgLevel = skillValues.length > 0
        ? skillValues.reduce((sum: number, val: number) => sum + val, 0) / skillValues.length
        : 0;
      setAvgSkillLevel(avgLevel);
    }
  }, [data]);

  // Loading state
  if (!mounted || !data) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-5 w-32" />
                <Skeleton className="h-4 w-4 rounded-full" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Helper function to format skill display
  const formatSkillDisplay = (skill: any) => {
    if (!skill) return "-";

    // Get the first word of the category or the full category if it's short
    const displayName = skill.category.length > 12
      ? skill.category.split(' ')[0]
      : skill.category;

    return `${displayName} (${skill.average.toFixed(1)})`;
  };

  return (
    <Tabs defaultValue="overview" className="space-y-4">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="users">User Management</TabsTrigger>
      </TabsList>

      <TabsContent value="overview" className="space-y-0">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.totalUsers || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Assessments</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.totalAssessments || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Top Skill</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatSkillDisplay(data.topSkills?.[0])}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Improvement Area</CardTitle>
              <TrendingDown className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatSkillDisplay(data.improvementAreas?.[0])}
              </div>
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="users" className="space-y-0">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Users</CardTitle>
              <UserCheck className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.activeUsers || 0}</div>
              <p className="text-xs text-muted-foreground mt-1">
                {data.totalUsers ? Math.round((data.activeUsers / data.totalUsers) * 100) : 0}% of total users
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Inactive Users</CardTitle>
              <UserX className="h-4 w-4 text-amber-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.inactiveUsers || 0}</div>
              <p className="text-xs text-muted-foreground mt-1">
                {data.totalUsers ? Math.round((data.inactiveUsers / data.totalUsers) * 100) : 0}% of total users
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Admin Created</CardTitle>
              <Database className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.adminCreatedUsers || 0}</div>
              <p className="text-xs text-muted-foreground mt-1">
                {data.totalUsers ? Math.round((data.adminCreatedUsers / data.totalUsers) * 100) : 0}% of total users
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Self Registered</CardTitle>
              <UserPlus className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.selfRegisteredUsers || 0}</div>
              <p className="text-xs text-muted-foreground mt-1">
                {data.totalUsers ? Math.round((data.selfRegisteredUsers / data.totalUsers) * 100) : 0}% of total users
              </p>
            </CardContent>
          </Card>
        </div>
      </TabsContent>
    </Tabs>
  )
}
