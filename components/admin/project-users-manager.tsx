"use client"

import { useState, use<PERSON><PERSON>back, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DataTable, Column } from "@/components/ui/data-table"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2, Plus, Trash2 } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { getUsersInProject, assignUsersToProject, removeUsersFromProject } from "@/lib/actions/projects"
import { getAllUserProfiles } from "@/lib/actions/admin-users"
import { Checkbox } from "@/components/ui/checkbox"
import { Skeleton } from "@/components/ui/skeleton"
import { useDebounce } from "@/lib/hooks/use-debounce"

interface ProjectUsersManagerProps {
  adminEmail: string
  projectId: string
  projectName: string
  initialUsers: any
}

export default function ProjectUsersManager({
  adminEmail,
  projectId,
  projectName,
  initialUsers,
}: ProjectUsersManagerProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ text: string; type: 'success' | 'error' } | null>(null)
  const [users, setUsers] = useState<any[]>(initialUsers?.data || [])
  const [totalItems, setTotalItems] = useState<number>(initialUsers?.totalItems || 0)
  const [currentPage, setCurrentPage] = useState<number>(initialUsers?.currentPage || 1)
  const [pageSize] = useState<number>(10)
  const [search, setSearch] = useState<string>("")
  const debouncedSearch = useDebounce(search, 500)

  // State for user selection dialog
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [availableUsers, setAvailableUsers] = useState<any[]>([])
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([])
  const [dialogLoading, setDialogLoading] = useState(false)
  const [dialogSearch, setDialogSearch] = useState<string>("")
  const debouncedDialogSearch = useDebounce(dialogSearch, 500)
  const [dialogPage, setDialogPage] = useState<number>(1)
  const [dialogTotalItems, setDialogTotalItems] = useState<number>(0)

  // Define columns for the users table
  const columns: Column<any>[] = [
    {
      header: "Name",
      accessorKey: "name",
    },
    {
      header: "Email",
      accessorKey: "email",
    },
    {
      header: "Business Unit",
      accessorKey: "businessUnit",
      cell: ({ row }) => (
        <Badge variant="outline" className="capitalize">
          {row.original.businessUnit}
        </Badge>
      ),
    },
    {
      header: "Career Level",
      accessorKey: "careerLevel",
      cell: ({ row }) => (
        <Badge variant="outline" className="uppercase">
          {row.original.careerLevel}
        </Badge>
      ),
    },
    {
      header: "Actions",
      id: "actions",
      cell: ({ row }) => (
        <Button
          variant="ghost"
          size="icon"
          onClick={() => handleRemoveUser(row.original.id)}
          title="Remove User"
        >
          <Trash2 className="h-4 w-4 text-red-500" />
        </Button>
      ),
    },
  ];

  // Function to fetch users in project with pagination and search
  const fetchProjectUsers = useCallback(
    async (page: number, search: string) => {
      setIsLoading(true)
      try {
        const result = await getUsersInProject(projectId, adminEmail, page, pageSize, search)
        if (result.success) {
          setUsers(result.data)
          setTotalItems(result.totalItems)
          setCurrentPage(result.currentPage)
        }
      } catch (error) {
        console.error("Error fetching project users:", error)
      } finally {
        setIsLoading(false)
      }
    },
    [projectId, adminEmail, pageSize]
  )

  // Function to fetch available users for assignment
  const fetchAvailableUsers = useCallback(
    async (page: number, search: string) => {
      setDialogLoading(true)
      try {
        const result = await getAllUserProfiles(adminEmail, page, pageSize, search)
        if (result.success) {
          setAvailableUsers(result.data)
          setDialogTotalItems(result.totalItems)
          setDialogPage(result.currentPage)
        }
      } catch (error) {
        console.error("Error fetching available users:", error)
      } finally {
        setDialogLoading(false)
      }
    },
    [adminEmail, pageSize]
  )

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearch(value)
    setCurrentPage(1) // Reset to first page on search
  }

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchProjectUsers(page, debouncedSearch)
  }

  // Handle dialog search input change
  const handleDialogSearchChange = (value: string) => {
    setDialogSearch(value)
    setDialogPage(1) // Reset to first page on search
  }

  // Handle dialog page change
  const handleDialogPageChange = (page: number) => {
    setDialogPage(page)
    fetchAvailableUsers(page, debouncedDialogSearch)
  }

  // Handle user selection in dialog
  const handleUserSelection = (userId: string) => {
    setSelectedUserIds((prev) => {
      if (prev.includes(userId)) {
        return prev.filter((id) => id !== userId)
      } else {
        return [...prev, userId]
      }
    })
  }

  // Handle assigning selected users to project
  const handleAssignUsers = async () => {
    if (selectedUserIds.length === 0) {
      setMessage({ text: "No users selected", type: "error" })
      return
    }

    setDialogLoading(true)
    try {
      const result = await assignUsersToProject(projectId, selectedUserIds, adminEmail)
      if (result.success) {
        setMessage({ text: result.message, type: "success" })
        setIsDialogOpen(false)
        setSelectedUserIds([])
        // Refresh the users list
        fetchProjectUsers(1, "")
        router.refresh()
      } else {
        setMessage({ text: result.message || "An error occurred", type: "error" })
      }
    } catch (error) {
      console.error("Error assigning users:", error)
      setMessage({ text: "An unexpected error occurred", type: "error" })
    } finally {
      setDialogLoading(false)
    }
  }

  // Handle removing a user from the project
  const handleRemoveUser = async (userId: string) => {
    setIsLoading(true)
    try {
      const result = await removeUsersFromProject(projectId, [userId], adminEmail)
      if (result.success) {
        setMessage({ text: result.message, type: "success" })
        // Refresh the users list
        fetchProjectUsers(currentPage, debouncedSearch)
        router.refresh()
      } else {
        setMessage({ text: result.message || "An error occurred", type: "error" })
      }
    } catch (error) {
      console.error("Error removing user:", error)
      setMessage({ text: "An unexpected error occurred", type: "error" })
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch data when debounced search changes
  useEffect(() => {
    // Only fetch if search is defined and not empty
    if (debouncedSearch !== undefined && debouncedSearch !== "") {
      setCurrentPage(1) // Reset to first page on search
      fetchProjectUsers(1, debouncedSearch)
    }
  }, [debouncedSearch, fetchProjectUsers])

  // Fetch dialog data when debounced dialog search changes
  useEffect(() => {
    // Only fetch if dialog search is defined and not empty
    if (debouncedDialogSearch !== undefined && isDialogOpen) {
      setDialogPage(1) // Reset to first page on search
      fetchAvailableUsers(1, debouncedDialogSearch)
    }
  }, [debouncedDialogSearch, fetchAvailableUsers, isDialogOpen])

  // Initial data loading - only run once on mount
  useEffect(() => {
    // Load initial data on component mount
    fetchProjectUsers(1, "")
  }, [fetchProjectUsers])

  // Open dialog and fetch available users
  const openAssignDialog = () => {
    setSelectedUserIds([])
    setDialogSearch("")
    setDialogPage(1)
    setIsDialogOpen(true)
    fetchAvailableUsers(1, "")
  }

  return (
    <div className="space-y-6">
      {message && (
        <Alert className={`mb-6 ${message.type === 'success' ? 'bg-green-50 text-green-800 border-green-200' : 'bg-red-50 text-red-800 border-red-200'}`}>
          <div className="flex items-center gap-2">
            {message.type === 'success' ? (
              <CheckCircle2 className="h-4 w-4 text-green-600" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-600" />
            )}
            <AlertDescription>{message.text}</AlertDescription>
          </div>
        </Alert>
      )}

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Team Members Assigned to Project</CardTitle>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" onClick={openAssignDialog}>
                <Plus className="mr-2 h-4 w-4" />
                Assign Team Members
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Assign Team Members to Project</DialogTitle>
              </DialogHeader>
              <div className="py-4">
                <DataTable
                  columns={[
                    {
                      header: "Select",
                      id: "select",
                      cell: ({ row }) => (
                        <Checkbox
                          checked={selectedUserIds.includes(row.original.id)}
                          onCheckedChange={() => handleUserSelection(row.original.id)}
                        />
                      ),
                    },
                    {
                      header: "Name",
                      accessorKey: "name",
                    },
                    {
                      header: "Email",
                      accessorKey: "email",
                    },
                    {
                      header: "Business Unit",
                      accessorKey: "businessUnit",
                      cell: ({ row }) => (
                        <Badge variant="outline" className="capitalize">
                          {row.original.businessUnit}
                        </Badge>
                      ),
                    },
                    {
                      header: "Career Level",
                      accessorKey: "careerLevel",
                      cell: ({ row }) => (
                        <Badge variant="outline" className="uppercase">
                          {row.original.careerLevel}
                        </Badge>
                      ),
                    },
                  ]}
                  data={availableUsers}
                  onSearch={handleDialogSearchChange}
                  searchPlaceholder="Search users..."
                  serverSide={true}
                  currentPage={dialogPage}
                  totalItems={dialogTotalItems}
                  pageSize={pageSize}
                  onPageChange={handleDialogPageChange}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                  disabled={dialogLoading}
                >
                  Cancel
                </Button>
                <Button onClick={handleAssignUsers} disabled={dialogLoading || selectedUserIds.length === 0}>
                  {dialogLoading ? "Assigning..." : `Assign ${selectedUserIds.length} Team Members`}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-full" />
                </div>
              ))}
            </div>
          ) : (
            <DataTable
              columns={columns}
              data={users}
              onSearch={handleSearchChange}
              searchPlaceholder="Search users..."
              serverSide={true}
              currentPage={currentPage}
              totalItems={totalItems}
              pageSize={pageSize}
              onPageChange={handlePageChange}
              emptyMessage={`No team members assigned to ${projectName}`}
            />
          )}
        </CardContent>
      </Card>
    </div>
  )
}
