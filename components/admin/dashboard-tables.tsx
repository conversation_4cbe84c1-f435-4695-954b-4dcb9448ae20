"use client"

import {
  <PERSON>,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { TrendingUp, TrendingDown } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import { AdminDashboardData } from "@/lib/models/admin-dashboard"
import { useState, useEffect } from "react"
import { skillsData } from "@/lib/skills-data"

interface AdminDashboardTablesProps {
  data: AdminDashboardData | undefined
}

interface SkillTableItem {
  id: string;
  category: string;
  average: number;
  status: 'strong' | 'improvement';
}

export default function AdminDashboardTables({ data }: AdminDashboardTablesProps) {
  const [mounted, setMounted] = useState(false);
  const [topSkills, setTopSkills] = useState<SkillTableItem[]>([]);
  const [improvementAreas, setImprovementAreas] = useState<SkillTableItem[]>([]);

  useEffect(() => {
    setMounted(true);

    if (data) {
      // Process top skills data
      if (data.topSkills && data.topSkills.length > 0) {
        const processedTopSkills = data.topSkills.map(skill => ({
          id: skill.id,
          category: skill.category,
          average: skill.average,
          status: 'strong' as const
        }));
        setTopSkills(processedTopSkills);
      }

      // Process improvement areas data
      if (data.improvementAreas && data.improvementAreas.length > 0) {
        const processedImprovementAreas = data.improvementAreas.map(skill => ({
          id: skill.id,
          category: skill.category,
          average: skill.average,
          status: 'improvement' as const
        }));
        setImprovementAreas(processedImprovementAreas);
      }
    }
  }, [data]);

  // Loading state
  if (!mounted || !data) {
    return (
      <div className="space-y-8">
        {[1, 2].map((section) => (
          <div key={section}>
            <Skeleton className="h-7 w-40 mb-4" />
            <div className="border rounded-md">
              <div className="p-4 border-b bg-muted/50">
                <div className="flex">
                  {[1, 2, 3, 4].map((col) => (
                    <Skeleton key={col} className="h-5 w-24 mr-8" />
                  ))}
                </div>
              </div>
              <div className="p-4">
                {[1, 2, 3, 4, 5].map((row) => (
                  <div key={row} className="flex items-center py-2 border-b last:border-0">
                    {[1, 2, 3, 4].map((col) => (
                      <Skeleton key={col} className="h-5 w-24 mr-8" />
                    ))}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Helper function to render skill status
  const renderSkillStatus = (status: 'strong' | 'improvement') => {
    if (status === 'strong') {
      return (
        <div className="flex items-center">
          <TrendingUp className="mr-2 h-4 w-4 text-green-600" />
          <span className="text-green-600">Strong</span>
        </div>
      );
    }

    return (
      <div className="flex items-center">
        <TrendingDown className="mr-2 h-4 w-4 text-red-600" />
        <span className="text-red-600">Needs Improvement</span>
      </div>
    );
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-medium mb-4">Top Skills</h3>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Rank</TableHead>
              <TableHead>Skill</TableHead>
              <TableHead>Average Level</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {topSkills.length > 0 ? (
              topSkills.map((skill, index) => (
                <TableRow key={skill.id}>
                  <TableCell className="font-medium">{index + 1}</TableCell>
                  <TableCell>{skill.category}</TableCell>
                  <TableCell>{skill.average.toFixed(1)}</TableCell>
                  <TableCell>{renderSkillStatus(skill.status)}</TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-4 text-muted-foreground">
                  No data available
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-4">Improvement Areas</h3>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Rank</TableHead>
              <TableHead>Skill</TableHead>
              <TableHead>Average Level</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {improvementAreas.length > 0 ? (
              improvementAreas.map((skill, index) => (
                <TableRow key={skill.id}>
                  <TableCell className="font-medium">{index + 1}</TableCell>
                  <TableCell>{skill.category}</TableCell>
                  <TableCell>{skill.average.toFixed(1)}</TableCell>
                  <TableCell>{renderSkillStatus(skill.status)}</TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-4 text-muted-foreground">
                  No data available
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
