"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  BarChart3,
  Users,
  FileText,
  Settings,
  Home,
  LogOut,
  Layers,
  Calendar,
  FolderKanban
} from "lucide-react"
import { signOut } from "next-auth/react"
import { cn } from "@/lib/utils"

const navItems = [
  {
    name: "Dashboard",
    href: "/admin",
    icon: BarChart3,
  },
  {
    name: "Users List",
    href: "/admin/users",
    icon: Users,
  },
  {
    name: "Projects",
    href: "/admin/projects",
    icon: FolderKanban,
  },
  {
    name: "Assessments",
    href: "/admin/assessments",
    icon: FileText,
  },
  {
    name: "Assessment Cycles",
    href: "/admin/assessments/cycles",
    icon: Calendar,
  },
  {
    name: "Core Skills Templates",
    href: "/admin/skills",
    icon: Layers,
  },
  {
    name: "Settings",
    href: "/admin/settings",
    icon: Settings,
  },
]

export default function AdminSidebar() {
  const pathname = usePathname()

  return (
    <div className="w-64 bg-slate-800 text-white min-h-screen p-4">
      <div className="mb-8">
        <h1 className="text-xl font-bold">Admin Dashboard</h1>
      </div>

      <nav className="space-y-1">
        <Link
          href="/"
          className="flex items-center px-4 py-2 text-sm rounded-md hover:bg-slate-700"
        >
          <Home className="mr-3 h-4 w-4" />
          Back to App
        </Link>

        <div className="pt-4 pb-2">
          <div className="border-t border-slate-700"></div>
        </div>

        {navItems.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "flex items-center px-4 py-2 text-sm rounded-md hover:bg-slate-700",
              pathname === item.href && "bg-slate-700 font-medium"
            )}
          >
            <item.icon className="mr-3 h-4 w-4" />
            {item.name}
          </Link>
        ))}

        <div className="pt-4 pb-2">
          <div className="border-t border-slate-700"></div>
        </div>

        <button
          onClick={() => signOut({ callbackUrl: "/" })}
          className="flex w-full items-center px-4 py-2 text-sm rounded-md hover:bg-slate-700 text-left"
        >
          <LogOut className="mr-3 h-4 w-4" />
          Sign Out
        </button>
      </nav>
    </div>
  )
}
