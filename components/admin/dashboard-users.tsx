"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Skeleton } from "@/components/ui/skeleton"
import { AdminDashboardData, UserSummary } from "@/lib/models/admin-dashboard"
import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Award, Clock, User, Briefcase, BarChart } from "lucide-react"

interface AdminDashboardUsersProps {
  data: AdminDashboardData | undefined
}

export default function AdminDashboardUsers({ data }: AdminDashboardUsersProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Loading state
  if (!mounted || !data) {
    return (
      <div className="space-y-8">
        {[1, 2].map((section) => (
          <div key={section}>
            <Skeleton className="h-7 w-40 mb-4" />
            <div className="border rounded-md">
              <div className="p-4 border-b bg-muted/50">
                <div className="flex">
                  {[1, 2, 3, 4].map((col) => (
                    <Skeleton key={col} className="h-5 w-24 mr-8" />
                  ))}
                </div>
              </div>
              <div className="p-4">
                {[1, 2, 3, 4, 5].map((row) => (
                  <div key={row} className="flex items-center py-2 border-b last:border-0">
                    {[1, 2, 3, 4].map((col) => (
                      <Skeleton key={col} className="h-5 w-24 mr-8" />
                    ))}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Helper function to render skill level badge
  const renderSkillLevelBadge = (level: number) => {
    let color = 'bg-gray-100 text-gray-800';
    
    if (level >= 5) {
      color = 'bg-purple-100 text-purple-800';
    } else if (level >= 4) {
      color = 'bg-blue-100 text-blue-800';
    } else if (level >= 3) {
      color = 'bg-green-100 text-green-800';
    } else if (level >= 2) {
      color = 'bg-yellow-100 text-yellow-800';
    } else {
      color = 'bg-red-100 text-red-800';
    }
    
    return (
      <Badge className={`${color} font-medium`}>
        {level.toFixed(1)}
      </Badge>
    );
  };

  // Helper function to render business unit badge
  const renderBusinessUnitBadge = (businessUnit: string) => {
    const colors: Record<string, string> = {
      'web': 'bg-blue-100 text-blue-800 border-blue-200',
      'mobile': 'bg-green-100 text-green-800 border-green-200',
      'cloud': 'bg-purple-100 text-purple-800 border-purple-200',
      'data': 'bg-amber-100 text-amber-800 border-amber-200',
      'ai': 'bg-red-100 text-red-800 border-red-200',
      'qa': 'bg-teal-100 text-teal-800 border-teal-200'
    };
    
    return (
      <Badge variant="outline" className={colors[businessUnit.toLowerCase()] || 'bg-gray-100 text-gray-800 border-gray-200'}>
        {businessUnit.toUpperCase()}
      </Badge>
    );
  };

  return (
    <div className="space-y-8">
      <Tabs defaultValue="top-performers">
        <TabsList className="mb-4">
          <TabsTrigger value="top-performers">
            <Award className="h-4 w-4 mr-2" />
            Top Performers
          </TabsTrigger>
          <TabsTrigger value="recently-active">
            <Clock className="h-4 w-4 mr-2" />
            Recently Active
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="top-performers">
          <h3 className="text-lg font-medium mb-4">Top Performing Users</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Rank</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Business Unit</TableHead>
                <TableHead>Career Level</TableHead>
                <TableHead>Avg. Skill Level</TableHead>
                <TableHead>Top Skill</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.topUsers.length > 0 ? (
                data.topUsers.map((user, index) => (
                  <TableRow key={user.id}>
                    <TableCell className="font-medium">{index + 1}</TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">{user.name}</span>
                        <span className="text-xs text-muted-foreground">{user.email}</span>
                      </div>
                    </TableCell>
                    <TableCell>{renderBusinessUnitBadge(user.businessUnit)}</TableCell>
                    <TableCell>{user.careerLevel}</TableCell>
                    <TableCell>{renderSkillLevelBadge(user.averageSkillLevel)}</TableCell>
                    <TableCell>
                      {user.topSkill ? (
                        <div className="flex flex-col">
                          <span className="font-medium">{user.topSkill.category}</span>
                          <span className="text-xs text-muted-foreground">
                            Level: {user.topSkill.level.toFixed(1)}
                          </span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">None</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                    No data available
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TabsContent>
        
        <TabsContent value="recently-active">
          <h3 className="text-lg font-medium mb-4">Recently Active Users</h3>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Business Unit</TableHead>
                <TableHead>Career Level</TableHead>
                <TableHead>Avg. Skill Level</TableHead>
                <TableHead>Top Skill</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.recentlyActiveUsers.length > 0 ? (
                data.recentlyActiveUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">{user.name}</span>
                        <span className="text-xs text-muted-foreground">{user.email}</span>
                      </div>
                    </TableCell>
                    <TableCell>{renderBusinessUnitBadge(user.businessUnit)}</TableCell>
                    <TableCell>{user.careerLevel}</TableCell>
                    <TableCell>{renderSkillLevelBadge(user.averageSkillLevel)}</TableCell>
                    <TableCell>
                      {user.topSkill ? (
                        <div className="flex flex-col">
                          <span className="font-medium">{user.topSkill.category}</span>
                          <span className="text-xs text-muted-foreground">
                            Level: {user.topSkill.level.toFixed(1)}
                          </span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">None</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                    No data available
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TabsContent>
      </Tabs>
    </div>
  )
}
