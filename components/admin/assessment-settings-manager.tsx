'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { updateAssessmentSettings } from '@/lib/actions/settings-actions'
import { toast } from '@/components/ui/use-toast'
import { Save, Loader2, RefreshCw } from 'lucide-react'

interface AssessmentSettingsManagerProps {
  initialIntervalMonths: number
  initialAllowUpdates: boolean
}

export default function AssessmentSettingsManager({
  initialIntervalMonths,
  initialAllowUpdates
}: AssessmentSettingsManagerProps) {
  const [intervalMonths, setIntervalMonths] = useState(initialIntervalMonths)
  const [allowUpdates, setAllowUpdates] = useState(initialAllowUpdates)
  const [isSaving, setIsSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  
  // Handle interval months change
  const handleIntervalMonthsChange = (value: string) => {
    const months = parseInt(value, 10)
    if (!isNaN(months) && months >= 0) {
      setIntervalMonths(months)
      setHasChanges(months !== initialIntervalMonths || allowUpdates !== initialAllowUpdates)
    }
  }
  
  // Handle allow updates change
  const handleAllowUpdatesChange = (checked: boolean) => {
    setAllowUpdates(checked)
    setHasChanges(intervalMonths !== initialIntervalMonths || checked !== initialAllowUpdates)
  }
  
  // Save settings
  const handleSaveSettings = async () => {
    setIsSaving(true)
    
    try {
      const result = await updateAssessmentSettings({
        intervalMonths,
        allowUpdates
      })
      
      if (result) {
        toast({
          title: 'Settings saved',
          description: 'Assessment settings have been updated successfully',
        })
        setHasChanges(false)
      } else {
        toast({
          title: 'Failed to save settings',
          description: 'An error occurred while saving the settings',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Error saving settings:', error)
      toast({
        title: 'Failed to save settings',
        description: 'An error occurred while saving the settings',
        variant: 'destructive',
      })
    } finally {
      setIsSaving(false)
    }
  }
  
  // Reset settings
  const handleResetSettings = () => {
    setIntervalMonths(initialIntervalMonths)
    setAllowUpdates(initialAllowUpdates)
    setHasChanges(false)
  }
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Assessment Settings</h3>
        {hasChanges && (
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleResetSettings}
              disabled={isSaving}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleSaveSettings}
              disabled={isSaving}
            >
              {isSaving ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save
            </Button>
          </div>
        )}
      </div>
      
      <div className="space-y-4">
        <div className="grid gap-2">
          <Label htmlFor="intervalMonths">Assessment Interval (Months)</Label>
          <Input
            id="intervalMonths"
            type="number"
            min="0"
            value={intervalMonths}
            onChange={(e) => handleIntervalMonthsChange(e.target.value)}
            disabled={isSaving}
          />
          <p className="text-sm text-muted-foreground">
            Users can only take an assessment once every {intervalMonths} months.
            Set to 0 to disable this restriction.
          </p>
        </div>
        
        <div className="grid gap-2">
          <Label htmlFor="allowUpdates">Allow Assessment Updates</Label>
          <div className="flex items-center space-x-2">
            <Switch
              id="allowUpdates"
              checked={allowUpdates}
              onCheckedChange={handleAllowUpdatesChange}
              disabled={isSaving}
            />
            <Label htmlFor="allowUpdates">
              {allowUpdates ? "Enabled" : "Disabled"}
            </Label>
          </div>
          <p className="text-sm text-muted-foreground">
            When enabled, users can update their existing assessments.
          </p>
        </div>
      </div>
    </div>
  )
}
