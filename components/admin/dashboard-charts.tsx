"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
// Import consolidated chart components
import { SkillBarChart, DistributionPieChart } from "@/components/charts"
import { skillsData } from "@/lib/skills-data"
import { AdminDashboardData } from "@/lib/models/admin-dashboard"
import { useState, useEffect } from "react"

interface AdminDashboardChartsProps {
  data: AdminDashboardData | undefined;
}

interface ChartDataItem {
  name: string;
  value: number;
}

interface SkillChartItem {
  name: string; // Skill name (human readable)
  id: string;   // Skill ID
  average: number;
}

// Colors are now defined in the direct-charts.tsx file

export default function AdminDashboardCharts({ data }: AdminDashboardChartsProps) {
  // State to track if charts are mounted (client-side)
  const [mounted, setMounted] = useState(false);

  // State for chart data
  const [businessUnitData, setBusinessUnitData] = useState<ChartDataItem[]>([]);
  const [careerLevelData, setCareerLevelData] = useState<ChartDataItem[]>([]);
  const [skillAveragesData, setSkillAveragesData] = useState<SkillChartItem[]>([]);

  // Process data when component mounts or data changes
  useEffect(() => {
    setMounted(true);

    if (!data) {
      return;
    }

    // Format business unit data for pie chart
    const buData = Object.entries(data.businessUnitBreakdown || {})
      .map(([name, value]) => ({ name, value }))
      .filter(item => item.value > 0);
    setBusinessUnitData(buData);

    // Format career level data for pie chart
    const clData = Object.entries(data.careerLevelBreakdown || {})
      .map(([name, value]) => ({ name, value }))
      .filter(item => item.value > 0);
    setCareerLevelData(clData);

    // Format skill averages for bar chart with proper names
    const skillData = Object.entries(data.skillAverages || {})
      .map(([id, average]) => {
        // Find the skill name from skillsData
        const skillInfo = skillsData.find(s => s.id === id);
        return {
          id,
          name: skillInfo?.category || id, // Use category name or fallback to ID
          average
        };
      })
      .filter(item => item.average > 0)
      .sort((a, b) => b.average - a.average);
    setSkillAveragesData(skillData);
  }, [data]);

  // Show loading state if not mounted or no data
  if (!mounted || !data) {
    return (
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="col-span-2">
          <CardHeader>
            <CardTitle>Skill Averages</CardTitle>
          </CardHeader>
          <CardContent>
            <Skeleton className="w-full h-80" />
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Business Unit Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <Skeleton className="w-full h-80" />
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Career Level Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <Skeleton className="w-full h-80" />
          </CardContent>
        </Card>
      </div>
    );
  }

  // Helper function to render charts safely
  const renderChart = (chartType: 'bar' | 'pie', data: any[], options: any = {}) => {
    if (!mounted || !data || data.length === 0) {
      return <Skeleton className="w-full h-80" />;
    }

    if (chartType === 'bar') {
      return (
        <SkillBarChart
          data={data.map(item => ({
            id: item.id || '',
            name: item.name || '',
            average: item.average || 0
          }))}
          title={options.title || "Skill Averages"}
        />
      );
    }

    if (chartType === 'pie') {
      return (
        <DistributionPieChart
          data={data.map(item => ({
            name: item.name || '',
            value: item.value || 0
          }))}
          title={options.title || "Distribution"}
        />
      );
    }

    return <div>Unsupported chart type</div>;
  };

  return (
    <div className="grid gap-4 md:grid-cols-2">
      <Card className="col-span-2">
        <CardHeader>
          <CardTitle>Skill Averages</CardTitle>
        </CardHeader>
        <CardContent>
          {renderChart('bar', skillAveragesData, {
            xDataKey: "name",
            barDataKey: "average",
            barName: "Average Skill Level"
          })}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Business Unit Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          {renderChart('pie', businessUnitData)}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Career Level Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          {renderChart('pie', careerLevelData)}
        </CardContent>
      </Card>
    </div>
  )
}
