"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Upload, Loader2, AlertCircle, CheckCircle2, FileText } from "lucide-react"
import { FileUpload } from "@/components/ui/file-upload"
import { importProjectsFromCSV } from "@/lib/actions/projects"

interface ProjectImportFormProps {
  onImportComplete?: () => void
}

export default function ProjectImportForm({ onImportComplete }: ProjectImportFormProps) {
  const { data: session } = useSession()
  const router = useRouter()
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [message, setMessage] = useState<{ text: string; type: "success" | "error" } | null>(null)

  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.onerror = (e) => reject(e)
      reader.readAsText(file)
    })
  }

  const handleFileUpload = async () => {
    if (!session?.user?.email || !selectedFile) {
      return
    }

    setIsUploading(true)
    setMessage(null)

    try {
      // Read file content
      const fileContent = await readFileContent(selectedFile)

      // Import projects
      const result = await importProjectsFromCSV(fileContent, session.user.email)

      if (result.success) {
        setMessage({ text: result.message || "Projects imported successfully", type: "success" })
        setSelectedFile(null)
        router.refresh()
        onImportComplete?.()
      } else {
        setMessage({ text: result.message || "Failed to import projects", type: "error" })
      }
    } catch (error) {
      console.error("Error importing projects from file:", error)
      setMessage({ text: "Failed to process file", type: "error" })
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <div className="space-y-6">
      {message && (
        <Alert className={message.type === "error" ? "border-red-200 bg-red-50" : "border-green-200 bg-green-50"}>
          {message.type === "error" ? (
            <AlertCircle className="h-4 w-4 text-red-600" />
          ) : (
            <CheckCircle2 className="h-4 w-4 text-green-600" />
          )}
          <AlertDescription className={message.type === "error" ? "text-red-800" : "text-green-800"}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      <div className="space-y-4">
        <div className="space-y-2">
          <label className="block text-sm font-medium">
            Upload CSV File
          </label>
          <p className="text-xs text-muted-foreground">
            Upload a CSV file with project data, team members, and skills.
          </p>

          <FileUpload
            accept=".csv"
            maxSize={10}
            onFileSelect={setSelectedFile}
            disabled={isUploading}
          />
        </div>

        <Button
          onClick={handleFileUpload}
          disabled={isUploading || !selectedFile}
          className="w-full"
        >
          {isUploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Importing...
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              Import Projects
            </>
          )}
        </Button>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <h4 className="text-blue-800 font-medium text-sm flex items-center">
          <FileText className="h-4 w-4 mr-2" />
          CSV Requirements
        </h4>
        <ul className="mt-2 space-y-1 text-xs text-blue-700">
          <li>• Required: project_name, business_unit, status</li>
          <li>• Optional: member_email, skill_category, etc.</li>
          <li>• Download template for correct format</li>
        </ul>
      </div>
    </div>
  )
}
