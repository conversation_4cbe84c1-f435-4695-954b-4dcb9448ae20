"use client"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  PlusCircle,
  Calendar,
  Users,
  Eye,
  Play,
  CheckCircle,
  Trash2,
  AlertTriangle
} from "lucide-react"
import { format } from "date-fns"
import { SerializedAssessmentCycle } from "@/lib/models/assessment-cycle"
import { createAssessmentCycle, deleteAssessmentCycle, activateAssessmentCycle, completeAssessmentCycle } from "@/lib/actions/assessment-cycles"
import { useSession } from "next-auth/react"

interface AssessmentCyclesListProps {
  cycles: SerializedAssessmentCycle[]
  error: string | null
}

export default function AssessmentCyclesList({ cycles, error }: AssessmentCyclesListProps) {
  const router = useRouter()
  const { data: session } = useSession()
  const [isCreating, setIsCreating] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isActivating, setIsActivating] = useState(false)
  const [isCompleting, setIsCompleting] = useState(false)
  const [cycleToDelete, setCycleToDelete] = useState<string | null>(null)
  const [cycleToActivate, setCycleToActivate] = useState<string | null>(null)
  const [cycleToComplete, setCycleToComplete] = useState<string | null>(null)
  const [message, setMessage] = useState<{ text: string; type: "success" | "error" } | null>(null)

  // New cycle form state
  const [cycleName, setCycleName] = useState("")
  const [cycleDescription, setCycleDescription] = useState("")
  const [startDate, setStartDate] = useState("")
  const [endDate, setEndDate] = useState("")
  const [includeSelf, setIncludeSelf] = useState(true)
  const [includeManager, setIncludeManager] = useState(true)
  const [includePeer, setIncludePeer] = useState(false)
  const [peerReviewsPerUser, setPeerReviewsPerUser] = useState(2)

  const handleCreateCycle = async () => {
    if (!session?.user?.email) {
      setMessage({ text: "You must be signed in to create an assessment cycle", type: "error" })
      return
    }

    if (!cycleName.trim()) {
      setMessage({ text: "Cycle name is required", type: "error" })
      return
    }

    if (!startDate) {
      setMessage({ text: "Start date is required", type: "error" })
      return
    }

    if (!endDate) {
      setMessage({ text: "End date is required", type: "error" })
      return
    }

    if (new Date(startDate) > new Date(endDate)) {
      setMessage({ text: "Start date must be before end date", type: "error" })
      return
    }

    if (!includeSelf && !includeManager && !includePeer) {
      setMessage({ text: "At least one assessment type must be selected", type: "error" })
      return
    }

    setIsCreating(true)

    const result = await createAssessmentCycle({
      name: cycleName,
      description: cycleDescription,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      status: "draft",
      createdBy: session.user.email,
      createdAt: new Date(),
      updatedAt: new Date(),
      updatedBy: session.user.email,
      assessmentTypes: {
        self: includeSelf,
        manager: includeManager,
        peer: includePeer,
        peerReviewsPerUser: peerReviewsPerUser
      },
      teamMembers: [],
      // Add the required projects field
      projects: []
    }, session.user.email)

    setIsCreating(false)

    if (result.success) {
      setMessage({ text: result.message || "Assessment cycle created successfully", type: "success" })
      setCycleName("")
      setCycleDescription("")
      setStartDate("")
      setEndDate("")

      // Redirect to the cycle setup page
      router.push(`/admin/assessments/cycles/${result.cycleId}/setup`)
      router.refresh()
    } else {
      setMessage({ text: result.message || "Failed to create assessment cycle", type: "error" })
    }
  }

  const handleDeleteCycle = async () => {
    if (!session?.user?.email || !cycleToDelete) {
      return
    }

    setIsDeleting(true)

    const result = await deleteAssessmentCycle(cycleToDelete, session.user.email)

    setIsDeleting(false)
    setCycleToDelete(null)

    if (result.success) {
      setMessage({ text: result.message || "Assessment cycle deleted successfully", type: "success" })
      router.refresh()
    } else {
      setMessage({ text: result.message || "Failed to delete assessment cycle", type: "error" })
    }
  }

  const handleActivateCycle = async () => {
    if (!session?.user?.email || !cycleToActivate) {
      return
    }

    setIsActivating(true)

    const result = await activateAssessmentCycle(cycleToActivate, session.user.email)

    setIsActivating(false)
    setCycleToActivate(null)

    if (result.success) {
      setMessage({ text: result.message || "Assessment cycle activated successfully", type: "success" })
      router.refresh()
    } else {
      setMessage({ text: result.message || "Failed to activate assessment cycle", type: "error" })
    }
  }

  const handleCompleteCycle = async () => {
    if (!session?.user?.email || !cycleToComplete) {
      return
    }

    setIsCompleting(true)

    const result = await completeAssessmentCycle(cycleToComplete, session.user.email)

    setIsCompleting(false)
    setCycleToComplete(null)

    if (result.success) {
      setMessage({ text: result.message || "Assessment cycle completed successfully", type: "success" })
      router.refresh()
    } else {
      setMessage({ text: result.message || "Failed to complete assessment cycle", type: "error" })
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "draft":
        return <Badge variant="outline" className="bg-slate-100">Draft</Badge>
      case "active":
        return <Badge variant="default" className="bg-green-500">Active</Badge>
      case "completed":
        return <Badge variant="secondary">Completed</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {message && (
        <div
          className={`p-4 rounded-lg border ${message.type === "success" ? "bg-green-50 border-green-200 text-green-800" : "bg-red-50 border-red-200 text-red-800"
            }`}
        >
          {message.text}
        </div>
      )}

      <div className="flex justify-end">
        <Dialog>
          <DialogTrigger asChild>
            <Button className="bg-primary hover:bg-blue-500 shadow-md border border-primary/20 font-medium transition-colors duration-200">
              <PlusCircle className="mr-2 h-4 w-4" />
              Create Assessment Cycle
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[550px]">
            <DialogHeader>
              <DialogTitle>Create New Assessment Cycle</DialogTitle>
              <DialogDescription>
                Create a new assessment cycle for your team members.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="cycleName">Cycle Name</Label>
                <Input
                  id="cycleName"
                  value={cycleName}
                  onChange={(e) => setCycleName(e.target.value)}
                  placeholder="e.g., Q2 2023 Performance Review"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="cycleDescription">Description (Optional)</Label>
                <Input
                  id="cycleDescription"
                  value={cycleDescription}
                  onChange={(e) => setCycleDescription(e.target.value)}
                  placeholder="Brief description of this assessment cycle"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Start Date</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate">End Date</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Assessment Types</Label>
                <div className="flex flex-col space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="includeSelf"
                      checked={includeSelf}
                      onChange={(e) => setIncludeSelf(e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                    />
                    <Label htmlFor="includeSelf" className="text-sm font-normal">
                      Self Assessment
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="includeManager"
                      checked={includeManager}
                      onChange={(e) => setIncludeManager(e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                    />
                    <Label htmlFor="includeManager" className="text-sm font-normal">
                      Manager Assessment
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="includePeer"
                      checked={includePeer}
                      onChange={(e) => setIncludePeer(e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                    />
                    <Label htmlFor="includePeer" className="text-sm font-normal">
                      Peer Assessment
                    </Label>
                  </div>
                </div>
              </div>

              {includePeer && (
                <div className="space-y-2">
                  <Label htmlFor="peerReviewsPerUser">Number of Peer Reviews per User</Label>
                  <Input
                    id="peerReviewsPerUser"
                    type="number"
                    min="1"
                    max="5"
                    value={peerReviewsPerUser}
                    onChange={(e) => setPeerReviewsPerUser(parseInt(e.target.value))}
                  />
                </div>
              )}
            </div>

            <DialogFooter>
              <Button
                onClick={handleCreateCycle}
                disabled={isCreating}
                className="bg-primary hover:bg-blue-500"
              >
                {isCreating ? "Creating..." : "Create Cycle"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Assessment Cycles</CardTitle>
        </CardHeader>
        <CardContent>
          {error ? (
            <div className="text-center py-4 text-red-500">
              {error}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Period</TableHead>
                    <TableHead>Team Members</TableHead>
                    <TableHead>Completion</TableHead>
                    <TableHead>Assessment Types</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {cycles.length > 0 ? (
                    cycles.map((cycle) => (
                      <TableRow key={cycle.id}>
                        <TableCell className="font-medium">{cycle.name}</TableCell>
                        <TableCell>{getStatusBadge(cycle.status)}</TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="text-xs text-muted-foreground">
                              {format(new Date(cycle.startDate), "MMM d, yyyy")}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              to {format(new Date(cycle.endDate), "MMM d, yyyy")}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Users className="h-4 w-4 mr-1 text-muted-foreground" />
                            <span>{cycle.teamMembers.length}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="w-full bg-muted rounded-full h-2.5">
                            <div
                              className="bg-primary h-2.5 rounded-full"
                              style={{ width: `${cycle.completionRate}%` }}
                            ></div>
                          </div>
                          <span className="text-xs text-muted-foreground mt-1 block">
                            {cycle.completionRate}% complete
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {cycle.assessmentTypes.self && (
                              <Badge variant="outline" className="text-xs">Self</Badge>
                            )}
                            {cycle.assessmentTypes.manager && (
                              <Badge variant="outline" className="text-xs">Manager</Badge>
                            )}
                            {cycle.assessmentTypes.peer && (
                              <Badge variant="outline" className="text-xs">
                                Peer ({cycle.assessmentTypes.peerReviewsPerUser})
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-1">
                            <Link href={`/admin/assessments/cycles/${cycle.id}`}>
                              <Button variant="ghost" size="icon" title="View Details">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </Link>

                            {cycle.status === "draft" && (
                              <>
                                <Link href={`/admin/assessments/cycles/${cycle.id}/setup`}>
                                  <Button variant="ghost" size="icon" title="Setup Cycle">
                                    <Users className="h-4 w-4" />
                                  </Button>
                                </Link>

                                <Button
                                  variant="ghost"
                                  size="icon"
                                  title="Activate Cycle"
                                  onClick={() => setCycleToActivate(cycle.id)}
                                  disabled={cycle.teamMembers.length === 0}
                                >
                                  <Play className="h-4 w-4 text-green-500" />
                                </Button>
                              </>
                            )}

                            {cycle.status === "active" && (
                              <Button
                                variant="ghost"
                                size="icon"
                                title="Complete Cycle"
                                onClick={() => setCycleToComplete(cycle.id)}
                              >
                                <CheckCircle className="h-4 w-4 text-blue-500" />
                              </Button>
                            )}

                            {/* Delete button available for all cycle statuses */}
                            <Button
                              variant="ghost"
                              size="icon"
                              title="Delete Cycle"
                              onClick={() => setCycleToDelete(cycle.id)}
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-4">
                        No assessment cycles found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!cycleToDelete} onOpenChange={(open) => !open && setCycleToDelete(null)}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center text-red-600">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Delete Assessment Cycle
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this assessment cycle? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="rounded-md bg-amber-50 p-4 border border-amber-200">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 mr-2" />
                <div>
                  <h3 className="text-sm font-medium text-amber-800">Warning</h3>
                  <div className="mt-1 text-sm text-amber-700">
                    <p>Deleting this assessment cycle will also delete:</p>
                    <ul className="list-disc pl-5 mt-1 space-y-1">
                      <li>All assessments associated with this cycle</li>
                      <li>All team member assignments</li>
                      <li>All manager and peer reviewer assignments</li>
                      <li>All assessment data and progress</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="flex space-x-2 justify-end">
            <Button
              variant="outline"
              onClick={() => setCycleToDelete(null)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteCycle}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Activate Confirmation Dialog */}
      <Dialog open={!!cycleToActivate} onOpenChange={(open) => !open && setCycleToActivate(null)}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center text-green-600">
              <Play className="h-5 w-5 mr-2" />
              Activate Assessment Cycle
            </DialogTitle>
            <DialogDescription>
              Activating this cycle will create assessments for all team members and notify them. Are you sure you want to proceed?
            </DialogDescription>
          </DialogHeader>

          <DialogFooter className="flex space-x-2 justify-end">
            <Button
              variant="outline"
              onClick={() => setCycleToActivate(null)}
            >
              Cancel
            </Button>
            <Button
              variant="default"
              className="bg-green-600 hover:bg-green-700"
              onClick={handleActivateCycle}
              disabled={isActivating}
            >
              {isActivating ? "Activating..." : "Activate"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Complete Confirmation Dialog */}
      <Dialog open={!!cycleToComplete} onOpenChange={(open) => !open && setCycleToComplete(null)}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center text-blue-600">
              <CheckCircle className="h-5 w-5 mr-2" />
              Complete Assessment Cycle
            </DialogTitle>
            <DialogDescription>
              Completing this cycle will mark all pending assessments as completed and finalize the results. Are you sure you want to proceed?
            </DialogDescription>
          </DialogHeader>

          <DialogFooter className="flex space-x-2 justify-end">
            <Button
              variant="outline"
              onClick={() => setCycleToComplete(null)}
            >
              Cancel
            </Button>
            <Button
              variant="default"
              className="bg-blue-600 hover:bg-blue-700"
              onClick={handleCompleteCycle}
              disabled={isCompleting}
            >
              {isCompleting ? "Completing..." : "Complete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
