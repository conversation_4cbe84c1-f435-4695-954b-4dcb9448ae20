"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { saveUserProfileByAdmin } from "@/lib/actions/admin-users"
import { businessUnitOptions, careerLevelOptions } from "@/lib/models/user-profile"

interface UserFormProps {
  adminEmail: string
  user?: any
}

export default function UserForm({ adminEmail, user }: UserFormProps) {
  const router = useRouter()
  const [formData, setFormData] = useState({
    email: "",
    name: "",
    businessUnit: "none",
    careerLevel: "none",
    jobRole: "",
    managerEmail: ""
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [message, setMessage] = useState<{ text: string; type: "success" | "error" } | null>(null)

  // Initialize form with user data if editing
  useEffect(() => {
    if (user) {
      setFormData({
        email: user.email || "",
        name: user.name || "",
        businessUnit: user.businessUnit || "none",
        careerLevel: user.careerLevel || "none",
        jobRole: user.jobRole || "",
        managerEmail: user.managerEmail || ""
      })
    }
  }, [user])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setMessage(null)

    // Validate required fields
    if (!formData.email || !formData.name) {
      setMessage({ text: "Email and name are required", type: "error" })
      setIsSubmitting(false)
      return
    }

    // Save the user profile
    const result = await saveUserProfileByAdmin(formData, adminEmail)

    setIsSubmitting(false)

    if (result.success) {
      setMessage({ text: result.message || "User saved successfully", type: "success" })

      // If creating a new user, reset the form
      if (!user) {
        setFormData({
          email: "",
          name: "",
          businessUnit: "none",
          careerLevel: "none",
          jobRole: "",
          managerEmail: ""
        })
      }

      // Refresh the page to update the data
      router.refresh()

      // Redirect to users list after a short delay
      setTimeout(() => {
        router.push("/admin/users")
      }, 1500)
    } else {
      setMessage({ text: result.message || "Failed to save user", type: "error" })
    }
  }

  return (
    <Card className="p-6">
      {message && (
        <div
          className={`p-4 mb-6 rounded-lg border ${message.type === "success" ? "bg-green-50 border-green-200 text-green-800" : "bg-red-50 border-red-200 text-red-800"
            }`}
        >
          {message.text}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="email">Email <span className="text-red-500">*</span></Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              required
              disabled={!!user} // Disable email editing for existing users
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="name">Name <span className="text-red-500">*</span></Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="businessUnit">Business Unit</Label>
            <Select
              value={formData.businessUnit}
              onValueChange={(value) => handleSelectChange("businessUnit", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select business unit" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                {businessUnitOptions.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option.toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="careerLevel">Career Level</Label>
            <Select
              value={formData.careerLevel}
              onValueChange={(value) => handleSelectChange("careerLevel", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select career level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                {careerLevelOptions.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option.toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="jobRole">Job Role</Label>
            <Input
              id="jobRole"
              name="jobRole"
              value={formData.jobRole}
              onChange={handleChange}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="managerEmail">Manager Email</Label>
            <Input
              id="managerEmail"
              name="managerEmail"
              type="email"
              value={formData.managerEmail}
              onChange={handleChange}
            />
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/admin/users")}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Saving..." : user ? "Update User" : "Add User"}
          </Button>
        </div>
      </form>
    </Card>
  )
}
