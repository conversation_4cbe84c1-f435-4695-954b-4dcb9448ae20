'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { addAdmin, removeAdmin } from '@/lib/actions/admin-actions'
import { toast } from '@/components/ui/use-toast'
import { Trash2, Plus, Loader2 } from 'lucide-react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface AdminSettingsProps {
  adminEmails: string[]
  currentUserEmail: string
}

export default function AdminSettings({ adminEmails, currentUserEmail }: AdminSettingsProps) {
  const [emails, setEmails] = useState<string[]>(adminEmails)
  const [newEmail, setNewEmail] = useState('')
  const [isAdding, setIsAdding] = useState(false)
  const [isRemoving, setIsRemoving] = useState(false)
  const [emailToRemove, setEmailToRemove] = useState<string | null>(null)
  
  // Add a new admin
  const handleAddAdmin = async () => {
    if (!newEmail || !newEmail.includes('@')) {
      toast({
        title: 'Invalid email',
        description: 'Please enter a valid email address',
        variant: 'destructive',
      })
      return
    }
    
    if (emails.includes(newEmail)) {
      toast({
        title: 'Email already exists',
        description: 'This email is already an admin',
        variant: 'destructive',
      })
      return
    }
    
    setIsAdding(true)
    
    try {
      const result = await addAdmin(newEmail)
      
      if (result) {
        setEmails([...emails, newEmail])
        setNewEmail('')
        toast({
          title: 'Admin added',
          description: `${newEmail} has been added as an admin`,
        })
      } else {
        toast({
          title: 'Failed to add admin',
          description: 'An error occurred while adding the admin',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Error adding admin:', error)
      toast({
        title: 'Failed to add admin',
        description: 'An error occurred while adding the admin',
        variant: 'destructive',
      })
    } finally {
      setIsAdding(false)
    }
  }
  
  // Remove an admin
  const handleRemoveAdmin = async (email: string) => {
    // Don't allow removing the current user
    if (email === currentUserEmail) {
      toast({
        title: 'Cannot remove yourself',
        description: 'You cannot remove your own admin access',
        variant: 'destructive',
      })
      return
    }
    
    setIsRemoving(true)
    
    try {
      const result = await removeAdmin(email)
      
      if (result) {
        setEmails(emails.filter(e => e !== email))
        toast({
          title: 'Admin removed',
          description: `${email} has been removed as an admin`,
        })
      } else {
        toast({
          title: 'Failed to remove admin',
          description: 'An error occurred while removing the admin',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Error removing admin:', error)
      toast({
        title: 'Failed to remove admin',
        description: 'An error occurred while removing the admin',
        variant: 'destructive',
      })
    } finally {
      setIsRemoving(false)
      setEmailToRemove(null)
    }
  }
  
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="grid gap-2">
          <Label htmlFor="adminEmails">Current Admin Emails</Label>
          <div className="space-y-2">
            {emails.length === 0 ? (
              <p className="text-sm text-muted-foreground">No admin emails configured</p>
            ) : (
              emails.map((email) => (
                <div key={email} className="flex items-center justify-between p-2 rounded-md border border-border">
                  <span className="text-sm">{email}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setEmailToRemove(email)}
                    disabled={email === currentUserEmail || isRemoving}
                    className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                  >
                    {isRemoving && emailToRemove === email ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Trash2 className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              ))
            )}
          </div>
        </div>
        
        <div className="grid gap-2">
          <Label htmlFor="newAdminEmail">Add New Admin</Label>
          <div className="flex space-x-2">
            <Input
              id="newAdminEmail"
              placeholder="<EMAIL>"
              value={newEmail}
              onChange={(e) => setNewEmail(e.target.value)}
              disabled={isAdding}
            />
            <Button onClick={handleAddAdmin} disabled={isAdding}>
              {isAdding ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Plus className="h-4 w-4 mr-2" />
              )}
              Add
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            Add a new administrator by email address
          </p>
        </div>
      </div>
      
      {/* Confirmation dialog for removing admin */}
      <AlertDialog open={!!emailToRemove} onOpenChange={(open) => !open && setEmailToRemove(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Admin Access</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove admin access for {emailToRemove}?
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => emailToRemove && handleRemoveAdmin(emailToRemove)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Remove
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
