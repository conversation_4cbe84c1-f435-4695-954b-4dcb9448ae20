"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { saveProjectSkillTemplate } from "@/lib/actions/projects"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogTrigger } from "@/components/ui/dialog"
import { AlertCircle, CheckCircle2, Plus, Trash2, Download } from "lucide-react"
import { createCustomProjectSkill } from "@/lib/project-skills-data"
import { ProjectSkill } from "@/lib/project-skills-data"
import { getBUSkillTemplate } from "@/lib/actions/admin-skills"

interface ProjectSkillTemplateEditorProps {
  adminEmail: string
  projectId: string
  projectName: string
  businessUnit: string
  initialTemplate: any
  isDefault: boolean
}

export default function ProjectSkillTemplateEditor({
  adminEmail,
  projectId,
  projectName,
  businessUnit,
  initialTemplate,
  isDefault,
}: ProjectSkillTemplateEditorProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [message, setMessage] = useState<{ text: string; type: 'success' | 'error' } | null>(null)
  const [skills, setSkills] = useState<ProjectSkill[]>(initialTemplate?.skills || [])
  const [showImportDialog, setShowImportDialog] = useState(false)

  // State for new skill form
  const [newSkill, setNewSkill] = useState({
    category: "",
    description: "",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 5,
  })

  const handleSkillChange = (index: number, field: string, value: any) => {
    const updatedSkills = [...skills]
    updatedSkills[index] = {
      ...updatedSkills[index],
      [field]: value,
    }
    setSkills(updatedSkills)
  }

  const handleNewSkillChange = (field: string, value: any) => {
    setNewSkill({
      ...newSkill,
      [field]: field.startsWith('target') ? parseInt(value) || 0 : value,
    })
  }

  const addNewSkill = () => {
    if (!newSkill.category || !newSkill.description) {
      setMessage({ text: "Category and description are required for new skills", type: "error" })
      return
    }

    const skillId = newSkill.category.toLowerCase().replace(/[^a-z0-9]/g, '-')

    const customSkill = createCustomProjectSkill(
      skillId,
      newSkill.category,
      newSkill.description,
      projectName,
      {
        cl2: newSkill.targetCL2,
        cl3: newSkill.targetCL3,
        cl4: newSkill.targetCL4,
        cl5: newSkill.targetCL5,
        cl6: newSkill.targetCL6,
      }
    )

    setSkills([...skills, customSkill])

    // Reset form
    setNewSkill({
      category: "",
      description: "",
      targetCL2: 2,
      targetCL3: 3,
      targetCL4: 4,
      targetCL5: 4,
      targetCL6: 5,
    })

    setMessage(null)
  }

  const removeSkill = (index: number) => {
    const updatedSkills = [...skills]
    updatedSkills.splice(index, 1)
    setSkills(updatedSkills)
  }

  const importFromBusinessUnit = async () => {
    setIsImporting(true)
    setMessage(null)

    try {
      // Fetch the business unit template
      const buTemplateResult = await getBUSkillTemplate(businessUnit)

      if (buTemplateResult.success && buTemplateResult.data) {
        // Get the example project skills
        const exampleSkills = buTemplateResult.data.projectSkills || []

        if (exampleSkills.length === 0) {
          setMessage({
            text: "No example skills found in the business unit template",
            type: "error"
          })
          return
        }

        // Add them to the current project skills
        // (with project name updated to current project)
        const updatedSkills = exampleSkills.map((skill: any) => ({
          ...skill,
          projectName: projectName,
          isCustom: false
        }))

        // Merge with existing skills, avoiding duplicates by ID
        const existingIds = skills.map((s: any) => s.id)
        const newSkills = updatedSkills.filter((s: any) => !existingIds.includes(s.id))

        setSkills([...skills, ...newSkills])
        setShowImportDialog(false)
        setMessage({
          text: `Imported ${newSkills.length} skills from business unit template`,
          type: "success"
        })
      } else {
        setMessage({
          text: buTemplateResult.message || "Failed to load business unit template",
          type: "error"
        })
      }
    } catch (error) {
      console.error("Error importing from business unit template:", error)
      setMessage({ text: "An unexpected error occurred during import", type: "error" })
    } finally {
      setIsImporting(false)
    }
  }

  const handleSubmit = async () => {
    setIsLoading(true)
    setMessage(null)

    try {
      const result = await saveProjectSkillTemplate(
        projectId,
        { skills },
        adminEmail
      )

      if (result.success) {
        setMessage({ text: result.message, type: "success" })
        router.refresh()
      } else {
        setMessage({ text: result.message || "An error occurred", type: "error" })
      }
    } catch (error) {
      console.error("Error saving project skill template:", error)
      setMessage({ text: "An unexpected error occurred", type: "error" })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {message && (
        <Alert className={`mb-6 ${message.type === 'success' ? 'bg-green-50 text-green-800 border-green-200' : 'bg-red-50 text-red-800 border-red-200'}`}>
          <div className="flex items-center gap-2">
            {message.type === 'success' ? (
              <CheckCircle2 className="h-4 w-4 text-green-600" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-600" />
            )}
            <AlertDescription>{message.text}</AlertDescription>
          </div>
        </Alert>
      )}

      {isDefault && (
        <Alert className="bg-blue-50 text-blue-800 border-blue-200">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4 text-blue-600" />
            <AlertDescription>
              This is a default template. You can customize it for this project.
            </AlertDescription>
          </div>
        </Alert>
      )}

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Project Skills</CardTitle>
          <Button
            variant="outline"
            onClick={() => setShowImportDialog(true)}
            className="ml-2"
          >
            <Download className="mr-2 h-4 w-4" />
            Import from BU Template
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">

            {/* Import Dialog */}
            <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Import Skills from Business Unit Template</DialogTitle>
                </DialogHeader>
                <p>This will import example skills from the {businessUnit} business unit template.</p>
                <p className="text-sm text-muted-foreground">
                  Only skills that don&apos;t already exist in this project will be imported.
                  Imported skills will have their project name updated to match this project.
                </p>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowImportDialog(false)} disabled={isImporting}>Cancel</Button>
                  <Button onClick={importFromBusinessUnit} disabled={isImporting}>
                    {isImporting ? (
                      <>
                        <div className="h-4 w-4 mr-2 rounded-full border-2 border-white/30 border-t-white animate-spin"></div>
                        Importing...
                      </>
                    ) : (
                      "Import Skills"
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Category</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead className="w-[80px] text-center">CL2</TableHead>
                  <TableHead className="w-[80px] text-center">CL3</TableHead>
                  <TableHead className="w-[80px] text-center">CL4</TableHead>
                  <TableHead className="w-[80px] text-center">CL5</TableHead>
                  <TableHead className="w-[80px] text-center">CL6</TableHead>
                  <TableHead className="w-[80px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {skills.map((skill, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Input
                        value={skill.category}
                        onChange={(e) => handleSkillChange(index, "category", e.target.value)}
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        value={skill.description}
                        onChange={(e) => handleSkillChange(index, "description", e.target.value)}
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        min="0"
                        max="6"
                        value={skill.targetCL2 || 0}
                        onChange={(e) => handleSkillChange(index, "targetCL2", parseInt(e.target.value) || 0)}
                        className="text-center"
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        min="0"
                        max="6"
                        value={skill.targetCL3 || 0}
                        onChange={(e) => handleSkillChange(index, "targetCL3", parseInt(e.target.value) || 0)}
                        className="text-center"
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        min="0"
                        max="6"
                        value={skill.targetCL4 || 0}
                        onChange={(e) => handleSkillChange(index, "targetCL4", parseInt(e.target.value) || 0)}
                        className="text-center"
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        min="0"
                        max="6"
                        value={skill.targetCL5 || 0}
                        onChange={(e) => handleSkillChange(index, "targetCL5", parseInt(e.target.value) || 0)}
                        className="text-center"
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        min="0"
                        max="6"
                        value={skill.targetCL6 || 0}
                        onChange={(e) => handleSkillChange(index, "targetCL6", parseInt(e.target.value) || 0)}
                        className="text-center"
                      />
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeSkill(index)}
                        title="Remove Skill"
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            <div className="border p-4 rounded-md">
              <h3 className="text-lg font-medium mb-4">Add New Skill</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <Label htmlFor="new-category">Category</Label>
                  <Input
                    id="new-category"
                    value={newSkill.category}
                    onChange={(e) => handleNewSkillChange("category", e.target.value)}
                    placeholder="e.g., React, TypeScript, Docker"
                  />
                </div>
                <div>
                  <Label htmlFor="new-description">Description</Label>
                  <Input
                    id="new-description"
                    value={newSkill.description}
                    onChange={(e) => handleNewSkillChange("description", e.target.value)}
                    placeholder="Brief description of the skill"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                <div>
                  <Label htmlFor="new-targetCL2">CL2 Target</Label>
                  <Input
                    id="new-targetCL2"
                    type="number"
                    min="0"
                    max="6"
                    value={newSkill.targetCL2}
                    onChange={(e) => handleNewSkillChange("targetCL2", e.target.value)}
                    className="text-center"
                  />
                </div>
                <div>
                  <Label htmlFor="new-targetCL3">CL3 Target</Label>
                  <Input
                    id="new-targetCL3"
                    type="number"
                    min="0"
                    max="6"
                    value={newSkill.targetCL3}
                    onChange={(e) => handleNewSkillChange("targetCL3", e.target.value)}
                    className="text-center"
                  />
                </div>
                <div>
                  <Label htmlFor="new-targetCL4">CL4 Target</Label>
                  <Input
                    id="new-targetCL4"
                    type="number"
                    min="0"
                    max="6"
                    value={newSkill.targetCL4}
                    onChange={(e) => handleNewSkillChange("targetCL4", e.target.value)}
                    className="text-center"
                  />
                </div>
                <div>
                  <Label htmlFor="new-targetCL5">CL5 Target</Label>
                  <Input
                    id="new-targetCL5"
                    type="number"
                    min="0"
                    max="6"
                    value={newSkill.targetCL5}
                    onChange={(e) => handleNewSkillChange("targetCL5", e.target.value)}
                    className="text-center"
                  />
                </div>
                <div>
                  <Label htmlFor="new-targetCL6">CL6 Target</Label>
                  <Input
                    id="new-targetCL6"
                    type="number"
                    min="0"
                    max="6"
                    value={newSkill.targetCL6}
                    onChange={(e) => handleNewSkillChange("targetCL6", e.target.value)}
                    className="text-center"
                  />
                </div>
              </div>

              <Button onClick={addNewSkill} className="w-full">
                <Plus className="mr-2 h-4 w-4" />
                Add Skill
              </Button>
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => router.push(`/admin/projects/${projectId}`)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button onClick={handleSubmit} disabled={isLoading}>
                {isLoading ? "Saving..." : "Save Template"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
