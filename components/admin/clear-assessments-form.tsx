"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { clearAllAssessments } from "@/lib/actions/admin-skills"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>rash } from "lucide-react"

export default function ClearAssessmentsForm() {
  const router = useRouter()
  const { data: session } = useSession()
  const [isClearing, setIsClearing] = useState(false)
  const [confirmText, setConfirmText] = useState("")
  const [message, setMessage] = useState<{ text: string; type: "success" | "error" } | null>(null)
  
  const handleClearAssessments = async () => {
    if (!session?.user?.email) {
      setMessage({ text: "You must be signed in as an admin to clear assessments", type: "error" })
      return
    }
    
    if (confirmText !== "DELETE ALL ASSESSMENTS") {
      setMessage({ text: "Please type the confirmation text exactly as shown", type: "error" })
      return
    }
    
    setIsClearing(true)
    
    const result = await clearAllAssessments(session.user.email)
    
    setIsClearing(false)
    
    if (result.success) {
      setMessage({ text: result.message || "Assessments cleared successfully", type: "success" })
      setConfirmText("")
      // Redirect after a short delay
      setTimeout(() => {
        router.push("/admin/skills")
        router.refresh()
      }, 2000)
    } else {
      setMessage({ text: result.message || "Failed to clear assessments", type: "error" })
    }
  }
  
  return (
    <div className="space-y-6">
      <Button
        variant="outline"
        onClick={() => router.push("/admin/skills")}
        className="mb-4"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Templates
      </Button>
      
      {message && (
        <div
          className={`p-4 rounded-lg border ${
            message.type === "success" ? "bg-green-50 border-green-200 text-green-800" : "bg-red-50 border-red-200 text-red-800"
          }`}
        >
          {message.text}
        </div>
      )}
      
      <Card className="p-6 shadow-md border-red-200 bg-red-50">
        <div className="flex items-center gap-3 mb-4 text-red-800">
          <AlertTriangle className="h-6 w-6" />
          <h2 className="text-xl font-semibold">Danger Zone</h2>
        </div>
        
        <p className="mb-6 text-red-700">
          This action will permanently delete all assessments from the database. This cannot be undone.
          Only use this for testing purposes.
        </p>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2 text-red-800">
              Type <span className="font-bold">DELETE ALL ASSESSMENTS</span> to confirm:
            </label>
            <input
              type="text"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              className="w-full p-2 border border-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="Type the confirmation text"
            />
          </div>
          
          <Button
            variant="destructive"
            onClick={handleClearAssessments}
            disabled={isClearing || confirmText !== "DELETE ALL ASSESSMENTS"}
            className="bg-red-600 hover:bg-red-700"
          >
            <Trash className="mr-2 h-4 w-4" />
            {isClearing ? (
              <span className="flex items-center gap-2">
                <div className="h-4 w-4 rounded-full border-2 border-white/30 border-t-white animate-spin"></div>
                Clearing...
              </span>
            ) : (
              "Clear All Assessments"
            )}
          </Button>
        </div>
      </Card>
    </div>
  )
}
