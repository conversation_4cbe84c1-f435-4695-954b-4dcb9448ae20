"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { 
  ArrowLeft, 
  Download,
  BarChart
} from "lucide-react"

interface MemberAssessmentViewProps {
  comparisonData: any
  cycleId: string
  userId: string
}

export default function MemberAssessmentView({ 
  comparisonData, 
  cycleId, 
  userId 
}: MemberAssessmentViewProps) {
  const [activeTab, setActiveTab] = useState("comparison")
  
  // Helper function to get color for skill level
  const getLevelColor = (level: number | null, target: number) => {
    if (level === null) return "bg-gray-500"
    if (level < target) return "bg-red-700"
    if (level === target) return "bg-amber-500"
    return "bg-green-600"
  }
  
  // Helper function to get color for gap
  const getGapColor = (gap: number | null) => {
    if (gap === null) return ""
    if (gap < -1) return "text-red-600"
    if (gap < 0) return "text-orange-500"
    if (gap === 0) return "text-green-600"
    if (gap > 0) return "text-blue-600"
    return ""
  }
  
  // Helper function to format gap value
  const formatGap = (gap: number | null) => {
    if (gap === null) return "-"
    return gap > 0 ? `+${gap.toFixed(1)}` : gap.toFixed(1)
  }
  
  const { user, cycle, self, manager, peers, skillComparison } = comparisonData
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Link href={`/admin/assessments/cycles/${cycleId}`}>
          <Button variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Cycle
          </Button>
        </Link>
        
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Results
          </Button>
          
          <Button variant="outline">
            <BarChart className="mr-2 h-4 w-4" />
            View Charts
          </Button>
        </div>
      </div>
      
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-2xl">{user?.name || "Team Member"}</CardTitle>
              <CardDescription>{user?.email || userId}</CardDescription>
            </div>
            <div className="flex space-x-2">
              {user?.businessUnit && (
                <Badge variant="outline">{user.businessUnit}</Badge>
              )}
              {user?.careerLevel && (
                <Badge variant="outline">{user.careerLevel}</Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-4">
              <TabsTrigger value="comparison">Comparison</TabsTrigger>
              <TabsTrigger value="self" disabled={!self}>Self</TabsTrigger>
              <TabsTrigger value="manager" disabled={!manager}>Manager</TabsTrigger>
              <TabsTrigger value="peers" disabled={!peers || peers.length === 0}>Peers</TabsTrigger>
            </TabsList>
            
            <TabsContent value="comparison" className="mt-6">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Skill Category</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead className="text-center">Target</TableHead>
                      <TableHead className="text-center">Self</TableHead>
                      {manager && (
                        <TableHead className="text-center">Manager</TableHead>
                      )}
                      {peers && peers.length > 0 && (
                        <TableHead className="text-center">Peer Avg</TableHead>
                      )}
                      <TableHead className="text-center">Gap</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {skillComparison && skillComparison.length > 0 ? (
                      skillComparison.map((skill: any, index: number) => (
                        <TableRow key={skill.id || index}>
                          <TableCell className="font-medium">{skill.category}</TableCell>
                          <TableCell className="text-sm text-muted-foreground">{skill.description}</TableCell>
                          <TableCell className="text-center">
                            <div className="inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-white bg-primary">
                              {skill.target}
                            </div>
                          </TableCell>
                          <TableCell className="text-center">
                            <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-white ${getLevelColor(skill.self, skill.target)}`}>
                              {skill.self !== null ? skill.self : "-"}
                            </div>
                          </TableCell>
                          {manager && (
                            <TableCell className="text-center">
                              <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-white ${getLevelColor(skill.manager, skill.target)}`}>
                                {skill.manager !== null ? skill.manager : "-"}
                              </div>
                            </TableCell>
                          )}
                          {peers && peers.length > 0 && (
                            <TableCell className="text-center">
                              <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-white ${getLevelColor(skill.peerAvg, skill.target)}`}>
                                {skill.peerAvg !== null ? skill.peerAvg.toFixed(1) : "-"}
                              </div>
                            </TableCell>
                          )}
                          <TableCell className="text-center">
                            <span className={`font-medium ${getGapColor(skill.gap)}`}>
                              {formatGap(skill.gap)}
                            </span>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-4">
                          No assessment data available
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
              
              <div className="mt-6 p-4 bg-muted/20 rounded-lg">
                <h3 className="text-lg font-medium mb-2">Assessment Legend</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-primary"></div>
                    <span className="text-sm">Target Level</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-red-700"></div>
                    <span className="text-sm">Below Target</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-amber-500"></div>
                    <span className="text-sm">At Target</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-green-600"></div>
                    <span className="text-sm">Above Target</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full bg-gray-500"></div>
                    <span className="text-sm">Not Assessed</span>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="self" className="mt-6">
              {self ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <Card className="p-4 bg-muted/20">
                      <h3 className="text-sm font-medium text-muted-foreground">Assessment Name</h3>
                      <p className="font-medium">{self.name}</p>
                    </Card>
                    <Card className="p-4 bg-muted/20">
                      <h3 className="text-sm font-medium text-muted-foreground">Completed On</h3>
                      <p className="font-medium">
                        {new Date(self.updatedAt).toLocaleDateString()}
                      </p>
                    </Card>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Skill Category</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead className="text-center">Target</TableHead>
                          <TableHead className="text-center">Self Rating</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {self.skills && self.skills.length > 0 ? (
                          self.skills.map((skill: any, index: number) => (
                            <TableRow key={skill.id || index}>
                              <TableCell className="font-medium">{skill.category}</TableCell>
                              <TableCell className="text-sm text-muted-foreground">{skill.description}</TableCell>
                              <TableCell className="text-center">
                                <div className="inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-white bg-primary">
                                  {skill.targetCL3 || 3}
                                </div>
                              </TableCell>
                              <TableCell className="text-center">
                                <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-white ${getLevelColor(skill.currentLevel, skill.targetCL3 || 3)}`}>
                                  {skill.currentLevel !== null ? skill.currentLevel : "-"}
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={4} className="text-center py-4">
                              No self-assessment data available
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                  
                  {self.feedback && (
                    <Card className="p-4">
                      <h3 className="text-lg font-medium mb-2">Self Feedback</h3>
                      <p className="text-muted-foreground">{self.feedback}</p>
                    </Card>
                  )}
                </div>
              ) : (
                <div className="p-6 text-center text-muted-foreground border rounded-md">
                  No self-assessment data available
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="manager" className="mt-6">
              {manager ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <Card className="p-4 bg-muted/20">
                      <h3 className="text-sm font-medium text-muted-foreground">Manager</h3>
                      <p className="font-medium">{manager.reviewerName || "Unknown"}</p>
                    </Card>
                    <Card className="p-4 bg-muted/20">
                      <h3 className="text-sm font-medium text-muted-foreground">Completed On</h3>
                      <p className="font-medium">
                        {new Date(manager.updatedAt).toLocaleDateString()}
                      </p>
                    </Card>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Skill Category</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead className="text-center">Target</TableHead>
                          <TableHead className="text-center">Manager Rating</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {manager.skills && manager.skills.length > 0 ? (
                          manager.skills.map((skill: any, index: number) => (
                            <TableRow key={skill.id || index}>
                              <TableCell className="font-medium">{skill.category}</TableCell>
                              <TableCell className="text-sm text-muted-foreground">{skill.description}</TableCell>
                              <TableCell className="text-center">
                                <div className="inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-white bg-primary">
                                  {skill.targetCL3 || 3}
                                </div>
                              </TableCell>
                              <TableCell className="text-center">
                                <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-white ${getLevelColor(skill.currentLevel, skill.targetCL3 || 3)}`}>
                                  {skill.currentLevel !== null ? skill.currentLevel : "-"}
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={4} className="text-center py-4">
                              No manager assessment data available
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                  
                  {manager.feedback && (
                    <Card className="p-4">
                      <h3 className="text-lg font-medium mb-2">Manager Feedback</h3>
                      <p className="text-muted-foreground">{manager.feedback}</p>
                    </Card>
                  )}
                </div>
              ) : (
                <div className="p-6 text-center text-muted-foreground border rounded-md">
                  No manager assessment data available
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="peers" className="mt-6">
              {peers && peers.length > 0 ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-3 gap-4">
                    <Card className="p-4 bg-muted/20">
                      <h3 className="text-sm font-medium text-muted-foreground">Peer Reviewers</h3>
                      <p className="font-medium">{peers.length}</p>
                    </Card>
                    <Card className="p-4 bg-muted/20">
                      <h3 className="text-sm font-medium text-muted-foreground">Completed Reviews</h3>
                      <p className="font-medium">
                        {peers.filter((p: any) => p.status === "completed").length} / {peers.length}
                      </p>
                    </Card>
                    <Card className="p-4 bg-muted/20">
                      <h3 className="text-sm font-medium text-muted-foreground">Average Rating</h3>
                      <p className="font-medium">
                        {skillComparison && skillComparison.length > 0 
                          ? (skillComparison.reduce((sum: number, skill: any) => sum + (skill.peerAvg || 0), 0) / 
                             skillComparison.filter((skill: any) => skill.peerAvg !== null).length).toFixed(1)
                          : "-"
                        }
                      </p>
                    </Card>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Skill Category</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead className="text-center">Target</TableHead>
                          <TableHead className="text-center">Peer Average</TableHead>
                          <TableHead className="text-center">Range</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {skillComparison && skillComparison.length > 0 ? (
                          skillComparison.map((skill: any, index: number) => {
                            // Calculate min and max peer ratings for this skill
                            const peerRatings = peers
                              .map((peer: any) => {
                                const peerSkill = peer.skills?.find((s: any) => s.id === skill.id);
                                return peerSkill?.currentLevel || null;
                              })
                              .filter((rating: any) => rating !== null);
                            
                            const min = peerRatings.length > 0 ? Math.min(...peerRatings) : null;
                            const max = peerRatings.length > 0 ? Math.max(...peerRatings) : null;
                            
                            return (
                              <TableRow key={skill.id || index}>
                                <TableCell className="font-medium">{skill.category}</TableCell>
                                <TableCell className="text-sm text-muted-foreground">{skill.description}</TableCell>
                                <TableCell className="text-center">
                                  <div className="inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-white bg-primary">
                                    {skill.target}
                                  </div>
                                </TableCell>
                                <TableCell className="text-center">
                                  <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-white ${getLevelColor(skill.peerAvg, skill.target)}`}>
                                    {skill.peerAvg !== null ? skill.peerAvg.toFixed(1) : "-"}
                                  </div>
                                </TableCell>
                                <TableCell className="text-center">
                                  {min !== null && max !== null ? (
                                    <span className="text-muted-foreground">
                                      {min === max ? min : `${min} - ${max}`}
                                    </span>
                                  ) : (
                                    "-"
                                  )}
                                </TableCell>
                              </TableRow>
                            );
                          })
                        ) : (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center py-4">
                              No peer assessment data available
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                  
                  {peers.some((peer: any) => peer.feedback) && (
                    <Card className="p-4">
                      <h3 className="text-lg font-medium mb-2">Peer Feedback</h3>
                      <div className="space-y-4">
                        {peers
                          .filter((peer: any) => peer.feedback)
                          .map((peer: any, index: number) => (
                            <div key={index} className="p-3 border rounded-md">
                              <p className="text-muted-foreground">{peer.feedback}</p>
                              <p className="text-xs text-muted-foreground mt-2 text-right">
                                - Peer Reviewer {index + 1}
                              </p>
                            </div>
                          ))
                        }
                      </div>
                    </Card>
                  )}
                </div>
              ) : (
                <div className="p-6 text-center text-muted-foreground border rounded-md">
                  No peer assessment data available
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
