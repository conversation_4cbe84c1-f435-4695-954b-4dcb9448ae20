"use client"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  ArrowLeft,
  Calendar,
  Users,
  CheckCircle,
  AlertTriangle,
  Mail,
  Eye,
  Download,
  BarChart
} from "lucide-react"
import { format } from "date-fns"
import { SerializedAssessmentCycle, AssessmentStatus } from "@/lib/models/assessment-cycle"
import { completeAssessmentCycle } from "@/lib/actions/assessment-cycles"

interface CycleDetailsViewProps {
  cycle: SerializedAssessmentCycle
  cycleId: string
}

export default function CycleDetailsView({ cycle, cycleId }: CycleDetailsViewProps) {
  const router = useRouter()
  const { data: session } = useSession()
  const [activeTab, setActiveTab] = useState("overview")
  const [isCompleting, setIsCompleting] = useState(false)
  const [message, setMessage] = useState<{ text: string; type: "success" | "error" } | null>(null)

  // Handle completing the cycle
  const handleCompleteCycle = async () => {
    if (!session?.user?.email) {
      setMessage({ text: "You must be signed in to complete the cycle", type: "error" })
      return
    }

    setIsCompleting(true)

    const result = await completeAssessmentCycle(cycleId, session.user.email)

    setIsCompleting(false)

    if (result.success) {
      setMessage({ text: result.message || "Assessment cycle completed successfully", type: "success" })

      // Force a hard refresh to get the latest data
      window.location.reload()
    } else {
      setMessage({ text: result.message || "Failed to complete assessment cycle", type: "error" })
    }
  }

  // Get status badge variant
  const getStatusVariant = (status: AssessmentStatus) => {
    switch (status) {
      case "pending":
        return "outline"
      case "in_progress":
        return "default"
      case "completed":
        return "secondary"
      default:
        return "outline"
    }
  }

  // Get overall status for a team member
  const getOverallStatus = (member: any) => {
    const statuses = [member.assessments.self]

    if (cycle.assessmentTypes.manager) {
      statuses.push(member.assessments.manager.status)
    }

    if (cycle.assessmentTypes.peer && member.assessments.peers.length > 0) {
      const peerStatuses = member.assessments.peers.map((peer: any) => peer.status)
      statuses.push(...peerStatuses)
    }

    if (statuses.every(status => status === "completed")) {
      return "Completed"
    }

    if (statuses.some(status => status === "in_progress")) {
      return "In Progress"
    }

    return "Pending"
  }

  // Get overall status variant
  const getOverallStatusVariant = (member: any) => {
    const status = getOverallStatus(member)

    switch (status) {
      case "Completed":
        return "secondary"
      case "In Progress":
        return "default"
      case "Pending":
        return "outline"
      default:
        return "outline"
    }
  }

  // Calculate completion statistics
  const calculateStats = () => {
    let totalAssessments = 0
    let completedAssessments = 0
    let inProgressAssessments = 0

    cycle.teamMembers.forEach(member => {
      if (cycle.assessmentTypes.self) {
        totalAssessments++
        if (member.assessments.self === "completed") {
          completedAssessments++
        } else if (member.assessments.self === "in_progress") {
          inProgressAssessments++
        }
      }

      if (cycle.assessmentTypes.manager) {
        totalAssessments++
        if (member.assessments.manager.status === "completed") {
          completedAssessments++
        } else if (member.assessments.manager.status === "in_progress") {
          inProgressAssessments++
        }
      }

      if (cycle.assessmentTypes.peer) {
        member.assessments.peers.forEach(peer => {
          totalAssessments++
          if (peer.status === "completed") {
            completedAssessments++
          } else if (peer.status === "in_progress") {
            inProgressAssessments++
          }
        })
      }
    })

    return {
      total: totalAssessments,
      completed: completedAssessments,
      inProgress: inProgressAssessments,
      pending: totalAssessments - completedAssessments - inProgressAssessments,
      completionRate: totalAssessments > 0 ? Math.round((completedAssessments / totalAssessments) * 100) : 0
    }
  }

  const stats = calculateStats()

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Link href="/admin/assessments/cycles">
          <Button variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Cycles
          </Button>
        </Link>

        {cycle.status === "active" && (
          <Button
            onClick={handleCompleteCycle}
            disabled={isCompleting}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <CheckCircle className="mr-2 h-4 w-4" />
            {isCompleting ? "Completing..." : "Complete Cycle"}
          </Button>
        )}

        {cycle.status === "draft" && (
          <Link href={`/admin/assessments/cycles/${cycleId}/setup`}>
            <Button className="bg-primary hover:bg-blue-500">
              <Users className="mr-2 h-4 w-4" />
              Setup Cycle
            </Button>
          </Link>
        )}
      </div>

      {message && (
        <div
          className={`p-4 rounded-lg border ${message.type === "success" ? "bg-green-50 border-green-200 text-green-800" : "bg-red-50 border-red-200 text-red-800"
            }`}
        >
          {message.text}
        </div>
      )}

      {/* Status-based guidance card */}
      {cycle.status === "draft" && (
        <Card className="bg-blue-50 border-blue-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg text-blue-800">This Assessment Cycle is in Draft Mode</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-blue-700">This cycle is currently in draft mode and is not visible to users. To make it available:</p>

              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-800 font-bold text-sm">1</div>
                <div>
                  <h3 className="font-medium text-blue-800">Return to the Assessment Cycles page</h3>
                  <p className="text-sm text-blue-700">Click the &quot;Back to Cycles&quot; button to return to the list of cycles</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-800 font-bold text-sm">2</div>
                <div>
                  <h3 className="font-medium text-blue-800">Activate the Cycle</h3>
                  <p className="text-sm text-blue-700">Click the green &quot;Play&quot; button next to this cycle to activate it</p>
                </div>
              </div>

              <div className="mt-2 pt-2 border-t border-blue-200 text-sm text-blue-700">
                <strong>Note:</strong> You don&apos;t need to manually add team members. When a cycle is activated, users will see it in their dashboard and can participate.
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {cycle.status === "active" && (
        <Card className="bg-green-50 border-green-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg text-green-800">This Assessment Cycle is Active</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-green-700">This cycle is currently active and visible to all users. They can participate in assessments until the cycle is completed.</p>

              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-800 font-bold text-sm">1</div>
                <div>
                  <h3 className="font-medium text-green-800">Monitor Progress</h3>
                  <p className="text-sm text-green-700">Track completion rates and individual progress in the tables below</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-800 font-bold text-sm">2</div>
                <div>
                  <h3 className="font-medium text-green-800">Complete the Cycle</h3>
                  <p className="text-sm text-green-700">When the assessment period is over, click the &quot;Complete Cycle&quot; button to finalize all assessments</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {cycle.status === "completed" && (
        <Card className="bg-slate-50 border-slate-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg text-slate-800">This Assessment Cycle is Completed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-slate-700">This cycle has been completed. All assessments are now finalized and can be viewed but not edited.</p>

              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center w-6 h-6 rounded-full bg-slate-100 text-slate-800 font-bold text-sm">1</div>
                <div>
                  <h3 className="font-medium text-slate-800">View Results</h3>
                  <p className="text-sm text-slate-700">Browse the assessment results in the tables below</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="flex items-center justify-center w-6 h-6 rounded-full bg-slate-100 text-slate-800 font-bold text-sm">2</div>
                <div>
                  <h3 className="font-medium text-slate-800">Start a New Cycle</h3>
                  <p className="text-sm text-slate-700">Return to the Assessment Cycles page to create a new cycle for the next assessment period</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-2xl">{cycle.name}</CardTitle>
              <CardDescription>{cycle.description}</CardDescription>
            </div>
            <Badge
              className={`
                ${cycle.status === "draft" ? "bg-slate-500" : ""}
                ${cycle.status === "active" ? "bg-green-500" : ""}
                ${cycle.status === "completed" ? "bg-blue-500" : ""}
              `}
            >
              {cycle.status.charAt(0).toUpperCase() + cycle.status.slice(1)}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <Card className="p-4 bg-muted/20">
              <h3 className="text-sm font-medium text-muted-foreground">Period</h3>
              <div className="mt-1">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                  <span className="text-sm">
                    {format(new Date(cycle.startDate), "MMM d, yyyy")}
                  </span>
                </div>
                <div className="flex items-center mt-1">
                  <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                  <span className="text-sm">
                    {format(new Date(cycle.endDate), "MMM d, yyyy")}
                  </span>
                </div>
              </div>
            </Card>

            <Card className="p-4 bg-muted/20">
              <h3 className="text-sm font-medium text-muted-foreground">Team Members</h3>
              <p className="text-2xl font-bold">{cycle.teamMembers.length}</p>
            </Card>

            <Card className="p-4 bg-muted/20">
              <h3 className="text-sm font-medium text-muted-foreground">Completion Rate</h3>
              <div className="flex items-center">
                <p className="text-2xl font-bold">{stats.completionRate}%</p>
              </div>
              <div className="w-full bg-muted rounded-full h-2 mt-2">
                <div
                  className="bg-primary h-2 rounded-full"
                  style={{ width: `${stats.completionRate}%` }}
                ></div>
              </div>
            </Card>

            <Card className="p-4 bg-muted/20">
              <h3 className="text-sm font-medium text-muted-foreground">Days Remaining</h3>
              <p className="text-2xl font-bold">{cycle.daysRemaining}</p>
            </Card>
          </div>

          {/* Projects Section */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3">Included Projects</h3>
            {cycle.projects && cycle.projects.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {cycle.projects.map(project => (
                  <Card key={project.projectId} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">{project.name}</CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      {project.businessUnit && (
                        <div className="text-sm text-muted-foreground mb-2">
                          Business Unit: {project.businessUnit}
                        </div>
                      )}
                      {project.description && (
                        <div className="text-sm">
                          {project.description}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-6 bg-muted/10 rounded-lg border border-dashed">
                <p className="text-muted-foreground">No projects included in this assessment cycle</p>
              </div>
            )}
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="self">Self Assessments</TabsTrigger>
              <TabsTrigger value="manager" disabled={!cycle.assessmentTypes.manager}>
                Manager Assessments
              </TabsTrigger>
              <TabsTrigger value="peer" disabled={!cycle.assessmentTypes.peer}>
                Peer Assessments
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-6">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Team Member</TableHead>
                      <TableHead className="text-center">Self</TableHead>
                      {cycle.assessmentTypes.manager && (
                        <TableHead className="text-center">Manager</TableHead>
                      )}
                      {cycle.assessmentTypes.peer && (
                        <TableHead className="text-center">Peer Reviews</TableHead>
                      )}
                      <TableHead className="text-center">Overall Status</TableHead>
                      <TableHead className="text-center">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {cycle.teamMembers.map(member => (
                      <TableRow key={member.userId}>
                        <TableCell>
                          <div className="font-medium">{member.name}</div>
                          <div className="text-sm text-muted-foreground">{member.email}</div>
                        </TableCell>

                        <TableCell className="text-center">
                          <Badge variant={getStatusVariant(member.assessments.self)}>
                            {member.assessments.self.charAt(0).toUpperCase() + member.assessments.self.slice(1)}
                          </Badge>
                        </TableCell>

                        {cycle.assessmentTypes.manager && (
                          <TableCell className="text-center">
                            <Badge variant={getStatusVariant(member.assessments.manager.status)}>
                              {member.assessments.manager.status.charAt(0).toUpperCase() + member.assessments.manager.status.slice(1)}
                            </Badge>
                          </TableCell>
                        )}

                        {cycle.assessmentTypes.peer && (
                          <TableCell className="text-center">
                            <div className="flex flex-col items-center">
                              <Badge variant="outline">
                                {member.assessments.peers.filter(p => p.status === "completed").length} / {member.assessments.peers.length}
                              </Badge>
                              <div className="w-full max-w-24 bg-muted rounded-full h-1.5 mt-1">
                                <div
                                  className="bg-primary h-1.5 rounded-full"
                                  style={{
                                    width: `${member.assessments.peers.length > 0
                                      ? (member.assessments.peers.filter(p => p.status === "completed").length / member.assessments.peers.length) * 100
                                      : 0}%`
                                  }}
                                ></div>
                              </div>
                            </div>
                          </TableCell>
                        )}

                        <TableCell className="text-center">
                          <Badge variant={getOverallStatusVariant(member)}>
                            {getOverallStatus(member)}
                          </Badge>
                        </TableCell>

                        <TableCell className="text-center">
                          <div className="flex justify-center space-x-1">
                            <Button variant="ghost" size="icon" title="View Details">
                              <Link href={`/admin/assessments/cycles/${cycleId}/members/${member.userId}`}>
                                <Eye className="h-4 w-4" />
                              </Link>
                            </Button>

                            <Button variant="ghost" size="icon" title="Send Reminder" disabled={cycle.status !== "active"}>
                              <Mail className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}

                    {cycle.teamMembers.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-4">
                          No team members assigned to this cycle
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="self" className="mt-6">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Team Member</TableHead>
                      <TableHead>Business Unit</TableHead>
                      <TableHead>Career Level</TableHead>
                      <TableHead className="text-center">Status</TableHead>
                      <TableHead className="text-center">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {cycle.teamMembers.map(member => (
                      <TableRow key={member.userId}>
                        <TableCell>
                          <div className="font-medium">{member.name}</div>
                          <div className="text-sm text-muted-foreground">{member.email}</div>
                        </TableCell>

                        <TableCell>{member.businessUnit || "-"}</TableCell>
                        <TableCell>{member.careerLevel || "-"}</TableCell>

                        <TableCell className="text-center">
                          <Badge variant={getStatusVariant(member.assessments.self)}>
                            {member.assessments.self.charAt(0).toUpperCase() + member.assessments.self.slice(1)}
                          </Badge>
                        </TableCell>

                        <TableCell className="text-center">
                          <div className="flex justify-center space-x-1">
                            <Button variant="ghost" size="icon" title="View Details">
                              <Link href={`/admin/assessments/cycles/${cycleId}/members/${member.userId}`}>
                                <Eye className="h-4 w-4" />
                              </Link>
                            </Button>

                            <Button variant="ghost" size="icon" title="Send Reminder" disabled={cycle.status !== "active" || member.assessments.self === "completed"}>
                              <Mail className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}

                    {cycle.teamMembers.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-4">
                          No team members assigned to this cycle
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            {cycle.assessmentTypes.manager && (
              <TabsContent value="manager" className="mt-6">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Team Member</TableHead>
                        <TableHead>Manager</TableHead>
                        <TableHead className="text-center">Status</TableHead>
                        <TableHead className="text-center">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {cycle.teamMembers.map(member => (
                        <TableRow key={member.userId}>
                          <TableCell>
                            <div className="font-medium">{member.name}</div>
                            <div className="text-sm text-muted-foreground">{member.email}</div>
                          </TableCell>

                          <TableCell>
                            {member.assessments.manager.userId ? (
                              <>
                                <div className="font-medium">{member.assessments.manager.name}</div>
                                <div className="text-sm text-muted-foreground">{member.assessments.manager.email}</div>
                              </>
                            ) : (
                              <span className="text-muted-foreground">Not assigned</span>
                            )}
                          </TableCell>

                          <TableCell className="text-center">
                            <Badge variant={getStatusVariant(member.assessments.manager.status)}>
                              {member.assessments.manager.status.charAt(0).toUpperCase() + member.assessments.manager.status.slice(1)}
                            </Badge>
                          </TableCell>

                          <TableCell className="text-center">
                            <div className="flex justify-center space-x-1">
                              <Button variant="ghost" size="icon" title="View Details">
                                <Link href={`/admin/assessments/cycles/${cycleId}/members/${member.userId}`}>
                                  <Eye className="h-4 w-4" />
                                </Link>
                              </Button>

                              <Button
                                variant="ghost"
                                size="icon"
                                title="Send Reminder"
                                disabled={
                                  cycle.status !== "active" ||
                                  member.assessments.manager.status === "completed" ||
                                  !member.assessments.manager.userId
                                }
                              >
                                <Mail className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}

                      {cycle.teamMembers.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={4} className="text-center py-4">
                            No team members assigned to this cycle
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            )}

            {cycle.assessmentTypes.peer && (
              <TabsContent value="peer" className="mt-6">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Team Member</TableHead>
                        <TableHead>Peer Reviewer</TableHead>
                        <TableHead className="text-center">Status</TableHead>
                        <TableHead className="text-center">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {cycle.teamMembers.flatMap(member =>
                        member.assessments.peers.map(peer => (
                          <TableRow key={`${member.userId}-${peer.userId}`}>
                            <TableCell>
                              <div className="font-medium">{member.name}</div>
                              <div className="text-sm text-muted-foreground">{member.email}</div>
                            </TableCell>

                            <TableCell>
                              <div className="font-medium">{peer.name}</div>
                              <div className="text-sm text-muted-foreground">{peer.email}</div>
                            </TableCell>

                            <TableCell className="text-center">
                              <Badge variant={getStatusVariant(peer.status)}>
                                {peer.status.charAt(0).toUpperCase() + peer.status.slice(1)}
                              </Badge>
                            </TableCell>

                            <TableCell className="text-center">
                              <div className="flex justify-center space-x-1">
                                <Button variant="ghost" size="icon" title="View Details">
                                  <Link href={`/admin/assessments/cycles/${cycleId}/members/${member.userId}`}>
                                    <Eye className="h-4 w-4" />
                                  </Link>
                                </Button>

                                <Button
                                  variant="ghost"
                                  size="icon"
                                  title="Send Reminder"
                                  disabled={cycle.status !== "active" || peer.status === "completed"}
                                >
                                  <Mail className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}

                      {cycle.teamMembers.length === 0 || cycle.teamMembers.every(member => member.assessments.peers.length === 0) ? (
                        <TableRow>
                          <TableCell colSpan={4} className="text-center py-4">
                            No peer reviewers assigned in this cycle
                          </TableCell>
                        </TableRow>
                      ) : null}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            )}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
