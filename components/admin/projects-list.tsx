"use client";

import { useState, useCallback, useEffect } from "react";
import { DataTable, Column } from "@/components/ui/data-table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { PlusCircle, Pencil, Users, Code, Download, Upload, Trash2 } from "lucide-react";
import Link from "next/link";
import { getAllProjects, exportProjectsToCSV, deleteProject } from "@/lib/actions/projects";
import { Badge } from "@/components/ui/badge";
import { useDebounce } from "@/lib/hooks/use-debounce";
import { Skeleton } from "@/components/ui/skeleton";
import { ProjectStatus } from "@/lib/models/project";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialog<PERSON>ontent, <PERSON>ertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import ProjectImportForm from "./project-import-form";

type Project = {
  id: string;
  name: string;
  description: string;
  businessUnit: string;
  status: ProjectStatus;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  updatedBy?: string;
};

type ProjectsApiResponse = {
  data: Project[];
  success: boolean;
  totalItems: number;
  currentPage: number;
  message?: string;
  [key: string]: any;
};

interface ProjectsListProps {
  initialData: ProjectsApiResponse;
  userEmail: string;
}

// Loading skeleton for table rows
const TableSkeleton = () => (
  <div className="space-y-3">
    {[...Array(5)].map((_, i) => (
      <div key={i} className="flex items-center space-x-4">
        <Skeleton className="h-12 w-full" />
      </div>
    ))}
  </div>
);

// Cell renderers
const NameCell = ({ project }: { project: Project }) => (
  <Link
    href={`/admin/projects/${project.id}`}
    className="font-medium hover:underline"
  >
    {project.name}
  </Link>
);

const BusinessUnitCell = ({ project }: { project: Project }) => (
  <Badge variant="outline" className="capitalize">
    {project.businessUnit}
  </Badge>
);

const StatusCell = ({ project }: { project: Project }) => {
  const statusColors = {
    active: "bg-green-100 text-green-800 border-green-200",
    completed: "bg-blue-100 text-blue-800 border-blue-200",
    planned: "bg-amber-100 text-amber-800 border-amber-200",
  };

  return (
    <Badge
      variant="outline"
      className={`capitalize ${statusColors[project.status] || ""}`}
    >
      {project.status}
    </Badge>
  );
};

const LastUpdatedCell = ({ project }: { project: Project }) => (
  <div className="text-sm">
    <div className="text-muted-foreground">
      {project.updatedAt ? new Date(project.updatedAt).toLocaleDateString() : 'N/A'}
    </div>
    {project.updatedBy && (
      <div className="text-xs text-muted-foreground">
        by {project.updatedBy}
      </div>
    )}
  </div>
);

const ActionsCell = ({ project, onDelete }: { project: Project; onDelete: (project: Project) => void }) => (
  <div className="flex space-x-2">
    <Link href={`/admin/projects/${project.id}`}>
      <Button variant="ghost" size="icon" title="Edit Project">
        <Pencil className="h-4 w-4" />
      </Button>
    </Link>
    <Link href={`/admin/projects/${project.id}/users`}>
      <Button variant="ghost" size="icon" title="Manage Team Members">
        <Users className="h-4 w-4" />
      </Button>
    </Link>
    <Link href={`/admin/projects/${project.id}/skills`}>
      <Button variant="ghost" size="icon" title="Manage Skills">
        <Code className="h-4 w-4" />
      </Button>
    </Link>
    <Button
      variant="ghost"
      size="icon"
      title="Delete Project"
      onClick={() => onDelete(project)}
      className="text-red-500 hover:text-red-700 hover:bg-red-50"
    >
      <Trash2 className="h-4 w-4" />
    </Button>
  </div>
);

export default function ProjectsList({ initialData, userEmail }: ProjectsListProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [data, setData] = useState<ProjectsApiResponse>({
    data: initialData.data ?? [],
    success: initialData.success ?? false,
    totalItems: initialData.totalItems ?? 0,
    currentPage: initialData.currentPage ?? 1,
    message: initialData.message,
  });
  const [search, setSearch] = useState<string>("");
  const debouncedSearch = useDebounce(search, 500);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize] = useState<number>(10);
  const [isExporting, setIsExporting] = useState<boolean>(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState<boolean>(false);
  const [projectToDelete, setProjectToDelete] = useState<Project | null>(null);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);

  // Define columns for the data table
  const columns: Column<Project>[] = [
    {
      header: "Name",
      accessorKey: "name",
      id: "name",
      cell: ({ row }) => <NameCell project={row.original} />,
    },
    {
      header: "Business Unit",
      accessorKey: "businessUnit",
      id: "businessUnit",
      cell: ({ row }) => <BusinessUnitCell project={row.original} />,
    },
    {
      header: "Status",
      accessorKey: "status",
      id: "status",
      cell: ({ row }) => <StatusCell project={row.original} />,
    },
    {
      header: "Last Updated",
      accessorKey: "updatedAt",
      id: "updatedAt",
      cell: ({ row }) => <LastUpdatedCell project={row.original} />,
    },
    {
      header: "Actions",
      id: "actions",
      cell: ({ row }) => <ActionsCell project={row.original} onDelete={setProjectToDelete} />,
    },
  ];

  // Function to fetch projects with pagination and search
  const fetchProjects = useCallback(
    async (page: number, search: string) => {
      setIsLoading(true);
      try {
        const result = await getAllProjects(userEmail, page, pageSize, search);
        if (result.success) {
          setData(result);
        }
      } catch (error) {
        console.error("Error fetching projects:", error);
      } finally {
        setIsLoading(false);
      }
    },
    [userEmail, pageSize]
  );

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearch(value);
    setCurrentPage(1); // Reset to first page on search
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchProjects(page, debouncedSearch);
  };

  // Fetch data when debounced search changes
  useEffect(() => {
    // Only fetch if search is defined and not empty
    if (debouncedSearch !== undefined && debouncedSearch !== "") {
      setCurrentPage(1); // Reset to first page on search
      fetchProjects(1, debouncedSearch);
    }
  }, [debouncedSearch, fetchProjects, setCurrentPage]);

  // Export CSV template
  const exportTemplate = () => {
    const csvContent = [
      [
        "project_name",
        "project_description",
        "business_unit",
        "status",
        "member_email",
        "member_name",
        "skill_category",
        "skill_description",
        "target_cl2",
        "target_cl3",
        "target_cl4",
        "target_cl5",
        "target_cl6"
      ],
      [
        "Web Portal Alpha",
        "Customer management portal",
        "web",
        "active",
        "<EMAIL>",
        "John Doe",
        "React.js",
        "Core Concepts, Hooks, State Mgmt",
        "2",
        "3",
        "4",
        "4",
        "5"
      ],
      [
        "Web Portal Alpha",
        "Customer management portal",
        "web",
        "active",
        "<EMAIL>",
        "John Doe",
        "Next.js",
        "App Router, SSR/SSG/ISR",
        "2",
        "3",
        "4",
        "4",
        "4"
      ],
      [
        "Mobile App Beta",
        "iOS/Android field app",
        "mobile",
        "planned",
        "<EMAIL>",
        "Jane Smith",
        "React Native",
        "Cross-platform development",
        "2",
        "3",
        "4",
        "4",
        "5"
      ]
    ]
      .map((row) => row.map(cell => `"${cell}"`).join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "projects-import-template.csv";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Export current projects data
  const exportCurrentData = async () => {
    setIsExporting(true);
    try {
      const result = await exportProjectsToCSV(userEmail);
      if (result.success && result.data) {
        const blob = new Blob([result.data], { type: "text/csv" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `projects-export-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } else {
        console.error("Export failed:", result.message);
      }
    } catch (error) {
      console.error("Error exporting data:", error);
    } finally {
      setIsExporting(false);
    }
  };

  // Handle import completion
  const handleImportComplete = () => {
    setIsImportDialogOpen(false);
    // Refresh the projects list
    fetchProjects(1, "");
  };

  // Handle project deletion
  const handleDeleteProject = async () => {
    if (!projectToDelete) return;

    setIsDeleting(true);
    try {
      const result = await deleteProject(projectToDelete.id, userEmail);
      if (result.success) {
        // Refresh the projects list
        fetchProjects(currentPage, debouncedSearch);
        setProjectToDelete(null);
      } else {
        console.error("Delete failed:", result.message);
        // You could add a toast notification here
      }
    } catch (error) {
      console.error("Error deleting project:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  // No need for initial data loading since we get it from the server

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Projects</CardTitle>
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={exportTemplate}
            title="Download CSV Template"
          >
            <Download className="mr-2 h-4 w-4" />
            Template
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={exportCurrentData}
            disabled={isExporting}
            title="Export Current Projects"
          >
            <Download className="mr-2 h-4 w-4" />
            {isExporting ? "Exporting..." : "Export"}
          </Button>
          <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
            <DialogTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                title="Import Projects from CSV"
              >
                <Upload className="mr-2 h-4 w-4" />
                Import
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-lg max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Import Projects from CSV</DialogTitle>
              </DialogHeader>
              <ProjectImportForm onImportComplete={handleImportComplete} />
            </DialogContent>
          </Dialog>
          <Link href="/admin/projects/new">
            <Button size="sm">
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Project
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <TableSkeleton />
        ) : (
          <DataTable
            columns={columns}
            data={data.data}
            onSearch={handleSearchChange}
            searchPlaceholder="Search projects..."
            serverSide={true}
            currentPage={data.currentPage}
            totalItems={data.totalItems}
            pageSize={pageSize}
            onPageChange={handlePageChange}
          />
        )}
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!projectToDelete} onOpenChange={() => setProjectToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Project</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the project &quot;{projectToDelete?.name}&quot;?
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground mb-2">This will:</p>
            <ul className="ml-4 list-disc text-sm text-muted-foreground space-y-1">
              <li>Remove the project permanently</li>
              <li>Remove all team member assignments</li>
              <li>Delete the project&apos;s skill template</li>
              <li>Remove project from all assessments</li>
            </ul>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteProject}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? "Deleting..." : "Delete Project"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}
