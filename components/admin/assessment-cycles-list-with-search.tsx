"use client"

import { useState, use<PERSON>ffect, use<PERSON><PERSON>back } from "react"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DataTable, Column } from "@/components/ui/data-table"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  PlusCircle,
  Calendar,
  Users,
  Eye,
  Play,
  CheckCircle,
  Trash2,
  AlertTriangle,
  RefreshCw
} from "lucide-react"
import { format } from "date-fns"
import { SerializedAssessmentCycle, CycleProject } from "@/lib/models/assessment-cycle"
import { createAssessmentCycle, deleteAs<PERSON><PERSON><PERSON><PERSON>ycle, activate<PERSON><PERSON>sment<PERSON>ycle, completeAssessmentCycle, getAllAssessmentCycles } from "@/lib/actions/assessment-cycles"
import { getActiveProjects } from "@/lib/actions/projects"
import { useSession } from "next-auth/react"

interface AssessmentCyclesListProps {
  initialData: {
    success: boolean;
    message?: string;
    data: SerializedAssessmentCycle[];
    totalItems?: number;
    totalPages?: number;
    currentPage?: number;
  };
}

export default function AssessmentCyclesListWithSearch({ initialData }: AssessmentCyclesListProps) {
  const router = useRouter()
  const { data: session } = useSession()
  const [isCreating, setIsCreating] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isActivating, setIsActivating] = useState(false)
  const [isCompleting, setIsCompleting] = useState(false)
  const [cycleToDelete, setCycleToDelete] = useState<string | null>(null)
  const [cycleToActivate, setCycleToActivate] = useState<string | null>(null)
  const [cycleToComplete, setCycleToComplete] = useState<string | null>(null)
  const [message, setMessage] = useState<{ text: string; type: "success" | "error" } | null>(null)
  const [newCycleName, setNewCycleName] = useState("")
  const [newCycleDescription, setNewCycleDescription] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [data, setData] = useState(initialData)
  const [page, setPage] = useState(initialData.currentPage || 1)
  const [search, setSearch] = useState("")
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [availableProjects, setAvailableProjects] = useState<any[]>([])
  const [selectedProjects, setSelectedProjects] = useState<CycleProject[]>([])
  const [isRecurring, setIsRecurring] = useState(false)
  const [intervalMonths, setIntervalMonths] = useState(6)

  // Fetch data with pagination and search
  const fetchData = useCallback(async (page: number, searchQuery: string) => {
    if (!session?.user?.email) return

    setIsLoading(true)
    try {
      const userEmail = session.user.email
      const result = await getAllAssessmentCycles(userEmail, page, 10, searchQuery)
      setData(result)
    } catch (error) {
      console.error("Error fetching assessment cycles:", error)
    } finally {
      setIsLoading(false)
    }
  }, [session])

  // Only fetch data when session is available and component mounts
  useEffect(() => {
    if (session?.user?.email) {
      fetchData(page, search)
    }
  }, [session, fetchData, page, search])

  // Fetch active projects when dialog opens
  const fetchProjects = useCallback(async () => {
    if (!session?.user?.email) return

    try {
      const result = await getActiveProjects(session.user.email)
      if (result.success) {
        setAvailableProjects(result.data)
      }
    } catch (error) {
      console.error("Error fetching projects:", error)
    }
  }, [session])

  // When dialog opens, fetch projects
  useEffect(() => {
    if (createDialogOpen) {
      fetchProjects()
    }
  }, [createDialogOpen, fetchProjects])

  // Handle search change
  const handleSearch = (query: string) => {
    setSearch(query)
    setPage(1) // Reset to first page when searching
  }

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage)
  }

  // Handle project selection
  const handleProjectSelection = (project: any, isChecked: boolean) => {
    if (isChecked) {
      // Add project to selected projects
      setSelectedProjects(prev => [
        ...prev,
        {
          projectId: project.id,
          name: project.name,
          description: project.description,
          businessUnit: project.businessUnit
        }
      ])
    } else {
      // Remove project from selected projects
      setSelectedProjects(prev =>
        prev.filter(p => p.projectId !== project.id)
      )
    }
  }

  const handleCreateCycle = async () => {
    if (!newCycleName.trim()) {
      setMessage({ text: "Please enter a cycle name", type: "error" })
      return
    }

    setIsCreating(true)
    const userEmail = session?.user?.email || ""

    try {
      // Create cycle with name, description, projects, and interval settings
      const result = await createAssessmentCycle({
        name: newCycleName,
        description: newCycleDescription,
        // Include selected projects
        projects: selectedProjects,
        // Include interval settings
        isRecurring,
        intervalMonths,
        // Required fields
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        status: 'draft',
        createdBy: userEmail,
        createdAt: new Date(),
        updatedAt: new Date(),
        updatedBy: userEmail,
        // Default assessment types
        assessmentTypes: {
          self: true,
          manager: false,
          peer: false,
          peerReviewsPerUser: 0
        },
        // Empty team members array
        teamMembers: []
      }, userEmail)

      if (result.success) {
        setMessage({ text: result.message || "Assessment cycle created successfully", type: "success" })
        setNewCycleName("")
        setNewCycleDescription("")
        setSelectedProjects([])
        // Close the dialog
        setCreateDialogOpen(false)

        // Refresh the data immediately
        if (session?.user?.email) {
          await fetchData(1, "")
        }

        // Reset pagination and search
        setPage(1)
        setSearch("")

        // Navigate to the new cycle
        if (result.cycleId) {
          router.push(`/admin/assessments/cycles/${result.cycleId}`)
        }
      } else {
        setMessage({ text: result.message || "Failed to create assessment cycle", type: "error" })
      }
    } catch (error) {
      console.error("Error creating assessment cycle:", error)
      setMessage({ text: "An error occurred while creating the assessment cycle", type: "error" })
    } finally {
      setIsCreating(false)
    }
  }

  const handleDeleteCycle = async () => {
    if (!cycleToDelete) return

    setIsDeleting(true)
    const userEmail = session?.user?.email || ""

    try {
      const result = await deleteAssessmentCycle(cycleToDelete, userEmail)

      if (result.success) {
        setMessage({ text: result.message || "Assessment cycle deleted successfully", type: "success" })

        // Refresh the data immediately
        if (session?.user?.email) {
          await fetchData(1, "")
        }

        // Reset pagination and search
        setPage(1)
        setSearch("")
      } else {
        setMessage({ text: result.message || "Failed to delete assessment cycle", type: "error" })
      }
    } catch (error) {
      console.error("Error deleting assessment cycle:", error)
      setMessage({ text: "An error occurred while deleting the assessment cycle", type: "error" })
    } finally {
      setIsDeleting(false)
      setCycleToDelete(null)
    }
  }

  const handleActivateCycle = async () => {
    if (!cycleToActivate) return

    setIsActivating(true)
    const userEmail = session?.user?.email || ""

    try {
      const result = await activateAssessmentCycle(cycleToActivate, userEmail)

      if (result.success) {
        setMessage({ text: result.message || "Assessment cycle activated successfully", type: "success" })

        // Refresh the data immediately
        if (session?.user?.email) {
          await fetchData(1, "")
        }

        // Reset pagination and search
        setPage(1)
        setSearch("")
      } else {
        setMessage({ text: result.message || "Failed to activate assessment cycle", type: "error" })
      }
    } catch (error) {
      console.error("Error activating assessment cycle:", error)
      setMessage({ text: "An error occurred while activating the assessment cycle", type: "error" })
    } finally {
      setIsActivating(false)
      setCycleToActivate(null)
    }
  }

  const handleCompleteCycle = async () => {
    if (!cycleToComplete) return

    setIsCompleting(true)
    const userEmail = session?.user?.email || ""

    try {
      const result = await completeAssessmentCycle(cycleToComplete, userEmail)

      if (result.success) {
        setMessage({ text: result.message || "Assessment cycle completed successfully", type: "success" })

        // Refresh the data immediately
        if (session?.user?.email) {
          await fetchData(1, "")
        }

        // Reset pagination and search
        setPage(1)
        setSearch("")
      } else {
        setMessage({ text: result.message || "Failed to complete assessment cycle", type: "error" })
      }
    } catch (error) {
      console.error("Error completing assessment cycle:", error)
      setMessage({ text: "An error occurred while completing the assessment cycle", type: "error" })
    } finally {
      setIsCompleting(false)
      setCycleToComplete(null)
    }
  }

  const getStatusBadge = (status: string) => {
    if (!status) {
      return <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-300">Unknown</Badge>
    }

    switch (status) {
      case 'draft':
        return <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-300">Draft</Badge>
      case 'active':
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">Active</Badge>
      case 'completed':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-300">Completed</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Define columns for the data table
  const columns: Column<SerializedAssessmentCycle>[] = [
    {
      header: "Name",
      accessorKey: "name",
      cell: ({ row }) => (
        <Link href={`/admin/assessments/cycles/${row.original.id}`} className="font-medium hover:underline">
          {row.original.name}
        </Link>
      )
    },
    {
      header: "Status",
      accessorKey: "status",
      cell: ({ row }) => getStatusBadge(row.original.status || '')
    },
    {
      header: "Period",
      accessorKey: "startDate",
      cell: ({ row }) => {
        const cycle = row.original;
        if (!cycle.startDate || !cycle.endDate) {
          return <span className="text-muted-foreground">Not set</span>
        }

        return (
          <div className="flex flex-col">
            <span className="text-sm">
              {format(new Date(cycle.startDate), "MMM d, yyyy")}
            </span>
            <span className="text-xs text-muted-foreground">
              to {format(new Date(cycle.endDate), "MMM d, yyyy")}
            </span>
          </div>
        )
      }
    },
    {
      header: "Team Members",
      accessorKey: "teamMembers",
      cell: ({ row }) => {
        const cycle = row.original;
        const count = Array.isArray(cycle.teamMembers) ? cycle.teamMembers.length : 0

        return (
          <div className="flex items-center">
            <Users className="h-4 w-4 mr-2 text-muted-foreground" />
            <span>{count}</span>
          </div>
        )
      }
    },
    {
      header: "Completion",
      accessorKey: "completionRate",
      cell: ({ row }) => {
        const cycle = row.original;
        // Default to 0 if completionRate is undefined or null
        const completionRate = typeof cycle.completionRate === 'number' ? cycle.completionRate : 0

        return (
          <div className="w-full bg-muted rounded-full h-2.5 dark:bg-gray-700">
            <div
              className="bg-primary h-2.5 rounded-full"
              style={{ width: `${completionRate}%` }}
            ></div>
            <span className="text-xs text-muted-foreground mt-1 block">{completionRate}%</span>
          </div>
        )
      }
    },
    {
      header: "Assessment Types",
      accessorKey: "assessmentTypes",
      cell: ({ row }) => {
        const cycle = row.original;
        // Check if assessmentTypes exists
        if (!cycle.assessmentTypes) {
          return <span className="text-muted-foreground">None</span>
        }

        return (
          <div className="flex flex-wrap gap-1">
            {cycle.assessmentTypes.self && (
              <Badge variant="outline" className="text-xs">Self</Badge>
            )}
            {cycle.assessmentTypes.manager && (
              <Badge variant="outline" className="text-xs">Manager</Badge>
            )}
            {cycle.assessmentTypes.peer && (
              <Badge variant="outline" className="text-xs">
                Peer ({cycle.assessmentTypes.peerReviewsPerUser || 0})
              </Badge>
            )}
            {!cycle.assessmentTypes.self && !cycle.assessmentTypes.manager && !cycle.assessmentTypes.peer && (
              <span className="text-muted-foreground">None</span>
            )}
          </div>
        )
      }
    },
    {
      header: "Last Updated",
      accessorKey: "updatedAt",
      cell: ({ row }) => {
        const cycle = row.original;
        return (
          <div className="text-sm">
            <div className="text-muted-foreground">
              {cycle.updatedAt ? new Date(cycle.updatedAt).toLocaleDateString() : 'N/A'}
            </div>
            {cycle.updatedBy && (
              <div className="text-xs text-muted-foreground">
                by {cycle.updatedBy}
              </div>
            )}
          </div>
        );
      }
    },
    {
      header: "Actions",
      accessorKey: "id",
      cell: ({ row }) => {
        const cycle = row.original;
        return (
          <div className="flex space-x-1">
            <Link href={`/admin/assessments/cycles/${cycle.id}`}>
              <Button variant="ghost" size="icon" title="View Details">
                <Eye className="h-4 w-4" />
              </Button>
            </Link>

            {cycle.status === 'draft' && (
              <Button
                variant="ghost"
                size="icon"
                title="Activate Cycle"
                onClick={() => setCycleToActivate(cycle.id)}
              >
                <Play className="h-4 w-4 text-green-500" />
              </Button>
            )}

            {cycle.status === 'active' && (
              <Button
                variant="ghost"
                size="icon"
                title="Complete Cycle"
                onClick={() => setCycleToComplete(cycle.id)}
              >
                <CheckCircle className="h-4 w-4 text-blue-500" />
              </Button>
            )}

            {/* Delete button available for all cycle statuses */}
            <Button
              variant="ghost"
              size="icon"
              title="Delete Cycle"
              onClick={() => setCycleToDelete(cycle.id)}
            >
              <Trash2 className="h-4 w-4 text-red-500" />
            </Button>
          </div>
        );
      }
    }
  ]

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h1 className="text-3xl font-bold">Assessment Cycles</h1>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => fetchData(page, search)}
            disabled={isLoading}
            title="Refresh data"
            className="ml-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setCreateDialogOpen(true)}>
              <PlusCircle className="mr-2 h-4 w-4" />
              New Assessment Cycle
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Assessment Cycle</DialogTitle>
              <DialogDescription>
                Create a new assessment cycle with just a name and description. All other settings will be configured automatically.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Cycle Name <span className="text-red-500">*</span></Label>
                <Input
                  id="name"
                  placeholder="e.g., Q2 2024 Performance Assessment"
                  value={newCycleName}
                  onChange={(e) => setNewCycleName(e.target.value)}
                />
                <p className="text-xs text-muted-foreground">Give your assessment cycle a clear, descriptive name</p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description (Optional)</Label>
                <Input
                  id="description"
                  placeholder="e.g., Quarterly assessment for all team members"
                  value={newCycleDescription}
                  onChange={(e) => setNewCycleDescription(e.target.value)}
                />
                <p className="text-xs text-muted-foreground">Add details about the purpose of this assessment cycle</p>
              </div>

              {/* Project Selection */}
              <div className="space-y-2">
                <Label htmlFor="projects">Select Projects to Include</Label>
                <div className="border rounded-md p-3 max-h-40 overflow-y-auto">
                  {availableProjects.length > 0 ? (
                    <div className="space-y-2">
                      {availableProjects.map(project => (
                        <div key={project.id} className="flex items-start space-x-2">
                          <Checkbox
                            id={`project-${project.id}`}
                            checked={selectedProjects.some(p => p.projectId === project.id)}
                            onCheckedChange={(checked) =>
                              handleProjectSelection(project, checked === true)
                            }
                          />
                          <div className="grid gap-1.5 leading-none">
                            <label
                              htmlFor={`project-${project.id}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {project.name}
                            </label>
                            {project.businessUnit && (
                              <p className="text-xs text-muted-foreground">
                                {project.businessUnit}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground text-center py-2">
                      No active projects found. <Link href="/admin/projects" className="text-blue-600 hover:underline">Create projects</Link> first.
                    </p>
                  )}
                </div>
                <p className="text-xs text-muted-foreground">
                  Select the projects that should be included in this assessment cycle.
                  Project skills will be available for assessment.
                </p>
              </div>

              {/* Interval Settings */}
              <div className="space-y-2 border-t pt-4 mt-4">
                <div className="flex items-start space-x-2">
                  <Checkbox
                    id="isRecurring"
                    checked={isRecurring}
                    onCheckedChange={(checked) => setIsRecurring(checked === true)}
                  />
                  <div>
                    <label
                      htmlFor="isRecurring"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Recurring Assessment Cycle
                    </label>
                    <p className="text-xs text-muted-foreground mt-1">
                      Enable this to set an interval between assessments. Users will only be able to take assessments at the specified interval.
                    </p>
                  </div>
                </div>

                {isRecurring && (
                  <div className="mt-2 pl-6">
                    <Label htmlFor="intervalMonths">Assessment Interval (months)</Label>
                    <div className="flex items-center gap-2 mt-1">
                      <Input
                        id="intervalMonths"
                        type="number"
                        min={1}
                        max={24}
                        value={intervalMonths}
                        onChange={(e) => setIntervalMonths(parseInt(e.target.value) || 6)}
                        className="w-20"
                      />
                      <span className="text-sm">months</span>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Users will only be able to take a new assessment after this many months have passed since their last assessment.
                    </p>
                  </div>
                )}
              </div>

              <div className="mt-4 p-3 bg-blue-50 border border-blue-100 rounded-md">
                <h4 className="text-sm font-medium text-blue-800 mb-1">What happens next?</h4>
                <ol className="text-xs text-blue-700 space-y-1 pl-5 list-decimal">
                  <li>After creating the cycle, it will be in &quot;Draft&quot; status</li>
                  <li>Click the green &quot;Play&quot; button to activate it</li>
                  <li>Once activated, all users will see it in their dashboard</li>
                </ol>
              </div>
            </div>
            <DialogFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => setCreateDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateCycle}
                disabled={isCreating || !newCycleName.trim()}
              >
                {isCreating ? (
                  <>
                    <div className="h-4 w-4 rounded-full border-2 border-current border-r-transparent animate-spin mr-2"></div>
                    Creating...
                  </>
                ) : (
                  "Create Cycle"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {message && (
        <div
          className={`p-4 rounded-lg ${message.type === "success"
            ? "bg-green-50 text-green-800 border border-green-200"
            : "bg-red-50 text-red-800 border border-red-200"
            }`}
        >
          {message.text}
        </div>
      )}

      {/* Admin Guide Card */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg text-blue-800">Assessment Cycle Quick Guide</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-800 font-bold text-sm">1</div>
              <div>
                <h3 className="font-medium text-blue-800">Create a New Assessment Cycle</h3>
                <p className="text-sm text-blue-700">Click &quot;New Assessment Cycle&quot; and give it a name. This creates a draft cycle.</p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-800 font-bold text-sm">2</div>
              <div>
                <h3 className="font-medium text-blue-800">Activate the Cycle</h3>
                <p className="text-sm text-blue-700">Click the green &quot;Play&quot; button to activate the cycle. This makes it available to users.</p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-800 font-bold text-sm">3</div>
              <div>
                <h3 className="font-medium text-blue-800">Complete the Cycle</h3>
                <p className="text-sm text-blue-700">When the assessment period is over, click the blue &quot;Check&quot; button to complete the cycle.</p>
              </div>
            </div>

            <div className="mt-2 pt-2 border-t border-blue-200 text-sm text-blue-700">
              <strong>Note:</strong> You don&apos;t need to manually add team members. When a cycle is activated, users will see it in their dashboard and can participate.
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Assessment Cycles</CardTitle>
          {isLoading && (
            <div className="flex items-center text-sm text-muted-foreground">
              <RefreshCw className="h-3 w-3 animate-spin mr-2" />
              Loading...
            </div>
          )}
        </CardHeader>
        <CardContent>
          {data.success ? (
            <DataTable
              data={data.data}
              columns={columns}
              searchPlaceholder="Search assessment cycles..."
              onSearch={handleSearch}
              serverSide={true}
              totalItems={data.totalItems}
              currentPage={data.currentPage}
              onPageChange={handlePageChange}
              isLoading={isLoading}
              emptyMessage="No assessment cycles found"
            />
          ) : (
            <div className="text-center py-4 text-red-500">
              {data.message || "Failed to load assessment cycles"}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!cycleToDelete} onOpenChange={(open) => !open && setCycleToDelete(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Assessment Cycle</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this assessment cycle? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center justify-center py-4">
            <AlertTriangle className="h-12 w-12 text-red-500" />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setCycleToDelete(null)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteCycle}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <div className="h-4 w-4 rounded-full border-2 border-current border-r-transparent animate-spin mr-2"></div>
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Activate Confirmation Dialog */}
      <Dialog open={!!cycleToActivate} onOpenChange={(open) => !open && setCycleToActivate(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="text-xl text-green-700">Activate Assessment Cycle</DialogTitle>
            <DialogDescription>
              Activating this cycle will make it available to all users in their dashboard.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="space-y-4">
              <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                <h3 className="text-sm font-medium text-green-800 mb-2">What happens when you activate:</h3>
                <ul className="text-sm text-green-700 space-y-2">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 mt-0.5 text-green-600" />
                    <span>The cycle status changes from &quot;Draft&quot; to &quot;Active&quot;</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 mt-0.5 text-green-600" />
                    <span>All users will see this cycle in their dashboard</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 mt-0.5 text-green-600" />
                    <span>Users can start their self-assessments immediately</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 mt-0.5 text-green-600" />
                    <span>You can track progress in the admin dashboard</span>
                  </li>
                </ul>
              </div>

              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <h3 className="text-sm font-medium text-blue-800 mb-1">No manual team assignment needed</h3>
                <p className="text-sm text-blue-700">
                  Users will automatically be able to participate when they log in. You don&apos;t need to manually assign team members.
                </p>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setCycleToActivate(null)}>
              Cancel
            </Button>
            <Button
              onClick={handleActivateCycle}
              disabled={isActivating}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {isActivating ? (
                <>
                  <div className="h-4 w-4 rounded-full border-2 border-current border-r-transparent animate-spin mr-2"></div>
                  Activating...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Activate Cycle
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Complete Confirmation Dialog */}
      <Dialog open={!!cycleToComplete} onOpenChange={(open) => !open && setCycleToComplete(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Complete Assessment Cycle</DialogTitle>
            <DialogDescription>
              Are you sure you want to mark this assessment cycle as completed? This will finalize all assessments.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setCycleToComplete(null)}>
              Cancel
            </Button>
            <Button
              onClick={handleCompleteCycle}
              disabled={isCompleting}
            >
              {isCompleting ? (
                <>
                  <div className="h-4 w-4 rounded-full border-2 border-current border-r-transparent animate-spin mr-2"></div>
                  Completing...
                </>
              ) : (
                "Complete"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
