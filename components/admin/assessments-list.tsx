"use client"

import { useState, useEffect, useCallback, useRef } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { DataTable, Column } from "@/components/ui/data-table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Eye, Download } from "lucide-react"
import { format } from "date-fns"
import { getAllAssessments } from "@/lib/actions/assessment"
import { useSession } from "next-auth/react"

interface Assessment {
  id: string;
  name: string;
  userId: string;
  reviewerId: string;
  userName: string;
  userEmail: string;
  businessUnit: string;
  careerLevel: string;
  jobRole: string;
  assessmentType: 'self' | 'manager' | 'peer';
  cycleId?: string;
  targetUserName?: string;
  status?: string;
  createdAt: string;
  updatedAt: string;
}

interface AssessmentsListProps {
  initialData: {
    success: boolean;
    message?: string;
    data: Assessment[];
    totalItems?: number;
    totalPages?: number;
    currentPage?: number;
  };
}

export default function AssessmentsList({ initialData }: AssessmentsListProps) {
  const router = useRouter()
  const { data: session } = useSession()
  const [isLoading, setIsLoading] = useState(false)
  const [data, setData] = useState(initialData)
  const [searchQuery, setSearchQuery] = useState("")
  const [filters, setFilters] = useState<{
    cycleId?: string;
    businessUnit?: string;
    assessmentType?: 'self' | 'manager' | 'peer';
  }>({})

  // Handle search
  async function handleSearch(query: string) {
    if (!session?.user?.email) return

    setSearchQuery(query) // Save the search query
    setIsLoading(true)
    try {
      const userEmail = session.user.email
      const result = await getAllAssessments(userEmail, 1, 10, query, filters)
      setData(result)
    } catch (error) {
      console.error("Error searching assessments:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // Handle page change
  async function handlePageChange(page: number) {
    if (!session?.user?.email) return

    // Don't do anything if we're already on this page
    const currentPage = data.currentPage || 1
    if (page === currentPage) return

    setIsLoading(true)
    try {
      const userEmail = session.user.email
      // Use the current search query when changing pages
      const result = await getAllAssessments(userEmail, page, 10, searchQuery, filters)
      setData(result)
    } catch (error) {
      console.error("Error changing page:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // Define columns for the data table
  const columns: Column<Assessment>[] = [
    {
      header: "Name",
      accessorKey: "name",
      cell: ({ row }) => (
        <Link href={`/admin/assessments/${row.original.id}`} className="font-medium hover:underline">
          {row.original.name}
        </Link>
      )
    },
    {
      header: "User",
      accessorKey: "userName"
    },
    {
      header: "Email",
      accessorKey: "userEmail"
    },
    {
      header: "Business Unit",
      accessorKey: "businessUnit",
      cell: ({ row }) => row.original.businessUnit || "—"
    },
    {
      header: "Career Level",
      accessorKey: "careerLevel",
      cell: ({ row }) => row.original.careerLevel || "—"
    },
    {
      header: "Type",
      accessorKey: "assessmentType",
      cell: ({ row }) => {
        const assessment = row.original;
        const type = assessment.assessmentType || 'self' // Default to 'self' if undefined
        let color = ""

        switch (type) {
          case "self":
            color = "bg-blue-100 text-blue-800 border-blue-200"
            break
          case "manager":
            color = "bg-purple-100 text-purple-800 border-purple-200"
            break
          case "peer":
            color = "bg-amber-100 text-amber-800 border-amber-200"
            break
          default:
            color = "bg-gray-100 text-gray-800 border-gray-200"
            break
        }

        return (
          <Badge variant="outline" className={color}>
            {type.charAt(0).toUpperCase() + type.slice(1)}
          </Badge>
        )
      }
    },
    {
      header: "Date",
      accessorKey: "updatedAt",
      cell: ({ row }) => {
        const assessment = row.original;
        if (!assessment.updatedAt) return "N/A"
        try {
          return format(new Date(assessment.updatedAt), "MMM d, yyyy")
        } catch (error) {
          return "Invalid date"
        }
      }
    },
    {
      header: "Actions",
      accessorKey: "id",
      cell: ({ row }) => {
        const assessment = row.original;
        return (
          <div className="flex space-x-2">
            <Link href={`/admin/assessments/${assessment.id}`}>
              <Button variant="ghost" size="icon" title="View Assessment">
                <Eye className="h-4 w-4" />
              </Button>
            </Link>
            <Button variant="ghost" size="icon" title="Export Assessment">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        );
      }
    }
  ]

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Assessments</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Assessments</CardTitle>
        </CardHeader>
        <CardContent>
          {data.success ? (
            <DataTable
              data={data.data}
              columns={columns}
              searchPlaceholder="Search assessments..."
              onSearch={handleSearch}
              serverSide={true}
              totalItems={data.totalItems}
              currentPage={data.currentPage || 1}
              onPageChange={handlePageChange}
              isLoading={isLoading}
              emptyMessage="No assessments found"
            />
          ) : (
            <div className="text-center py-4 text-red-500">
              {data.message || "Failed to load assessments"}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
