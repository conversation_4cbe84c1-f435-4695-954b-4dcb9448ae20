"use client"

import { useState } from "react"
import { Users, Code } from "lucide-react"
import { cn } from "@/lib/utils"

interface ProjectTabsProps {
  projectId: string
  projectName: string
  children: {
    details: React.ReactNode
    users: React.ReactNode
    skills: React.ReactNode
  }
}

export default function ProjectTabs({ projectId, projectName, children }: ProjectTabsProps) {
  const [activeTab, setActiveTab] = useState<'details' | 'users' | 'skills'>('details')
  
  const tabs = [
    {
      id: 'details',
      name: "Details",
      icon: null,
    },
    {
      id: 'users',
      name: "Users",
      icon: Users,
    },
    {
      id: 'skills',
      name: "Skills",
      icon: Code,
    },
  ]

  return (
    <div>
      <div className="flex space-x-4 border-b mb-6 pb-2">
        {tabs.map((tab) => {
          const isActive = activeTab === tab.id
          const Icon = tab.icon
          
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={cn(
                "flex items-center px-3 py-2 text-sm font-medium border-b-2 -mb-px",
                isActive
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              {Icon && <Icon className="mr-2 h-4 w-4" />}
              {tab.name}
            </button>
          )
        })}
      </div>

      {activeTab === 'details' && (
        <div>{children.details}</div>
      )}
      
      {activeTab === 'users' && (
        <div>{children.users}</div>
      )}
      
      {activeTab === 'skills' && (
        <div>{children.skills}</div>
      )}
    </div>
  )
}
