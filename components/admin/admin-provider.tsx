'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import { isAdmin } from '@/lib/actions/admin-actions'

// Create a context for admin status
interface AdminContextType {
  isAdmin: boolean
  isLoading: boolean
}

const AdminContext = createContext<AdminContextType>({
  isAdmin: false,
  isLoading: true
})

// Hook to use admin context
export const useAdmin = () => useContext(AdminContext)

// Provider component
export function AdminProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession()
  const [adminStatus, setAdminStatus] = useState<AdminContextType>({
    isAdmin: false,
    isLoading: true
  })

  useEffect(() => {
    const checkAdminStatus = async () => {
      // If not authenticated, not an admin
      if (status !== 'authenticated' || !session?.user?.email) {
        setAdminStatus({
          isAdmin: false,
          isLoading: false
        })
        return
      }

      try {
        // Check if user is admin
        const adminResult = await isAdmin(session.user.email)
        
        setAdminStatus({
          isAdmin: adminResult,
          isLoading: false
        })
      } catch (error) {
        console.error('Error checking admin status:', error)
        setAdminStatus({
          isAdmin: false,
          isLoading: false
        })
      }
    }

    checkAdminStatus()
  }, [session, status])

  return (
    <AdminContext.Provider value={adminStatus}>
      {children}
    </AdminContext.Provider>
  )
}
