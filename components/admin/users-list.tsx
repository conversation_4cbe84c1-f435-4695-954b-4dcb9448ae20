"use client";

import { useState, useCallback, useEffect } from "react";
import { DataTable, Column } from "@/components/ui/data-table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { PlusCircle, Upload, Pencil } from "lucide-react";
import Link from "next/link";
import { getAllUserProfiles } from "@/lib/actions/admin-users";
import { Badge } from "@/components/ui/badge";
import type { UserProfile as UserProfileBase } from "@/lib/models/user-profile";
import { useDebounce } from "@/lib/hooks/use-debounce";
import { Skeleton } from "@/components/ui/skeleton";

type User = UserProfileBase & { id?: string };

type UsersApiResponse = {
  data: User[];
  success: boolean;
  totalItems: number;
  currentPage: number;
  message?: string;
  [key: string]: any;
};

interface UsersListProps {
  initialData: UsersApiResponse;
  userEmail: string;
}

// Loading skeleton for table rows
const TableSkeleton = () => (
  <div className="space-y-3">
    {[...Array(5)].map((_, i) => (
      <div key={i} className="flex items-center space-x-4">
        <Skeleton className="h-12 w-full" />
      </div>
    ))}
  </div>
);

// Cell renderers moved outside UsersList
const NameCell = ({ user }: any) => (
  <Link
    href={`/admin/users/${user.row.original.id ?? ""}`}
    className="font-medium hover:underline"
  >
    {user.row.original.name || "—"}
  </Link>
);
const BusinessUnitCell = ({ user }: any) => {
  return user.row.original.businessUnit || "—";
};
const CareerLevelCell = ({ user }: any) => {
  return user.row.original.careerLevel || "—";
};
const JobRoleCell = ({ user }: any) => {
  return user.row.original.jobRole || "—";
};
const SourceCell = ({ user }: any) => (
  <Badge
    variant="outline"
    className={
      user.row.original.createdBy === "admin"
        ? "bg-blue-50 text-blue-700 border-blue-200"
        : "bg-purple-50 text-purple-700 border-purple-200"
    }
  >
    {user.row.original.createdBy === "admin" ? "Admin" : "Self"}
  </Badge>
);
const LastUpdatedCell = ({ user }: any) => (
  <div className="text-sm">
    <div className="text-muted-foreground">
      {user.row.original.updatedAt ? new Date(user.row.original.updatedAt).toLocaleDateString() : 'N/A'}
    </div>
    {user.row.original.updatedBy && (
      <div className="text-xs text-muted-foreground">
        by {user.row.original.updatedBy}
      </div>
    )}
  </div>
);

const ActionsCell = ({ user }: any) => (
  <div className="flex space-x-2">
    <Link href={`/admin/users/${user.row.original.id ?? ""}`}>
      <Button variant="ghost" size="icon" title="Edit User">
        <Pencil className="h-4 w-4" />
      </Button>
    </Link>
    {/* Delete button would go here */}
  </div>
);

export default function UsersList({ initialData, userEmail }: UsersListProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [data, setData] = useState<UsersApiResponse>({
    data: initialData.data ?? [],
    success: initialData.success ?? false,
    totalItems: initialData.totalItems ?? 0,
    currentPage: initialData.currentPage ?? 1,
    message: initialData.message,
  });
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [lastRequestedPage, setLastRequestedPage] = useState<number>(1);
  const debouncedSearchTerm = useDebounce(searchTerm, 500); // 500ms debounce

  const loadUsers = useCallback(
    async (page: number, search: string): Promise<void> => {
      // Only prevent loading if already loading
      if (isLoading) return;

      // Always update the last requested page
      setIsLoading(true);
      setLastRequestedPage(page);

      try {
        console.log(`⏳ Loading users with search: "${search}", page: ${page}`);
        const result = await getAllUserProfiles(userEmail, page, 10, search);
        if (!result) throw new Error("No result returned");

        console.log(`✅ Loaded ${result.data?.length || 0} users for search: "${search}"`);
        setData((prev) => ({
          data: Array.isArray(result.data) ? result.data : prev.data,
          success: Boolean(result.success),
          totalItems: Number(result.totalItems) || prev.totalItems,
          currentPage: Number(result.currentPage) || prev.currentPage,
          message: result.message,
        }));
      } catch (error) {
        console.error("❌ Error loading users:", error);
        setData((prev) => ({
          ...prev,
          success: false,
          message: "Failed to load user data",
        }));
      } finally {
        setIsLoading(false);
      }
    },
    [isLoading, userEmail]
  );

  // We don't need this effect anymore since we're handling search directly in handleSearch

  const handleSearch = useCallback((query: string): void => {
    console.log("🔍 Search term changed:", query);
    setSearchTerm(query);

    // Reset to page 1 for new searches
    setLastRequestedPage(1);

    // Only search if term is at least 2 chars or empty (to show all)
    if (query.length >= 2 || query.length === 0) {
      console.log(`🔍 Searching for: "${query}"`);
      loadUsers(1, query);
    }
  }, [loadUsers, setLastRequestedPage]);

  const handlePageChange = useCallback(
    (page: number): void => {
      if (page === data.currentPage || isLoading || page === lastRequestedPage)
        return;
      loadUsers(page, searchTerm);
    },
    [data.currentPage, isLoading, lastRequestedPage, loadUsers, searchTerm]
  );

  // Define columns for the data table
  const columns: Column<User>[] = [
    {
      header: "Name",
      accessorKey: "name",
      cell: (user) => <NameCell user={user} />,
    },
    {
      header: "Email",
      accessorKey: "email",
    },
    {
      header: "Business Unit",
      accessorKey: "businessUnit",
      cell: (user) => user.row.original.businessUnit || "—",
    },
    {
      header: "Career Level",
      accessorKey: "careerLevel",
      cell: (user) => user.row.original.careerLevel || "—",
    },
    {
      header: "Job Role",
      accessorKey: "jobRole",
      cell: (user) => user.row.original.jobRole || "—",
    },
    {
      header: "Source",
      accessorKey: "createdBy",
      cell: (user) => <SourceCell user={user} />,
    },
    {
      header: "Last Updated",
      accessorKey: "updatedAt",
      cell: (user) => <LastUpdatedCell user={user} />,
    },
    {
      header: "Actions",
      accessorKey: "id",
      cell: (user) => <ActionsCell user={user} />,
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Users</h1>
        <div className="flex gap-2">
          <Link href="/admin/users/import">
            <Button variant="outline">
              <Upload className="mr-2 h-4 w-4" />
              Import Users
            </Button>
          </Link>
          <Link href="/admin/users/new">
            <Button>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add User
            </Button>
          </Link>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Users</CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            {data && data.success ? (
              <div className="transition-opacity duration-200 ease-in-out">
                <DataTable
                  data={Array.isArray(data.data) ? data.data : []}
                  columns={columns}
                  searchPlaceholder="Search users... (min. 2 characters)"
                  onSearch={handleSearch}
                  serverSide={true}
                  totalItems={
                    typeof data.totalItems === "number" ? data.totalItems : 0
                  }
                  currentPage={
                    typeof data.currentPage === "number" ? data.currentPage : 1
                  }
                  onPageChange={handlePageChange}
                  isLoading={isLoading}
                  emptyMessage="No users found"
                />
              </div>
            ) : (
              <div className="text-center py-4 text-red-500">
                {data?.message ?? "Failed to load user data"}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
