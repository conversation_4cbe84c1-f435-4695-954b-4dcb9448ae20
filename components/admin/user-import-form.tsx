"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { importUsersManually, importUsersFromFile } from "@/lib/actions/admin-users"
import { Upload as FileUploadIcon, Users, Database, Upload, Loader2 } from "lucide-react"
import { FileUpload } from "@/components/ui/file-upload"

export default function UserImportForm() {
  const router = useRouter()
  const { data: session } = useSession()
  const [isUploading, setIsUploading] = useState(false)
  const [message, setMessage] = useState<{ text: string; type: "success" | "error" } | null>(null)
  const [manualUsers, setManualUsers] = useState("")
  const [selectedFile, setSelectedFile] = useState<File | null>(null)

  const handleManualImport = async () => {
    if (!session?.user?.email || !manualUsers.trim()) {
      return
    }

    setIsUploading(true)

    try {
      // Parse the JSON input
      const users = JSON.parse(manualUsers)
      const result = await importUsersManually(users, session.user.email)

      if (result.success) {
        setMessage({ text: result.message || "Users imported successfully", type: "success" })
        setManualUsers("")
        router.refresh()
      } else {
        setMessage({ text: result.message || "Failed to import users", type: "error" })
      }
    } catch (error) {
      setMessage({ text: "Invalid JSON format", type: "error" })
    } finally {
      setIsUploading(false)
    }
  }

  const handleFileUpload = async () => {
    if (!session?.user?.email || !selectedFile) {
      return
    }

    setIsUploading(true)

    try {
      // Read file content
      const fileContent = await readFileContent(selectedFile)

      // Determine file type
      const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase()
      const fileType = fileExtension === 'json' ? 'json' : 'csv'

      // Import users
      const result = await importUsersFromFile(fileContent, fileType, session.user.email)

      if (result.success) {
        setMessage({ text: result.message || "Users imported successfully", type: "success" })
        setSelectedFile(null)
        router.refresh()
      } else {
        setMessage({ text: result.message || "Failed to import users", type: "error" })
      }
    } catch (error) {
      console.error("Error importing users from file:", error)
      setMessage({ text: "Failed to process file", type: "error" })
    } finally {
      setIsUploading(false)
    }
  }

  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()

      reader.onload = (event) => {
        if (event.target?.result) {
          resolve(event.target.result as string)
        } else {
          reject(new Error('Failed to read file'))
        }
      }

      reader.onerror = () => {
        reject(new Error('Failed to read file'))
      }

      reader.readAsText(file)
    })
  }

  return (
    <div className="space-y-6">
      {message && (
        <div
          className={`p-4 rounded-lg border ${message.type === "success" ? "bg-green-50 border-green-200 text-green-800" : "bg-red-50 border-red-200 text-red-800"
            }`}
        >
          {message.text}
        </div>
      )}

      <Tabs defaultValue="file">
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="file">
            <Upload className="mr-2 h-4 w-4" />
            File Upload
          </TabsTrigger>
          <TabsTrigger value="manual">
            <Users className="mr-2 h-4 w-4" />
            Manual Entry
          </TabsTrigger>
        </TabsList>

        <TabsContent value="file">
          <Card className="p-6">
            <h2 className="text-lg font-medium mb-4">Upload Users File</h2>

            <div className="space-y-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium mb-1">
                  Upload CSV or JSON File
                </label>
                <p className="text-xs text-muted-foreground">
                  Upload a CSV or JSON file containing user data. CSV files must include &quot;email&quot; and &quot;name&quot; columns.
                </p>

                <FileUpload
                  accept=".csv,.json"
                  maxSize={5}
                  onFileSelect={setSelectedFile}
                  disabled={isUploading}
                />
              </div>

              <div className="space-y-2">
                <h4 className="text-xs font-medium">CSV Format Example:</h4>
                <pre className="text-xs bg-muted p-2 rounded-md overflow-x-auto">
                  name,email,businessUnit,careerLevel,jobRole<br />
                  John Doe,<EMAIL>,Web,CL2,Software Engineer<br />
                  Jane Smith,<EMAIL>,Mobile,CL3,Senior Developer
                </pre>
              </div>

              <Button
                onClick={handleFileUpload}
                disabled={isUploading || !selectedFile}
                className="w-full"
              >
                {isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Importing...
                  </>
                ) : (
                  "Import Users"
                )}
              </Button>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="manual">
          <Card className="p-6">
            <h2 className="text-lg font-medium mb-4">Manual User Entry</h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  Enter Users (JSON format)
                </label>
                <textarea
                  value={manualUsers}
                  onChange={(e) => setManualUsers(e.target.value)}
                  rows={10}
                  className="w-full p-2 border rounded-md font-mono text-sm"
                  placeholder={`[\n  {\n    "email": "<EMAIL>",\n    "name": "User Name",\n    "businessUnit": "web",\n    "careerLevel": "CL3",\n    "jobRole": "Software Engineer",\n    "managerEmail": "<EMAIL>"\n  }\n]`}
                />
              </div>

              <Button
                onClick={handleManualImport}
                disabled={isUploading || !manualUsers.trim()}
                className="w-full"
              >
                {isUploading ? "Importing..." : "Import Users"}
              </Button>
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-blue-800 font-medium flex items-center">
          <Database className="h-5 w-5 mr-2" />
          JSON Format Requirements
        </h3>
        <ul className="mt-2 space-y-1 text-sm text-blue-700">
          <li>• Required fields: email, name</li>
          <li>• Optional fields: businessUnit, careerLevel, jobRole, managerEmail</li>
          <li>• Business units should match your defined values (web, mobile, etc.)</li>
          <li>• Career levels should match your defined values (CL1, CL2, etc.)</li>
        </ul>
      </div>
    </div>
  )
}
