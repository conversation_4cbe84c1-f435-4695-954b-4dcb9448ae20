"use client"

import { useState } from "react"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DataTable, Column } from "@/components/ui/data-table"
import { Badge } from "@/components/ui/badge"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { businessUnitOptions } from "@/lib/models/skill-template"
import { deleteBUSkillTemplate } from "@/lib/actions/admin-skills"
import { PlusCircle, Edit, Calendar, Trash2, <PERSON><PERSON><PERSON>rian<PERSON>, Download, Upload } from "lucide-react"

interface AdminSkillsListProps {
  templates: any[]
  businessUnitNames: Record<string, string>
}

type SkillTemplate = {
  id: string;
  businessUnit: string;
  name: string;
  coreSkills: any[];
  updatedAt: string;
  updatedBy: string;
};

// Cell renderers
const NameCell = ({ template }: { template: SkillTemplate }) => (
  <Link
    href={`/admin/skills/${template.businessUnit}`}
    className="font-medium hover:underline"
  >
    {template.name}
  </Link>
);

const BusinessUnitCell = ({ template }: { template: SkillTemplate }) => (
  <Badge variant="outline" className="capitalize">
    {template.businessUnit}
  </Badge>
);

const SkillsCountCell = ({ template }: { template: SkillTemplate }) => (
  <span className="text-sm text-muted-foreground">
    {template.coreSkills?.length || 0} skills
  </span>
);

const LastUpdatedCell = ({ template }: { template: SkillTemplate }) => (
  <div className="text-sm">
    <div className="text-muted-foreground">
      {new Date(template.updatedAt).toLocaleDateString()}
    </div>
    <div className="text-xs text-muted-foreground">
      by {template.updatedBy}
    </div>
  </div>
);

const ActionsCell = ({ template, onDelete }: { template: SkillTemplate; onDelete: (template: SkillTemplate) => void }) => (
  <div className="flex space-x-2">
    <Link href={`/admin/skills/${template.businessUnit}`}>
      <Button variant="ghost" size="icon" title="Edit Template">
        <Edit className="h-4 w-4" />
      </Button>
    </Link>
    <Button
      variant="ghost"
      size="icon"
      title="Delete Template"
      onClick={() => onDelete(template)}
      className="text-red-500 hover:text-red-700 hover:bg-red-50"
    >
      <Trash2 className="h-4 w-4" />
    </Button>
  </div>
);

export default function AdminSkillsList({ templates, businessUnitNames }: AdminSkillsListProps) {
  const router = useRouter()
  const { data: session } = useSession()
  const [isCreating, setIsCreating] = useState(false)
  const [selectedBU, setSelectedBU] = useState("")
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [templateToDelete, setTemplateToDelete] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [deleteError, setDeleteError] = useState<string | null>(null)
  const [isExporting, setIsExporting] = useState(false)
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isImporting, setIsImporting] = useState(false)
  const [importMessage, setImportMessage] = useState<{ text: string; type: "success" | "error" } | null>(null)

  // Define columns for the data table
  const columns: Column<SkillTemplate>[] = [
    {
      header: "Template Name",
      accessorKey: "name",
      id: "name",
      cell: ({ row }) => <NameCell template={row.original} />,
    },
    {
      header: "Business Unit",
      accessorKey: "businessUnit",
      id: "businessUnit",
      cell: ({ row }) => <BusinessUnitCell template={row.original} />,
    },
    {
      header: "Core Skills",
      id: "coreSkills",
      cell: ({ row }) => <SkillsCountCell template={row.original} />,
    },
    {
      header: "Last Updated",
      accessorKey: "updatedAt",
      id: "updatedAt",
      cell: ({ row }) => <LastUpdatedCell template={row.original} />,
    },
    {
      header: "Actions",
      id: "actions",
      cell: ({ row }) => <ActionsCell template={row.original} onDelete={(template) => {
        setTemplateToDelete(template.businessUnit);
        setDeleteDialogOpen(true);
      }} />,
    },
  ];

  // Find business units that don't have templates yet
  const availableBUs = businessUnitOptions.filter(
    bu => !templates.some(template => template.businessUnit === bu)
  )

  const handleCreateTemplate = (bu: string) => {
    router.push(`/admin/skills/${bu}`)
  }

  const handleDeleteClick = (businessUnit: string) => {
    setTemplateToDelete(businessUnit)
    setDeleteDialogOpen(true)
    setDeleteError(null)
  }

  const handleDeleteConfirm = async () => {
    if (!templateToDelete || !session?.user?.email) return

    setIsDeleting(true)
    setDeleteError(null)

    try {
      const result = await deleteBUSkillTemplate(templateToDelete, session.user.email)

      if (result.success) {
        setDeleteDialogOpen(false)
        // Refresh the page to show updated templates
        router.refresh()
      } else {
        setDeleteError(result.message || "Failed to delete template")
      }
    } catch (error) {
      setDeleteError("An unexpected error occurred")
      console.error("Error deleting template:", error)
    } finally {
      setIsDeleting(false)
    }
  }

  // Export CSV template
  const exportTemplate = () => {
    const csvContent = [
      [
        "business_unit",
        "template_name",
        "skill_category",
        "skill_description",
        "target_cl2",
        "target_cl3",
        "target_cl4",
        "target_cl5",
        "target_cl6"
      ],
      [
        "web",
        "Web Engineering",
        "Front-End Development",
        "HTML, CSS, JavaScript Fundamentals, Framework Basics",
        "2",
        "3",
        "4",
        "4",
        "4"
      ],
      [
        "web",
        "Web Engineering",
        "Back-End Development",
        "Server-side Scripting, Database Concepts, API Basics",
        "1",
        "3",
        "4",
        "4",
        "4"
      ],
      [
        "web",
        "Web Engineering",
        "Testing & Quality Assurance",
        "Unit Testing, Integration Testing, Code Quality",
        "2",
        "3",
        "4",
        "4",
        "4"
      ],
      [
        "mobile",
        "Mobile Engineering",
        "Mobile Development",
        "iOS/Android Development, Cross-platform Frameworks",
        "2",
        "3",
        "4",
        "4",
        "5"
      ],
      [
        "mobile",
        "Mobile Engineering",
        "UI/UX Design",
        "Mobile UI Patterns, User Experience Design",
        "2",
        "3",
        "4",
        "4",
        "4"
      ]
    ]
      .map((row) => row.map(cell => `"${cell}"`).join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "core-competencies-template.csv";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Export current templates data
  const exportCurrentData = async () => {
    if (!session?.user?.email) return;

    setIsExporting(true);
    try {
      // Placeholder - will implement later
      console.log("Export functionality to be implemented");
    } catch (error) {
      console.error("Error exporting data:", error);
    } finally {
      setIsExporting(false);
    }
  };

  // Handle file upload
  const handleFileUpload = async () => {
    if (!session?.user?.email || !selectedFile) return;

    setIsImporting(true);
    setImportMessage(null);

    try {
      // Placeholder - will implement later
      console.log("Import functionality to be implemented");
      setImportMessage({ text: "Import functionality coming soon!", type: "success" });
    } catch (error) {
      console.error("Error importing templates:", error);
      setImportMessage({ text: "Failed to process file", type: "error" });
    } finally {
      setIsImporting(false);
    }
  };

  // Read file content
  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = (e) => reject(e);
      reader.readAsText(file);
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Core Skills Templates</h2>
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={exportTemplate}
            title="Download CSV Template"
          >
            <Download className="mr-2 h-4 w-4" />
            Template
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={exportCurrentData}
            disabled={isExporting}
            title="Export Current Templates"
          >
            <Download className="mr-2 h-4 w-4" />
            {isExporting ? "Exporting..." : "Export"}
          </Button>
          <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
            <DialogTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                title="Import Templates from CSV"
              >
                <Upload className="mr-2 h-4 w-4" />
                Import
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-lg max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Import Core Skills Templates from CSV</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium">
                    Upload CSV File
                  </label>
                  <p className="text-xs text-muted-foreground">
                    Upload a CSV file with core skills templates data.
                  </p>
                  <input
                    type="file"
                    accept=".csv"
                    onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                </div>
                {importMessage && (
                  <div className={`p-3 rounded-md text-sm ${importMessage.type === "error"
                    ? "bg-red-50 text-red-700 border border-red-200"
                    : "bg-green-50 text-green-700 border border-green-200"
                    }`}>
                    {importMessage.text}
                  </div>
                )}
                <Button
                  onClick={handleFileUpload}
                  disabled={isImporting || !selectedFile}
                  className="w-full"
                >
                  {isImporting ? "Importing..." : "Import Core Skills"}
                </Button>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <h4 className="text-blue-800 font-medium text-sm">CSV Requirements</h4>
                  <ul className="mt-2 space-y-1 text-xs text-blue-700">
                    <li>• Required: business_unit, template_name, skill_category</li>
                    <li>• Focus on core competencies, not project-specific skills</li>
                    <li>• Download template for correct format</li>
                  </ul>
                </div>
              </div>
            </DialogContent>
          </Dialog>
          <Link href="/admin/skills/clear" passHref>
            <Button
              variant="outline"
              className="border-red-300 text-red-600 hover:bg-red-50 hover:text-red-700"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Clear Assessments
            </Button>
          </Link>
          {availableBUs.length > 0 && (
            <Button
              onClick={() => setIsCreating(!isCreating)}
              className="bg-primary hover:bg-blue-500 shadow-md border border-primary/20 font-medium transition-colors duration-200"
            >
              <PlusCircle className="mr-2 h-4 w-4" />
              Create Template
            </Button>
          )}
        </div>
      </div>

      {isCreating && (
        <Card className="p-6 bg-muted/20">
          <h3 className="text-lg font-medium mb-4">Create New Template</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {availableBUs.map(bu => (
              <Button
                key={bu}
                variant="outline"
                onClick={() => handleCreateTemplate(bu)}
                className="h-auto py-6 flex flex-col items-center justify-center gap-2 hover:bg-primary/10"
              >
                <span className="text-lg font-medium">{businessUnitNames[bu]}</span>
                <span className="text-xs text-muted-foreground">{bu}</span>
              </Button>
            ))}
          </div>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Core Skills Templates</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={templates}
            searchPlaceholder="Search templates..."
          />
        </CardContent>
      </Card>

      {templates.length === 0 && !isCreating && (
        <Card className="p-8 text-center bg-muted/10">
          <h3 className="text-lg font-medium mb-2">No Templates Found</h3>
          <p className="text-muted-foreground mb-4">
            Create skill templates for different business units to customize assessments.
          </p>
          <div className="space-y-2">
            <p className="text-xs text-muted-foreground">
              Debug info: {JSON.stringify({ templateCount: templates.length, availableBUs: availableBUs.length })}
            </p>
            <Button
              onClick={() => setIsCreating(true)}
              className="bg-primary hover:bg-blue-500 shadow-md border border-primary/20 font-medium transition-colors duration-200"
            >
              <PlusCircle className="mr-2 h-4 w-4" />
              Create First Template
            </Button>
          </div>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Delete Template
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this template? This action cannot be undone.
              <br /><br />
              <strong>Warning:</strong> Deleting this template will prevent users in this business unit from taking self-assessments.

              {deleteError && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-700">
                  {deleteError}
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault()
                handleDeleteConfirm()
              }}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              {isDeleting ? (
                <>
                  <div className="h-4 w-4 rounded-full border-2 border-white/30 border-t-white animate-spin mr-2"></div>
                  Deleting...
                </>
              ) : (
                "Delete Template"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
