"use client"

import { useState } from "react"
import { Card } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { type ProjectSkill } from "@/lib/project-skills-data"
import { ratingScale } from "@/lib/skills-data"

interface ProjectSkillAssessmentProps {
  projectSkills: ProjectSkill[]
  onSkillsChange: (skills: ProjectSkill[]) => void
  careerLevel: string
  projectName?: string
}

export default function ProjectSkillAssessment({
  projectSkills,
  onSkillsChange,
  careerLevel,
  projectName
}: ProjectSkillAssessmentProps) {
  // Initialize with project skills or empty array
  const [skills, setSkills] = useState<ProjectSkill[]>(
    projectSkills.length > 0 ? projectSkills : []
  )

  const handleSkillLevelChange = (skillId: string, level: string) => {
    const updatedSkills = skills.map((skill) =>
      skill.id === skillId ? { ...skill, currentLevel: level === "-" ? null : Number.parseInt(level) } : skill
    )
    setSkills(updatedSkills)
    onSkillsChange(updatedSkills)
  }

  const handleRemoveSkill = (skillId: string) => {
    const updatedSkills = skills.filter(skill => skill.id !== skillId)
    setSkills(updatedSkills)
    onSkillsChange(updatedSkills)
  }

  const getLevelColor = (current: number | null, target: number | undefined) => {
    if (!current || !target) return "bg-gray-500"
    if (current < target) return "bg-red-700"
    if (current === target) return "bg-amber-500"
    return "bg-green-600"
  }

  // Helper to get the appropriate target level based on career level
  const getCareerLevelKey = () => {
    if (careerLevel === "cl2") return "cl2"
    if (careerLevel === "cl3") return "cl3"
    if (careerLevel === "cl4") return "cl4"
    if (careerLevel === "tm1" || careerLevel === "tm2") return "tm12"
    if (careerLevel === "tm3" || careerLevel === "tm4") return "tm34"
    return "cl3" // Default to CL3 if unknown
  }

  return (
    <div className="space-y-6">
      <Card className="overflow-hidden shadow-md border-primary/20 bg-card">
        <div className="p-4 bg-muted/30 border-b">
          <h2 className="text-lg font-medium text-primary">Project/Tech Stack Specific Assessment</h2>
          {projectName && (
            <div className="mt-1 mb-2 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 border border-blue-200">
              Project: {projectName}
            </div>
          )}
          <p className="text-sm text-muted-foreground mt-1">
            These skills are specific to your current project and tech stack. Your Lead/Manager should define the target levels.
            The same rating scale is used as in Core Competencies: 1 (Basic) to 6 (Mastery).
          </p>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-muted/50 border-b">
                <th className="px-4 py-3 text-left font-medium text-primary/90">Skill Category</th>
                <th className="px-4 py-3 text-left font-medium text-primary/90">Skill Description/Examples</th>
                <th className="px-4 py-3 text-center font-medium text-primary/90">
                  <div className="flex flex-col items-center">
                    <span className="text-xs uppercase tracking-wider text-muted-foreground mb-1">Target</span>
                    <span>CL2</span>
                  </div>
                </th>
                <th className="px-4 py-3 text-center font-medium text-primary/90">
                  <div className="flex flex-col items-center">
                    <span className="text-xs uppercase tracking-wider text-muted-foreground mb-1">Target</span>
                    <span>CL3</span>
                  </div>
                </th>
                <th className="px-4 py-3 text-center font-medium text-primary/90">
                  <div className="flex flex-col items-center">
                    <span className="text-xs uppercase tracking-wider text-muted-foreground mb-1">Target</span>
                    <span>CL4</span>
                  </div>
                </th>
                <th className="px-4 py-3 text-center font-medium text-primary/90">
                  <div className="flex flex-col items-center">
                    <span className="text-xs uppercase tracking-wider text-muted-foreground mb-1">Target</span>
                    <span>CL5</span>
                  </div>
                </th>
                <th className="px-4 py-3 text-center font-medium text-primary/90">
                  <div className="flex flex-col items-center">
                    <span className="text-xs uppercase tracking-wider text-muted-foreground mb-1">Target</span>
                    <span>CL6</span>
                  </div>
                </th>
                <th className="px-4 py-3 text-center font-medium text-primary/90">
                  <div className="flex flex-col items-center">
                    <span className="text-xs uppercase tracking-wider text-muted-foreground mb-1">Current</span>
                    <span>Level (1-6)</span>
                  </div>
                </th>

              </tr>
            </thead>
            <tbody>
              {skills.map((skill) => {
                return (
                  <tr key={skill.id} className="border-b hover:bg-muted/10 transition-colors">
                    <td className="px-4 py-4 font-medium">{skill.category}</td>
                    <td className="px-4 py-4 text-sm text-muted-foreground">{skill.description}</td>
                    <td className="px-4 py-4 text-center">
                      <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-background shadow-md border border-white/20 ${getLevelColor(skill.currentLevel, skill.targetCL2)}`}>
                        {skill.targetCL2 || '-'}
                      </div>
                    </td>
                    <td className="px-4 py-4 text-center">
                      <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-background shadow-md border border-white/20 ${getLevelColor(skill.currentLevel, skill.targetCL3)}`}>
                        {skill.targetCL3 || '-'}
                      </div>
                    </td>
                    <td className="px-4 py-4 text-center">
                      <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-background shadow-md border border-white/20 ${getLevelColor(skill.currentLevel, skill.targetCL4)}`}>
                        {skill.targetCL4 || '-'}
                      </div>
                    </td>
                    <td className="px-4 py-4 text-center">
                      <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-background shadow-md border border-white/20 ${getLevelColor(skill.currentLevel, skill.targetCL5)}`}>
                        {skill.targetCL5 || '-'}
                      </div>
                    </td>
                    <td className="px-4 py-4 text-center">
                      <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-background shadow-md border border-white/20 ${getLevelColor(skill.currentLevel, skill.targetCL6)}`}>
                        {skill.targetCL6 || '-'}
                      </div>
                    </td>
                    <td className="px-4 py-4 text-center">
                      <Select
                        value={skill.currentLevel?.toString() || ""}
                        onValueChange={(value) => handleSkillLevelChange(skill.id, value)}
                      >
                        <SelectTrigger className="w-24 mx-auto bg-card border-primary/20 focus:ring-primary/20">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="-">Not Assessed</SelectItem>
                          {[1, 2, 3, 4, 5, 6].map((level) => {
                            const levelColors = {
                              1: 'text-red-700',
                              2: 'text-orange-600',
                              3: 'text-amber-500',
                              4: 'text-green-600',
                              5: 'text-blue-600',
                              6: 'text-purple-700'
                            };
                            const textColor = levelColors[level as keyof typeof levelColors];

                            return (
                              <SelectItem key={level} value={level.toString()}>
                                <span className="flex items-center gap-2">
                                  <span className={`font-semibold ${textColor}`}>{level}</span>
                                  <span>- {ratingScale[level as keyof typeof ratingScale]}</span>
                                </span>
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    </td>

                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  )
}
