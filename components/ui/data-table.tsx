"use client";

import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { SearchInput } from "@/components/ui/search-input";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { ChevronDown, ChevronUp } from "lucide-react";
import { Button } from "@/components/ui/button";

export interface Column<T> {
  header: string;
  accessorKey?: string;
  id?: string;
  cell?: (item: { row: { original: T } }) => React.ReactNode;
  sortable?: boolean;
  searchable?: boolean;
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  searchPlaceholder?: string;
  pageSize?: number;
  onSearch?: (query: string) => void;
  serverSide?: boolean;
  totalItems?: number;
  currentPage?: number;
  onPageChange?: (page: number) => void;
  isLoading?: boolean;
  emptyMessage?: string;
}

export function DataTable<T>({
  data,
  columns,
  searchPlaceholder = "Search...",
  pageSize = 10,
  onSearch,
  serverSide = false,
  totalItems,
  currentPage: externalCurrentPage,
  onPageChange,
  isLoading = false,
  emptyMessage = "No data found",
}: DataTableProps<T>) {
  const [searchQuery, setSearchQuery] = useState("");
  // For client-side pagination only
  const [clientPage, setClientPage] = useState(1);
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

  // Use external page for server-side, internal for client-side
  const currentPage = serverSide ? externalCurrentPage || 1 : clientPage;

  // Filter data based on search query (client-side only)
  const filteredData =
    !serverSide && searchQuery
      ? data.filter((item) => {
        return columns
          .filter((col) => col.searchable !== false && col.accessorKey)
          .some((column) => {
            const value = column.accessorKey ? (item as any)[column.accessorKey] : null;
            return (
              value &&
              value
                .toString()
                .toLowerCase()
                .includes(searchQuery.toLowerCase())
            );
          });
      })
      : data;

  // Sort data (client-side only)
  const sortedData =
    !serverSide && sortColumn
      ? [...filteredData].sort((a, b) => {
        const aValue = (a as any)[sortColumn];
        const bValue = (b as any)[sortColumn];

        if (aValue === bValue) return 0;

        // Handle null/undefined values
        if (aValue === null || aValue === undefined)
          return sortDirection === "asc" ? -1 : 1;
        if (bValue === null || bValue === undefined)
          return sortDirection === "asc" ? 1 : -1;

        // Sort strings
        if (typeof aValue === "string" && typeof bValue === "string") {
          return sortDirection === "asc"
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }

        // Sort numbers
        return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
      })
      : filteredData;

  // Calculate pagination
  const totalPages = serverSide
    ? Math.ceil((totalItems || 0) / pageSize)
    : Math.ceil(sortedData.length / pageSize);

  const paginatedData = !serverSide
    ? sortedData.slice((currentPage - 1) * pageSize, currentPage * pageSize)
    : sortedData;

  // Handle search
  const handleSearch = (query: string) => {
    // For client-side filtering
    setSearchQuery(query);

    if (serverSide) {
      // For server-side, just call the parent handler
      if (onSearch) {
        onSearch(query);
      }
    } else {
      // For client-side, reset to first page
      setClientPage(1);
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    // Don't do anything if we're already on this page
    if (page === currentPage) return;

    if (serverSide) {
      // For server-side, just call the parent handler
      if (onPageChange) {
        onPageChange(page);
      }
    } else {
      // For client-side, update our internal state
      setClientPage(page);
    }
  };

  // Handle sorting
  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  // Generate pagination items
  const renderPaginationItems = () => {
    const items = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if there are few
      for (let i = 1; i <= totalPages; i++) {
        items.push(
          <PaginationItem key={i}>
            <PaginationLink
              isActive={currentPage === i}
              onClick={() => handlePageChange(i)}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }
    } else {
      // Always show first page
      items.push(
        <PaginationItem key={1}>
          <PaginationLink
            isActive={currentPage === 1}
            onClick={() => handlePageChange(1)}
          >
            1
          </PaginationLink>
        </PaginationItem>
      );

      // Show ellipsis if not on first few pages
      if (currentPage > 3) {
        items.push(
          <PaginationItem key="ellipsis-start">
            <PaginationEllipsis />
          </PaginationItem>
        );
      }

      // Show pages around current page
      const startPage = Math.max(2, currentPage - 1);
      const endPage = Math.min(totalPages - 1, currentPage + 1);

      for (let i = startPage; i <= endPage; i++) {
        items.push(
          <PaginationItem key={i}>
            <PaginationLink
              isActive={currentPage === i}
              onClick={() => handlePageChange(i)}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }

      // Show ellipsis if not on last few pages
      if (currentPage < totalPages - 2) {
        items.push(
          <PaginationItem key="ellipsis-end">
            <PaginationEllipsis />
          </PaginationItem>
        );
      }

      // Always show last page
      items.push(
        <PaginationItem key={totalPages}>
          <PaginationLink
            isActive={currentPage === totalPages}
            onClick={() => handlePageChange(totalPages)}
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }

    return items;
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <SearchInput
          placeholder={searchPlaceholder}
          onChange={handleSearch}
          className="w-full max-w-sm"
        />
        {!serverSide && (
          <div className="text-sm text-muted-foreground">
            Showing {paginatedData.length} of {filteredData.length} items
          </div>
        )}
        {serverSide && totalItems !== undefined && (
          <div className="text-sm text-muted-foreground">
            Showing {(currentPage - 1) * pageSize + 1}-
            {Math.min(currentPage * pageSize, totalItems)} of {totalItems} items
          </div>
        )}
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column, colIndex) => (
                <TableHead
                  key={column.accessorKey || column.id || `header-${colIndex}`}
                  className="whitespace-nowrap"
                >
                  {column.sortable !== false && column.accessorKey ? (
                    <Button
                      variant="ghost"
                      className="p-0 font-medium flex items-center gap-1 hover:bg-transparent"
                      onClick={() => column.accessorKey ? handleSort(column.accessorKey) : null}
                    >
                      {column.header}
                      {sortColumn === column.accessorKey &&
                        (sortDirection === "asc" ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        ))}
                    </Button>
                  ) : (
                    column.header
                  )}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <div className="flex justify-center items-center h-full">
                    <div className="h-6 w-6 rounded-full border-2 border-primary/30 border-t-primary animate-spin"></div>
                    <span className="ml-2">Loading...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : paginatedData.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  {emptyMessage}
                </TableCell>
              </TableRow>
            ) : (
              paginatedData.map((row, rowIndex) => (
                <TableRow key={`row-${rowIndex}`}>
                  {columns.map((column, colIndex) => (
                    <TableCell key={`cell-${rowIndex}-${colIndex}`}>
                      {column.cell
                        ? column.cell({ row: { original: row } })
                        : column.accessorKey ? (row as any)[column.accessorKey] : null}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                className={
                  currentPage === 1 ? "pointer-events-none opacity-50" : ""
                }
              />
            </PaginationItem>

            {renderPaginationItems()}

            <PaginationItem>
              <PaginationNext
                onClick={() =>
                  handlePageChange(Math.min(totalPages, currentPage + 1))
                }
                className={
                  currentPage === totalPages
                    ? "pointer-events-none opacity-50"
                    : ""
                }
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  );
}
