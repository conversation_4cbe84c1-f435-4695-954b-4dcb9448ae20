'use client';

import { useSSE } from "@/lib/hooks/use-sse";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

export default function TemplateChangeListener({
  children,
  businessUnit
}: {
  children: React.ReactNode;
  businessUnit: string;
}) {
  const router = useRouter();
  const { connected, lastEvent } = useSSE();
  const [needsRefresh, setNeedsRefresh] = useState(false);

  useEffect(() => {
    if (lastEvent) {
      // If a template was deleted or saved that matches the user's business unit
      if (
        (lastEvent.type === 'template_deleted' && lastEvent.businessUnit === businessUnit) ||
        (lastEvent.type === 'template_saved' && lastEvent.businessUnit === businessUnit)
      ) {
        console.log('Template change detected for user\'s business unit, refreshing...');
        setNeedsRefresh(true);

        // Refresh the page to update the UI
        router.refresh();
      }

      // If an assessment cycle was created, activated, or updated
      if (
        lastEvent.type === 'cycle_created' ||
        lastEvent.type === 'cycle_activated' ||
        lastEvent.type === 'cycle_updated'
      ) {
        console.log('Assessment cycle change detected, refreshing...');
        setNeedsRefresh(true);

        // Refresh the page to update the UI
        router.refresh();
      }
    }
  }, [lastEvent, businessUnit, router]);

  return (
    <>
      {needsRefresh && (
        <div className="fixed bottom-4 right-4 bg-blue-500 text-white p-3 rounded-lg shadow-lg z-50 animate-pulse">
          Template changes detected. Refreshing...
        </div>
      )}
      {children}
    </>
  );
}
