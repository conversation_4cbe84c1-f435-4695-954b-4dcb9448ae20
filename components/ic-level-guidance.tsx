"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Info, ChevronRight } from "lucide-react"
import { ICSkill, icSkillCategories } from "@/lib/ic-skills-data"

interface ICLevelGuidanceProps {
  skill: ICSkill
  userCareerLevel: string
  ratingScale: Record<number, string>
}

export default function ICLevelGuidance({ skill, userCareerLevel, ratingScale }: ICLevelGuidanceProps) {
  const [open, setOpen] = useState(false)

  // Map career level to IC level
  const getICLevel = (careerLevel: string): keyof ICSkill['icLevelDescriptions'] => {
    const level = careerLevel.toLowerCase()
    if (level.includes('l1') || level.includes('intern') || level.includes('cadet')) return 'L1'
    if (level.includes('l2') || level.includes('junior')) return 'L2'
    if (level.includes('l3') || level.includes('engineer')) return 'L3'
    if (level.includes('l4') || level.includes('senior')) return 'L4'
    if (level.includes('l5') || level.includes('staff')) return 'L5'
    if (level.includes('l6') || level.includes('principal')) return 'L6'
    // For very high-level positions (CL7+, VP, etc.), show L6 as they're beyond IC levels
    if (level.includes('l7') || level.includes('l8') || level.includes('l9') ||
      level.includes('vp') || level.includes('director')) return 'L6'
    return 'L2' // Default to L2
  }

  const currentICLevel = getICLevel(userCareerLevel)
  const nextICLevel = currentICLevel === 'L1' ? 'L2' :
    currentICLevel === 'L2' ? 'L3' :
      currentICLevel === 'L3' ? 'L4' :
        currentICLevel === 'L4' ? 'L5' :
          currentICLevel === 'L5' ? 'L6' :
            null // L6 is the highest

  // Check if user is at very high level (CL7+, VP, etc.) - beyond IC levels
  const isHighLevel = userCareerLevel.toLowerCase().includes('l7') ||
    userCareerLevel.toLowerCase().includes('l8') ||
    userCareerLevel.toLowerCase().includes('l9') ||
    userCareerLevel.toLowerCase().includes('vp') ||
    userCareerLevel.toLowerCase().includes('director')

  const getCategoryColor = (categoryGroup: string) => {
    switch (categoryGroup) {
      case 'technical': return 'bg-blue-100 text-blue-800'
      case 'problemSolving': return 'bg-green-100 text-green-800'
      case 'collaboration': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'L1': return 'bg-red-100 text-red-800'
      case 'L2': return 'bg-yellow-100 text-yellow-800'
      case 'L3': return 'bg-blue-100 text-blue-800'
      case 'L4': return 'bg-green-100 text-green-800'
      case 'L5': return 'bg-purple-100 text-purple-800'
      case 'L6': return 'bg-indigo-100 text-indigo-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
          <Info className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <span>{skill.skillNumber}. {skill.category}</span>
            <Badge variant="outline" className={getCategoryColor(skill.categoryGroup)}>
              {icSkillCategories[skill.categoryGroup as keyof typeof icSkillCategories]}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Skill Description */}
          <div>
            <h4 className="font-medium text-sm text-gray-700 mb-2">Skill Description</h4>
            <p className="text-sm text-gray-600">{skill.description}</p>
          </div>

          {/* Rating Scale Reference */}
          <div>
            <h4 className="font-medium text-sm text-gray-700 mb-2">Rating Scale (1-6)</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {Object.entries(ratingScale).map(([rating, label]) => (
                <div key={rating} className="flex items-center gap-2 text-sm">
                  <span className="font-medium w-4">{rating}:</span>
                  <span className="text-gray-600">{label}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Current Level Expectations */}
          <div className="border rounded-lg p-4 bg-blue-50">
            <div className="flex items-center gap-2 mb-3">
              <Badge className={getLevelColor(currentICLevel)}>
                Your Level: {currentICLevel}
              </Badge>
              <span className="text-sm text-gray-600">({userCareerLevel})</span>
              {isHighLevel && (
                <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-300">
                  Leadership Level
                </Badge>
              )}
            </div>
            <h4 className="font-medium text-sm text-gray-700 mb-2">
              {isHighLevel ? "Reference Level (You're beyond IC levels)" : "Expected at Your Level"}
            </h4>
            <p className="text-sm text-gray-700">
              {skill.icLevelDescriptions[currentICLevel]}
            </p>
            {isHighLevel && (
              <p className="text-xs text-purple-600 mt-2 italic">
                💡 As a {userCareerLevel}, you&apos;re operating at leadership level. This L6 reference shows the highest IC expectations for context.
              </p>
            )}
          </div>

          {/* Next Level Target */}
          {nextICLevel && !isHighLevel && (
            <div className="border rounded-lg p-4 bg-green-50">
              <div className="flex items-center gap-2 mb-3">
                <Badge className={getLevelColor(nextICLevel)}>
                  Next Level: {nextICLevel}
                </Badge>
                <ChevronRight className="h-4 w-4 text-gray-400" />
              </div>
              <h4 className="font-medium text-sm text-gray-700 mb-2">Target for Next Level</h4>
              <p className="text-sm text-gray-700">
                {skill.icLevelDescriptions[nextICLevel]}
              </p>
            </div>
          )}

          {/* All Levels Reference */}
          <div>
            <h4 className="font-medium text-sm text-gray-700 mb-3">Complete IC Level Reference</h4>
            <div className="space-y-3">
              {Object.entries(skill.icLevelDescriptions).map(([level, description]) => (
                <div
                  key={level}
                  className={`border rounded-lg p-3 ${level === currentICLevel ? 'border-blue-300 bg-blue-50' :
                    level === nextICLevel ? 'border-green-300 bg-green-50' :
                      'border-gray-200'
                    }`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Badge className={getLevelColor(level)}>
                      {level}
                    </Badge>
                    {level === currentICLevel && (
                      <span className="text-xs text-blue-600 font-medium">Your Current Level</span>
                    )}
                    {level === nextICLevel && nextICLevel && (
                      <span className="text-xs text-green-600 font-medium">Next Target</span>
                    )}
                  </div>
                  <p className="text-sm text-gray-700">{description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
