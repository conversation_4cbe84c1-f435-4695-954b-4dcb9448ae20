import { initializeSettings } from '@/lib/actions/settings-actions';

// Initialize settings flag
let settingsInitialized = false;

/**
 * Server component that initializes settings
 * This component doesn't render anything visible
 */
export default async function SettingsInitializer() {
  // Initialize settings if not already initialized
  if (!settingsInitialized) {
    try {
      await initializeSettings();
      settingsInitialized = true;
      console.log('Settings initialized successfully');
    } catch (error) {
      console.error('Error initializing settings:', error);
    }
  }
  
  // This component doesn't render anything visible
  return null;
}
