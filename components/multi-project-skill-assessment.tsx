"use client"

import { useState } from "react"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { type ProjectSkill } from "@/lib/project-skills-data"
import ProjectSkillAssessment from "@/components/project-skill-assessment"

interface ProjectSkillsGroup {
  projectId: string
  projectName: string
  skills: ProjectSkill[]
}

interface MultiProjectSkillAssessmentProps {
  projectSkillsGroups: ProjectSkillsGroup[]
  onSkillsChange: (projectId: string, skills: ProjectSkill[]) => void
  careerLevel: string
  activeProjectId?: string
}

export default function MultiProjectSkillAssessment({
  projectSkillsGroups,
  onSkillsChange,
  careerLevel,
  activeProjectId
}: MultiProjectSkillAssessmentProps) {
  const [activeTab, setActiveTab] = useState<string>(
    activeProjectId ||
    (projectSkillsGroups.length > 0 ? projectSkillsGroups[0].projectId : "")
  )

  // Handle skill changes for a specific project
  const handleProjectSkillsChange = (projectId: string, updatedSkills: ProjectSkill[]) => {
    onSkillsChange(projectId, updatedSkills)
  }

  if (projectSkillsGroups.length === 0) {
    return (
      <Card className="p-6 text-center">
        <h2 className="text-lg font-medium text-primary mb-3">No Projects Assigned</h2>
        <p className="text-muted-foreground">
          You don&apos;t have any projects assigned yet. Please contact your manager to be assigned to projects.
        </p>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <Card className="overflow-hidden shadow-md border-primary/20 bg-card">
        <div className="p-4 bg-muted/30 border-b">
          <h2 className="text-lg font-medium text-primary">Project Skills Assessment</h2>
          <p className="text-sm text-muted-foreground mt-1">
            These skills are specific to the projects you&apos;re assigned to. Select a project tab below to assess your skills for that project.
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="flex flex-wrap p-0 bg-muted/30 border-b rounded-none">
            {projectSkillsGroups.map((group) => (
              <TabsTrigger
                key={group.projectId}
                value={group.projectId}
                className="data-[state=active]:bg-background rounded-none border-b-2 border-transparent data-[state=active]:border-primary"
              >
                {group.projectName}
              </TabsTrigger>
            ))}
          </TabsList>

          {projectSkillsGroups.map((group) => (
            <TabsContent key={group.projectId} value={group.projectId} className="mt-0 pt-0">
              <ProjectSkillAssessment
                projectSkills={group.skills}
                onSkillsChange={(skills) => handleProjectSkillsChange(group.projectId, skills)}
                careerLevel={careerLevel}
                projectName={group.projectName}
              />
            </TabsContent>
          ))}
        </Tabs>
      </Card>
    </div>
  )
}
