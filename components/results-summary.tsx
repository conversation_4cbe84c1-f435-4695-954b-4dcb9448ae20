import { Card } from "@/components/ui/card"
import type { Skill } from "@/lib/skills-data"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from "recharts"

interface ResultsSummaryProps {
  assessmentData: Skill[]
}

export default function ResultsSummary({ assessmentData }: ResultsSummaryProps) {
  // Count completed assessments
  const completedCount = assessmentData.filter((skill) => skill.currentLevel).length
  const totalSkills = assessmentData.length
  const completionPercentage = Math.round((completedCount / totalSkills) * 100)

  // Calculate gaps for different roles
  const calculateGaps = (targetField: keyof Skill) => {
    return assessmentData.reduce(
      (acc, skill) => {
        if (skill.currentLevel) {
          const target = skill[targetField] as number
          const gap = skill.currentLevel - target

          if (gap < 0) acc.below++
          else if (gap === 0) acc.at++
          else acc.above++
        }
        return acc
      },
      { below: 0, at: 0, above: 0 },
    )
  }

  const cl2Gaps = calculateGaps("targetCL2")
  const cl3Gaps = calculateGaps("targetCL3")
  const cl4Gaps = calculateGaps("targetCL4")
  const cl5Gaps = calculateGaps("targetCL5")
  const cl6Gaps = calculateGaps("targetCL6")

  // Prepare chart data
  const chartData = [
    { name: "CL2", Below: cl2Gaps.below, At: cl2Gaps.at, Above: cl2Gaps.above },
    { name: "CL3", Below: cl3Gaps.below, At: cl3Gaps.at, Above: cl3Gaps.above },
    { name: "CL4", Below: cl4Gaps.below, At: cl4Gaps.at, Above: cl4Gaps.above },
    { name: "CL5", Below: cl5Gaps.below, At: cl5Gaps.at, Above: cl5Gaps.above },
    { name: "CL6", Below: cl6Gaps.below, At: cl6Gaps.at, Above: cl6Gaps.above },
  ]

  // Find strengths and areas for improvement
  const strengths = assessmentData
    .filter((skill) => skill.currentLevel && skill.currentLevel >= 5)
    .map((skill) => skill.category)

  const improvements = assessmentData
    .filter((skill) => {
      // Find skills that are below the target for the user's closest role level
      if (!skill.currentLevel) return false

      // Determine which role level the user is closest to based on average skill level
      const avgLevel = assessmentData.reduce((sum, s) => sum + (s.currentLevel || 0), 0) / completedCount

      let relevantTarget
      if (avgLevel <= 2) relevantTarget = skill.targetCL2
      else if (avgLevel <= 3) relevantTarget = skill.targetCL3
      else if (avgLevel <= 4) relevantTarget = skill.targetCL4
      else relevantTarget = skill.targetCL5

      return skill.currentLevel < relevantTarget
    })
    .map((skill) => skill.category)

  return (
    <div className="space-y-8">
      <div className="flex items-center gap-3">
        <div className="h-8 w-1 bg-primary rounded"></div>
        <h2 className="text-2xl font-bold text-primary">Assessment Summary</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6 shadow-sm border-primary/10 card-hover">
          <h3 className="text-lg font-medium mb-3 text-primary/90">Completion</h3>
          <div className="flex items-end gap-2">
            <div className="text-4xl font-bold">{completionPercentage}%</div>
            <div className="text-sm text-muted-foreground mb-1">
              {completedCount} of {totalSkills} skills
            </div>
          </div>
          <div className="mt-4 h-2 bg-muted rounded-full overflow-hidden">
            <div
              className="h-full bg-primary rounded-full"
              style={{ width: `${completionPercentage}%` }}
            ></div>
          </div>
        </Card>

        <Card className="p-6 shadow-sm border-primary/10 card-hover">
          <h3 className="text-lg font-medium mb-3 text-primary/90">Strengths</h3>
          {strengths.length > 0 ? (
            <ul className="space-y-2">
              {strengths.map((strength, index) => (
                <li key={index} className="flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-green-500"></div>
                  <span>{strength}</span>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-muted-foreground flex items-center gap-2 p-3 bg-muted/20 rounded-lg">
              <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
              No high-level skills identified yet
            </p>
          )}
        </Card>

        <Card className="p-6 shadow-sm border-primary/10 card-hover">
          <h3 className="text-lg font-medium mb-3 text-primary/90">Areas for Improvement</h3>
          {improvements.length > 0 ? (
            <ul className="space-y-2">
              {improvements.map((area, index) => (
                <li key={index} className="flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-red-500"></div>
                  <span>{area}</span>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-muted-foreground flex items-center gap-2 p-3 bg-muted/20 rounded-lg">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              No specific improvement areas identified
            </p>
          )}
        </Card>
      </div>

      <Card className="p-6 shadow-sm border-primary/10">
        <h3 className="text-lg font-medium mb-4 text-primary/90">Skills Gap Analysis</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis dataKey="name" tick={{ fill: '#666' }} />
              <YAxis tick={{ fill: '#666' }} />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e2e8f0',
                  borderRadius: '0.5rem',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                }}
              />
              <Legend />
              <Bar dataKey="Below" stackId="a" fill="#ef4444" name="Below Target" />
              <Bar dataKey="At" stackId="a" fill="#eab308" name="At Target" />
              <Bar dataKey="Above" stackId="a" fill="#22c55e" name="Above Target" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </Card>

      <Card className="p-6 shadow-sm border-primary/10 bg-gradient-to-br from-primary/5 to-primary/10">
        <h3 className="text-lg font-medium mb-3 text-primary/90">Next Steps</h3>
        <p className="mb-4 text-muted-foreground">Based on your assessment, here are some recommended actions:</p>
        <ul className="space-y-3">
          {improvements.length > 0 ? (
            improvements.slice(0, 3).map((area, index) => (
              <li key={index} className="flex items-center gap-3 p-3 bg-white dark:bg-card rounded-lg shadow-sm">
                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">{index + 1}</div>
                <span>Focus on developing your <span className="font-medium">{area}</span> skills</span>
              </li>
            ))
          ) : (
            <li className="flex items-center gap-3 p-3 bg-white dark:bg-card rounded-lg shadow-sm">
              <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">1</div>
              <span>Complete the assessment to get personalized recommendations</span>
            </li>
          )}
          <li className="flex items-center gap-3 p-3 bg-white dark:bg-card rounded-lg shadow-sm">
            <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">{improvements.length > 0 ? improvements.length + 1 : 2}</div>
            <span>Discuss your results with your manager or mentor</span>
          </li>
          <li className="flex items-center gap-3 p-3 bg-white dark:bg-card rounded-lg shadow-sm">
            <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">{improvements.length > 0 ? improvements.length + 2 : 3}</div>
            <span>Create a personal development plan focusing on gap areas</span>
          </li>
          <li className="flex items-center gap-3 p-3 bg-white dark:bg-card rounded-lg shadow-sm">
            <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">{improvements.length > 0 ? improvements.length + 3 : 4}</div>
            <span>Re-assess in 3-6 months to track your progress</span>
          </li>
        </ul>
      </Card>
    </div>
  )
}
