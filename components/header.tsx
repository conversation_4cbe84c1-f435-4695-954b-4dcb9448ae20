"use client"

import Link from "next/link"
import { signOut } from "next-auth/react"
import { useSessionContext } from "./auth/session-context"
import { But<PERSON> } from "@/components/ui/button"
import dynamic from 'next/dynamic'
import {
  Bar<PERSON>hart3,
  LogOut,
  User,
  Setting<PERSON>
} from "lucide-react"
import { ThemeToggle } from './theme-toggle'
import { useAdminStatus } from '@/lib/hooks/use-admin'

// Dynamically import the DB status component to avoid affecting initial load
const DatabaseStatus = dynamic(() => import('@/components/db-status'), {
  ssr: false,
  loading: () => <div className="text-xs text-gray-500 flex items-center gap-1">
    <div className="w-2 h-2 rounded-full bg-yellow-500 animate-pulse" />
    <span>Checking DB...</span>
  </div>
})

// No debug components needed

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
// Dropdown menu components imported above

export default function Header() {
  // Use our optimized session context instead of useSession directly
  const { session } = useSessionContext()
  const { isAdmin: isAdminUser } = useAdminStatus()

  return (
    <header className="border-b border-border bg-card shadow-md sticky top-0 z-50">
      <div className="container mx-auto py-3 px-4 flex justify-between items-center">
        <div className="flex items-center gap-4">
          <Link href="/" className="text-xl font-bold text-primary hover:text-primary/90 transition-colors">
            Skills Assessment Tool
          </Link>
          <DatabaseStatus />
        </div>

        <div className="flex items-center space-x-4">
          {isAdminUser && (
            <Button asChild variant="default" className="bg-blue-600 hover:bg-blue-700 text-white shadow-md font-medium transition-colors duration-200">
              <Link href="/admin" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-white" />
                <span>Admin</span>
              </Link>
            </Button>
          )}
          <ThemeToggle />
          {session?.user ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-9 px-3 rounded-full bg-primary/10 hover:bg-blue-500 hover:text-white transition-colors duration-200 flex items-center gap-2">
                  <User className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium hidden sm:inline-block">{session.user.name?.split(' ')[0]}</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-64" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">{session.user.name}</p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {session.user.email}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />

                {isAdminUser && (
                  <DropdownMenuItem asChild>
                    <Link href="/admin" className="flex items-center">
                      <BarChart3 className="mr-2 h-4 w-4 text-primary" />
                      <span>Admin Dashboard</span>
                    </Link>
                  </DropdownMenuItem>
                )}

                <DropdownMenuItem asChild>
                  <Link href="/profile" className="flex items-center">
                    <Settings className="mr-2 h-4 w-4 text-primary" />
                    <span>Profile Settings</span>
                  </Link>
                </DropdownMenuItem>

                <DropdownMenuSeparator />

                <DropdownMenuItem
                  className="cursor-pointer text-destructive focus:text-destructive"
                  onClick={() => signOut({ callbackUrl: "/auth/signin" })}
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Sign Out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button asChild variant="default" className="bg-primary hover:bg-blue-500 shadow-md transition-colors duration-200 font-medium">
              <Link href="/auth/signin" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>Sign In</span>
              </Link>
            </Button>
          )}
        </div>
      </div>
    </header>
  )
}
