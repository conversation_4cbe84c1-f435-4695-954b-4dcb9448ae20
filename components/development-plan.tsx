"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Plus, X } from "lucide-react"
import type { DevelopmentPlan } from "@/lib/actions/assessment"

interface DevelopmentPlanProps {
  developmentPlan: DevelopmentPlan
  onPlanChange: (plan: DevelopmentPlan) => void
}

export default function DevelopmentPlanComponent({
  developmentPlan,
  onPlanChange
}: DevelopmentPlanProps) {
  const [keyAreas, setKeyAreas] = useState<string[]>(developmentPlan?.keyAreas || [])
  const [actions, setActions] = useState<string[]>(developmentPlan?.actions || [])
  const [notes, setNotes] = useState(developmentPlan?.notes || "")
  const [newKeyArea, setNewKeyArea] = useState("")
  const [newAction, setNewAction] = useState("")

  // Update parent component when plan changes
  useEffect(() => {
    onPlanChange({
      keyAreas,
      actions,
      notes
    })
  }, [keyAreas, actions, notes, onPlanChange])

  const handleAddKeyArea = () => {
    if (!newKeyArea.trim()) return
    setKeyAreas([...keyAreas, newKeyArea])
    setNewKeyArea("")
  }

  const handleRemoveKeyArea = (index: number) => {
    const updatedAreas = [...keyAreas]
    updatedAreas.splice(index, 1)
    setKeyAreas(updatedAreas)
  }

  const handleAddAction = () => {
    if (!newAction.trim()) return
    setActions([...actions, newAction])
    setNewAction("")
  }

  const handleRemoveAction = (index: number) => {
    const updatedActions = [...actions]
    updatedActions.splice(index, 1)
    setActions(updatedActions)
  }

  return (
    <div className="space-y-6">
      <Card className="p-6 shadow-md border-primary/20 bg-card">
        <h2 className="text-lg font-medium text-primary mb-4">Development Plan</h2>
        <p className="text-sm text-muted-foreground mb-6">
          Identify key areas for development based on gaps between your current level and the target levels.
          Outline specific actions to address these gaps in your development plan.
        </p>

        {/* Key Development Areas */}
        <div className="mb-6">
          <h3 className="text-md font-medium mb-3">Key Areas for Development (1-3)</h3>
          <div className="space-y-2 mb-4">
            {keyAreas.map((area, index) => (
              <div key={index} className="flex items-center gap-2 p-2 bg-muted/20 rounded-md">
                <span className="flex-1">{area}</span>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => handleRemoveKeyArea(index)}
                  className="h-8 w-8 text-destructive hover:text-destructive/80 hover:bg-destructive/10"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
          <div className="flex gap-2">
            <Input
              value={newKeyArea}
              onChange={(e) => setNewKeyArea(e.target.value)}
              placeholder="Enter a key development area"
              className="flex-1"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  handleAddKeyArea()
                }
              }}
            />
            <Button 
              onClick={handleAddKeyArea}
              disabled={!newKeyArea.trim() || keyAreas.length >= 3}
              className="bg-primary hover:bg-blue-500 shadow-md border border-primary/20 font-medium transition-colors duration-200"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add
            </Button>
          </div>
          {keyAreas.length >= 3 && (
            <p className="text-xs text-amber-600 mt-1">Maximum of 3 key areas recommended</p>
          )}
        </div>

        {/* Development Actions */}
        <div className="mb-6">
          <h3 className="text-md font-medium mb-3">Specific Actions</h3>
          <div className="space-y-2 mb-4">
            {actions.map((action, index) => (
              <div key={index} className="flex items-center gap-2 p-2 bg-muted/20 rounded-md">
                <span className="flex-1">{action}</span>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => handleRemoveAction(index)}
                  className="h-8 w-8 text-destructive hover:text-destructive/80 hover:bg-destructive/10"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
          <div className="flex gap-2">
            <Input
              value={newAction}
              onChange={(e) => setNewAction(e.target.value)}
              placeholder="Enter a specific action (e.g., training, project tasks, mentoring)"
              className="flex-1"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  handleAddAction()
                }
              }}
            />
            <Button 
              onClick={handleAddAction}
              disabled={!newAction.trim()}
              className="bg-primary hover:bg-blue-500 shadow-md border border-primary/20 font-medium transition-colors duration-200"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add
            </Button>
          </div>
        </div>

        {/* Additional Notes */}
        <div>
          <Label htmlFor="notes" className="text-md font-medium mb-3 block">Additional Notes</Label>
          <Textarea
            id="notes"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Add any additional notes or comments about your development plan"
            className="min-h-[100px]"
          />
        </div>
      </Card>
    </div>
  )
}
