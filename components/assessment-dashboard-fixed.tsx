"use client"

import React, { useState, useEffect, useCallback } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { getUserAssessments } from "@/lib/actions/assessment-cycles"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calendar, CheckCircle, Clock, FileText, UserCheck, Users, Eye } from "lucide-react"

interface AssessmentDashboardProps {
  userId: string
  userName: string
  userEmail: string
  businessUnit: string
}

export default function AssessmentDashboardFixed({
  userId,
  userName,
  userEmail,
  businessUnit
}: AssessmentDashboardProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("available")
  const [assessments, setAssessments] = useState<any[]>([])
  const [cycles, setCycles] = useState<any[]>([])
  const [error, setError] = useState<string | null>(null)
  const [message, setMessage] = useState<{ text: string; type: "success" | "error" } | null>(null)

  useEffect(() => {
    const fetchAssessments = async () => {
      setIsLoading(true)
      try {
        const result = await getUserAssessments(userId, userEmail)

        // Check if result exists before accessing properties
        if (result && result.success) {
          setAssessments(result.data?.assessments || [])
          setCycles(result.data?.cycles || [])

          // Check for completed self-assessments in the last 6 months
          const selfAssessments = result.data?.assessments?.filter((a: any) =>
            a.relationshipType === 'self' && a.status === 'completed'
          ) || [];

          if (selfAssessments.length > 0) {
            // Sort by date, newest first
            selfAssessments.sort((a: any, b: any) =>
              new Date(b.updatedAt || b.createdAt).getTime() -
              new Date(a.updatedAt || a.createdAt).getTime()
            );

            // Get the most recent assessment
            const latestAssessment: any = selfAssessments[0];
            const assessmentDate = new Date(latestAssessment.updatedAt || latestAssessment.createdAt);

            // Calculate when the next assessment can be taken (6 months later)
            const nextAssessmentDate = new Date(assessmentDate);
            nextAssessmentDate.setMonth(nextAssessmentDate.getMonth() + 6);

            // Check if the next assessment date is in the future
            if (nextAssessmentDate > new Date()) {
              const formattedDate = nextAssessmentDate.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              });

              setMessage({
                text: `You can only take an assessment once every 6 months. Your next assessment can be taken after ${formattedDate}.`,
                type: "error"
              });
            } else {
              setMessage(null);
            }
          } else {
            setMessage(null);
          }
        } else {
          setError((result?.message) || "Failed to fetch assessments")
        }
      } catch (err) {
        setError("An error occurred while fetching assessments")
        console.error("Error fetching assessments:", err)
      } finally {
        setIsLoading(false)
      }
    }

    // Only fetch if we have a userId and userEmail
    if (userId && userEmail) {
      fetchAssessments()
    } else {
      setIsLoading(false)
      setError("User information is missing")
    }
  }, [userId, userEmail, businessUnit])

  // Filter assessments by type and status
  const selfAssessments = assessments.filter(a => a.relationshipType === 'self')
  const managerAssessments = assessments.filter(a => a.relationshipType === 'manager')
  const peerAssessments = assessments.filter(a => a.relationshipType === 'peer')

  // Get active assessment cycles that the user can participate in
  // Only show cycles if the user can take a new assessment (based on the 6-month restriction)
  const canTakeNewAssessment = !message || !message.text.includes("You can only take an assessment once every")
  const availableCycles = canTakeNewAssessment ? cycles.filter(c => c.status === 'active') : []

  const inProgressAssessments = assessments.filter(a => a.status === 'in_progress')
  const completedAssessments = assessments.filter(a => a.status === 'completed')

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800 px-2.5 py-0.5 rounded-full font-medium">
            <span className="flex items-center gap-1.5">
              <div className="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400"></div>
              Pending
            </span>
          </Badge>
        )
      case 'in_progress':
        return (
          <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/30 dark:text-amber-300 dark:border-amber-800 px-2.5 py-0.5 rounded-full font-medium">
            <span className="flex items-center gap-1.5">
              <div className="w-2 h-2 rounded-full bg-amber-500 dark:bg-amber-400 animate-pulse"></div>
              In Progress
            </span>
          </Badge>
        )
      case 'completed':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800 px-2.5 py-0.5 rounded-full font-medium">
            <span className="flex items-center gap-1.5">
              <div className="w-2 h-2 rounded-full bg-green-500 dark:bg-green-400"></div>
              Completed
            </span>
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Get relationship type badge
  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'self':
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-800 px-2.5 py-0.5 rounded-full font-medium">
            Self Assessment
          </Badge>
        )
      case 'manager':
        return (
          <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-300 dark:border-indigo-800 px-2.5 py-0.5 rounded-full font-medium">
            Manager Assessment
          </Badge>
        )
      case 'peer':
        return (
          <Badge variant="outline" className="bg-teal-50 text-teal-700 border-teal-200 dark:bg-teal-900/30 dark:text-teal-300 dark:border-teal-800 px-2.5 py-0.5 rounded-full font-medium">
            Peer Assessment
          </Badge>
        )
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  // Handle starting a new assessment
  const handleStartAssessment = (assessment: any) => {
    if (assessment.relationshipType === 'self') {
      router.push(`/assessment/self/${assessment.id}`)
    } else if (assessment.relationshipType === 'manager') {
      router.push(`/assessment/manager/${assessment.id}`)
    } else if (assessment.relationshipType === 'peer') {
      router.push(`/assessment/peer/${assessment.id}`)
    }
  }

  // Handle starting a new assessment from a cycle
  const handleStartCycleAssessment = (cycle: any) => {
    router.push(`/assessment/self/${cycle.id}`)
  }

  if (isLoading) {
    return (
      <div className="w-full rounded-lg border bg-card text-card-foreground shadow-sm">
        <div className="p-6">
          <div className="flex justify-center py-8">
            <div className="h-8 w-8 rounded-full border-2 border-primary/30 border-t-primary animate-spin"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="w-full rounded-lg border bg-card text-card-foreground shadow-sm">
        <div className="p-6">
          <div className="p-4 rounded-lg border border-red-200 bg-red-50 text-red-800">
            {error}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full rounded-lg border bg-card text-card-foreground shadow-sm">
      <div className="flex flex-col space-y-1.5 p-6">
        <div className="text-2xl font-semibold leading-none tracking-tight text-primary">My Assessment Dashboard</div>
        <div className="text-sm text-muted-foreground">
          Track your progress and manage all your assessments in one place
        </div>
      </div>
      <div className="p-6 pt-0">
        {/* Assessment Summary Section */}
        <div className="mb-8 p-5 bg-gradient-to-br from-primary/5 to-primary/10 rounded-xl border border-primary/20">
          <h3 className="text-lg font-semibold mb-4 text-primary">Assessment Summary</h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Self Assessment Summary */}
            <div className="bg-card p-4 rounded-lg shadow-sm border border-border">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/50">
                  <FileText className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <p className="font-medium">Self Assessment</p>
                  <p className="text-xs text-muted-foreground">Your self-evaluation</p>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span>Completed</span>
                  <span className="font-medium">{selfAssessments.filter(a => a.status === 'completed').length}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span>In Progress</span>
                  <span className="font-medium">{selfAssessments.filter(a => a.status === 'in_progress').length}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span>Available Cycles</span>
                  <span className="font-medium">{availableCycles.length}</span>
                </div>
              </div>
            </div>

            {/* Manager Assessment Summary */}
            <div className="bg-card p-4 rounded-lg shadow-sm border border-border">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-full bg-indigo-100 dark:bg-indigo-900/50">
                  <UserCheck className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                </div>
                <div>
                  <p className="font-medium">Manager Assessment</p>
                  <p className="text-xs text-muted-foreground">Your manager&apos;s evaluation</p>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span>Completed</span>
                  <span className="font-medium">{managerAssessments.filter(a => a.status === 'completed').length}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span>In Progress</span>
                  <span className="font-medium">{managerAssessments.filter(a => a.status === 'in_progress').length}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span>Pending</span>
                  <span className="font-medium">{managerAssessments.filter(a => a.status === 'pending').length}</span>
                </div>
              </div>
            </div>

            {/* Peer Assessment Summary */}
            <div className="bg-card p-4 rounded-lg shadow-sm border border-border">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-full bg-teal-100 dark:bg-teal-900/50">
                  <Users className="h-5 w-5 text-teal-600 dark:text-teal-400" />
                </div>
                <div>
                  <p className="font-medium">Peer Assessment</p>
                  <p className="text-xs text-muted-foreground">Feedback from peers</p>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span>Completed</span>
                  <span className="font-medium">{peerAssessments.filter(a => a.status === 'completed').length}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span>In Progress</span>
                  <span className="font-medium">{peerAssessments.filter(a => a.status === 'in_progress').length}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span>Pending</span>
                  <span className="font-medium">{peerAssessments.filter(a => a.status === 'pending').length}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="rounded-lg border bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/40 dark:to-blue-900/20 border-blue-200 dark:border-blue-800 shadow-sm hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/50 shadow-inner">
                    <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Available</p>
                    <p className="text-3xl font-bold text-blue-700 dark:text-blue-300">{availableCycles.length}</p>
                    <p className="text-xs text-blue-600/70 dark:text-blue-400/70 mt-1">Active assessment cycles</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="rounded-lg border bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-950/40 dark:to-amber-900/20 border-amber-200 dark:border-amber-800 shadow-sm hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-3 rounded-full bg-amber-100 dark:bg-amber-900/50 shadow-inner">
                    <Clock className="h-6 w-6 text-amber-600 dark:text-amber-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-amber-600 dark:text-amber-400">In Progress</p>
                    <p className="text-3xl font-bold text-amber-700 dark:text-amber-300">{inProgressAssessments.length}</p>
                    <p className="text-xs text-amber-600/70 dark:text-amber-400/70 mt-1">Assessments you&apos;ve started</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="rounded-lg border bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/40 dark:to-green-900/20 border-green-200 dark:border-green-800 shadow-sm hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/50 shadow-inner">
                    <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-green-600 dark:text-green-400">Completed</p>
                    <p className="text-3xl font-bold text-green-700 dark:text-green-300">{completedAssessments.length}</p>
                    <p className="text-xs text-green-600/70 dark:text-green-400/70 mt-1">Assessments you&apos;ve finished</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-6 bg-muted/30 p-1 rounded-lg">
            <TabsTrigger
              value="available"
              className="data-[state=active]:bg-blue-100 data-[state=active]:text-blue-700 dark:data-[state=active]:bg-blue-900/30 dark:data-[state=active]:text-blue-300 rounded-md"
            >
              <span className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <span>Available</span>
                {availableCycles.length > 0 && (
                  <span className="ml-1 bg-blue-200 dark:bg-blue-800 text-blue-800 dark:text-blue-200 text-xs rounded-full px-2 py-0.5">
                    {availableCycles.length}
                  </span>
                )}
              </span>
            </TabsTrigger>
            <TabsTrigger
              value="in-progress"
              className="data-[state=active]:bg-amber-100 data-[state=active]:text-amber-700 dark:data-[state=active]:bg-amber-900/30 dark:data-[state=active]:text-amber-300 rounded-md"
            >
              <span className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span>In Progress</span>
                {inProgressAssessments.length > 0 && (
                  <span className="ml-1 bg-amber-200 dark:bg-amber-800 text-amber-800 dark:text-amber-200 text-xs rounded-full px-2 py-0.5">
                    {inProgressAssessments.length}
                  </span>
                )}
              </span>
            </TabsTrigger>
            <TabsTrigger
              value="completed"
              className="data-[state=active]:bg-green-100 data-[state=active]:text-green-700 dark:data-[state=active]:bg-green-900/30 dark:data-[state=active]:text-green-300 rounded-md"
            >
              <span className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                <span>Completed</span>
                {completedAssessments.length > 0 && (
                  <span className="ml-1 bg-green-200 dark:bg-green-800 text-green-800 dark:text-green-200 text-xs rounded-full px-2 py-0.5">
                    {completedAssessments.length}
                  </span>
                )}
              </span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="available">
            {message && message.text.includes("You can only take an assessment once every") ? (
              <div className="p-6 bg-amber-50 border border-amber-200 rounded-lg mb-4">
                <div className="flex items-start gap-3">
                  <div className="p-2 rounded-full bg-amber-100 mt-1">
                    <Clock className="h-5 w-5 text-amber-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-amber-800 text-lg mb-1">Assessment Time Restriction</h3>
                    <p className="text-amber-700">{message.text}</p>
                    <p className="text-amber-700 mt-2">
                      This restriction ensures that assessments are spaced out appropriately to track your progress over time.
                    </p>
                  </div>
                </div>
              </div>
            ) : availableCycles.length > 0 ? (
              <div className="space-y-4">
                {/* Available cycles will be added in the next edit */}
              </div>
            ) : (
              <div className="text-center py-12 px-6 text-muted-foreground bg-muted/10 rounded-lg border border-dashed border-blue-200 dark:border-blue-900/50">
                <FileText className="h-12 w-12 mx-auto mb-3 text-blue-300 dark:text-blue-800" />
                <p className="text-lg font-medium mb-1">No active assessment cycles</p>
                <p className="text-sm max-w-md mx-auto mb-4">Assessment cycles must be created by an administrator before they appear here.</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="in-progress">
            {/* In progress content will be added in the next edit */}
          </TabsContent>

          <TabsContent value="completed">
            {/* Completed content will be added in the next edit */}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
