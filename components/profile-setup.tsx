"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { saveUserProfile, getUserProfile } from "@/lib/actions/user-profile"
import { businessUnitOptions, careerLevelOptions } from "@/lib/models/user-profile"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"

interface ProfileSetupProps {
  user: {
    name?: string | null
    email?: string | null
  }
}

export default function ProfileSetup({ user }: ProfileSetupProps) {
  const [businessUnit, setBusinessUnit] = useState("")
  const [careerLevel, setCareerLevel] = useState("")
  const [jobRole, setJobRole] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const { toast } = useToast()

  // Fetch existing profile data if available
  useEffect(() => {
    const fetchProfile = async () => {
      if (user?.email) {
        setIsLoading(true)
        const result = await getUserProfile(user.email)
        if (result.success && result.data) {
          setBusinessUnit(result.data.businessUnit || "")
          setCareerLevel(result.data.careerLevel || "")
          setJobRole(result.data.jobRole || "")
        }
        setIsLoading(false)
      }
    }

    fetchProfile()
  }, [user])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!businessUnit || !careerLevel || !jobRole) {
      toast({
        title: "Missing information",
        description: "Please fill in all fields",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    const result = await saveUserProfile({
      email: user.email || "",
      name: user.name || "",
      businessUnit,
      careerLevel,
      jobRole,
    })

    setIsSubmitting(false)

    if (result.success) {
      toast({
        title: "Profile saved",
        description: "Your profile has been saved successfully",
      })
      router.refresh()
    } else {
      toast({
        title: "Error",
        description: result.message || "Failed to save profile",
        variant: "destructive",
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
        <div className="flex flex-col items-center justify-center space-y-4">
          <div className="h-8 w-8 rounded-full border-4 border-primary/30 border-t-primary animate-spin"></div>
          <p className="text-muted-foreground">Loading profile information...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Complete Your Profile</CardTitle>
          <CardDescription>
            {businessUnit || careerLevel || jobRole ?
              "Review and update your profile information." :
              "Please provide some additional information before you continue to the assessment tool."}
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-1">
              <Label htmlFor="name">Name</Label>
              <Input id="name" value={user.name || ""} disabled />
            </div>
            <div className="space-y-1">
              <Label htmlFor="email">Email</Label>
              <Input id="email" value={user.email || ""} disabled />
            </div>
            <div className="space-y-1">
              <Label htmlFor="businessUnit">Business Unit</Label>
              <Select value={businessUnit} onValueChange={setBusinessUnit} required>
                <SelectTrigger>
                  <SelectValue placeholder="Select your business unit" />
                </SelectTrigger>
                <SelectContent>
                  {businessUnitOptions.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option.toUpperCase()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-1">
              <Label htmlFor="careerLevel">Current Career Level</Label>
              <Select value={careerLevel} onValueChange={setCareerLevel} required>
                <SelectTrigger>
                  <SelectValue placeholder="Select your career level" />
                </SelectTrigger>
                <SelectContent>
                  {careerLevelOptions.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option.toUpperCase()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-1">
              <Label htmlFor="jobRole">Current Job Role</Label>
              <Input
                id="jobRole"
                placeholder="e.g., Frontend Developer, Data Scientist"
                value={jobRole}
                onChange={(e) => setJobRole(e.target.value)}
                required
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? "Saving..." :
                (businessUnit || careerLevel || jobRole) ?
                  "Update Profile and Continue" :
                  "Continue to Assessment Tool"}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  )
}
