'use client'

import * as React from 'react'
import {
  ThemeProvider as NextThemesProvider,
  type ThemeProviderProps,
} from 'next-themes'

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  // Set default theme to dark
  const defaultProps = {
    defaultTheme: 'dark',
    enableSystem: true,
    disableTransitionOnChange: false,
    storageKey: 'skills-assessment-theme',
    attribute: 'class',
    ...props
  }
  const [mounted, setMounted] = React.useState(false)

  // After mounting, we have access to the theme
  React.useEffect(() => {
    setMounted(true)
  }, [])

  // Prevent hydration mismatch by only rendering children when mounted
  // This ensures the server and client render the same content
  return (
    <NextThemesProvider
      attribute={defaultProps.attribute as "class"}
      defaultTheme={defaultProps.defaultTheme}
      enableSystem={defaultProps.enableSystem}
      disableTransitionOnChange={defaultProps.disableTransitionOnChange}
      storageKey={defaultProps.storageKey}
    >
      {mounted ? children : (
        // Use a wrapper with the dark theme class to prevent layout shift
        <div className="dark" style={{ colorScheme: "dark" }}>
          {children}
        </div>
      )}
    </NextThemesProvider>
  )
}
