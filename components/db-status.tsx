"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"

export default function DatabaseStatus() {
  const [status, setStatus] = useState<"loading" | "connected" | "error">("loading")
  const [latency, setLatency] = useState<number | null>(null)

  useEffect(() => {
    const checkConnection = async () => {
      try {
        const startTime = performance.now()

        // Make a simple request to check DB connection
        const response = await fetch("/api/health/db", {
          method: "GET",
          cache: "no-store"
        })

        const endTime = performance.now()
        setLatency(Math.round(endTime - startTime))

        if (response.ok) {
          setStatus("connected")
        } else {
          setStatus("error")
        }
      } catch (error) {
        setStatus("error")
      }
    }

    // Check connection once on mount
    checkConnection()

    // Don't check again - the initial check is sufficient
    // This prevents multiple health checks that slow down the app
  }, [])

  return (
    <div className="text-xs text-gray-500 flex items-center gap-1">
      <div
        className={`w-2 h-2 rounded-full ${status === "connected"
            ? "bg-green-500"
            : status === "error"
              ? "bg-red-500"
              : "bg-yellow-500 animate-pulse"
          }`}
      />
      <span>
        {status === "connected"
          ? `DB Connected ${latency ? `(${latency}ms)` : ""}`
          : status === "error"
            ? "DB Connection Error"
            : "Connecting to DB..."}
      </span>
    </div>
  )
}
