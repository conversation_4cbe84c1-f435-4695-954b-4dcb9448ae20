"use client"

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Calendar } from "lucide-react"
import {
  Line<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'

// Modern color palette for charts
const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16']

// Loading component with optimized skeleton
export const ChartLoading = () => (
  <div className="w-full h-80 animate-pulse">
    <Skeleton className="w-full h-full rounded-lg" />
    <div className="mt-4 flex justify-center">
      <div className="h-6 w-6 rounded-full border-2 border-primary/30 border-t-primary animate-spin"></div>
    </div>
  </div>
)

// Line Chart Component for skill history
interface SkillHistoryChartProps {
  data: Array<{
    date: string;
    level: number;
  }>;
  title: string;
  skillName: string;
}

export function SkillHistoryChart({ data, title, skillName }: SkillHistoryChartProps) {
  // If no data, show a message
  if (!data || data.length === 0) {
    return (
      <div className="py-8 text-center bg-muted/20 rounded-lg border border-dashed">
        <div className="flex flex-col items-center gap-3">
          <div className="h-12 w-12 rounded-full bg-muted/50 flex items-center justify-center text-muted-foreground">
            <Calendar className="h-6 w-6" />
          </div>
          <p className="text-muted-foreground">No history data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="date"
            tick={{ fill: '#666' }}
            axisLine={{ stroke: '#e2e8f0' }}
            tickLine={{ stroke: '#e2e8f0' }}
          />
          <YAxis
            domain={[0, 6]}
            ticks={[0, 1, 2, 3, 4, 5, 6]}
            tick={{ fill: '#666' }}
            axisLine={{ stroke: '#e2e8f0' }}
            tickLine={{ stroke: '#e2e8f0' }}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: 'white',
              border: '1px solid #e2e8f0',
              borderRadius: '0.5rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
            }}
          />
          <Legend
            wrapperStyle={{
              paddingTop: '10px',
              borderTop: '1px solid #f0f0f0'
            }}
          />
          <Line
            type="monotone"
            dataKey="level"
            name={skillName}
            stroke="#3b82f6"
            strokeWidth={2}
            activeDot={{ r: 8, fill: '#3b82f6', stroke: 'white', strokeWidth: 2 }}
            dot={{ r: 4, fill: '#3b82f6', stroke: 'white', strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}

// Multi-line Chart Component for all skills history
interface AllSkillsHistoryChartProps {
  data: any[];
  title: string;
  skills: Array<{
    id: string;
    category: string;
  }>;
}

export function AllSkillsHistoryChart({ data, title, skills }: AllSkillsHistoryChartProps) {
  // If no data, show a message
  if (!data || data.length === 0) {
    return (
      <div className="py-8 text-center bg-muted/20 rounded-lg border border-dashed">
        <div className="flex flex-col items-center gap-3">
          <div className="h-12 w-12 rounded-full bg-muted/50 flex items-center justify-center text-muted-foreground">
            <Calendar className="h-6 w-6" />
          </div>
          <p className="text-muted-foreground">No history data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="date"
            tick={{ fill: '#666' }}
            axisLine={{ stroke: '#e2e8f0' }}
            tickLine={{ stroke: '#e2e8f0' }}
          />
          <YAxis
            domain={[0, 6]}
            ticks={[0, 1, 2, 3, 4, 5, 6]}
            tick={{ fill: '#666' }}
            axisLine={{ stroke: '#e2e8f0' }}
            tickLine={{ stroke: '#e2e8f0' }}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: 'white',
              border: '1px solid #e2e8f0',
              borderRadius: '0.5rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
            }}
          />
          <Legend
            wrapperStyle={{
              paddingTop: '10px',
              borderTop: '1px solid #f0f0f0'
            }}
          />
          {skills.map((skill, index) => (
            <Line
              key={skill.id}
              type="monotone"
              dataKey={skill.id}
              name={skill.category}
              stroke={COLORS[index % COLORS.length]}
              strokeWidth={2}
              activeDot={{ r: 6, stroke: 'white', strokeWidth: 2 }}
              dot={{ r: 3, stroke: 'white', strokeWidth: 1 }}
              connectNulls
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
