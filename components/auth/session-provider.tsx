"use client"

import type React from "react"

// Import the real SessionProvider
import { SessionProvider } from "next-auth/react"
import { SessionContextProvider } from "./session-context"

// Use the real session provider with optimized settings
export default function AuthSessionProvider({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider
      // Increase refetch interval to reduce session requests
      // Only refetch session every 2 hours (7200 seconds) for development
      refetchInterval={process.env.NODE_ENV === 'development' ? 7200 : 1800}
      // Don't refetch session on window focus
      refetchOnWindowFocus={false}
    >
      {/* Wrap children with our custom context provider */}
      <SessionContextProvider>
        {children}
      </SessionContextProvider>
    </SessionProvider>
  )
}
