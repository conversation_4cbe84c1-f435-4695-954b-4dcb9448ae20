"use client"

import React, { create<PERSON>ontext, useContext, useEffect, useState } from "react"
import { Session } from "next-auth"
import { useSession } from "next-auth/react"

// Create a context for the session
interface SessionContextType {
  session: Session | null
  status: "loading" | "authenticated" | "unauthenticated"
  update: () => Promise<void>
}

const SessionContext = createContext<SessionContextType>({
  session: null,
  status: "loading",
  update: async () => { },
})

// Custom hook to use the session context
export const useSessionContext = () => useContext(SessionContext)

// Provider component to wrap the application
export function SessionContextProvider({ children }: { children: React.ReactNode }) {
  // Use the useSession hook once at the top level
  const { data: session, status, update } = useSession()

  // Create a state to store the session
  const [sessionState, setSessionState] = useState<Session | null>(null)
  const [statusState, setStatusState] = useState<"loading" | "authenticated" | "unauthenticated">("loading")

  // Update the state when the session changes
  useEffect(() => {
    if (session !== undefined) {
      setSessionState(session)
    }
    setStatusState(status)
  }, [session, status])

  // Create a wrapper for the update function to match the expected type
  const updateSession = async () => {
    await update();
  };

  // Provide the session to all children
  return (
    <SessionContext.Provider value={{ session: sessionState, status: statusState, update: updateSession }}>
      {children}
    </SessionContext.Provider>
  )
}
