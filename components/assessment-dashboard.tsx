"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { getUserAssessments } from "@/lib/actions/assessment-cycles"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calendar, CheckCircle, Clock, FileText, UserCheck, Users, Eye } from "lucide-react"

interface AssessmentDashboardProps {
  userId: string
  userName: string
  userEmail: string
  businessUnit: string
}

export default function AssessmentDashboard({
  userId,
  userName,
  userEmail,
  businessUnit
}: AssessmentDashboardProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("available")
  const [assessments, setAssessments] = useState<any[]>([])
  const [cycles, setCycles] = useState<any[]>([])
  const [error, setError] = useState<string | null>(null)
  const [message, setMessage] = useState<{ text: string; type: "success" | "error" } | null>(null)

  useEffect(() => {
    const fetchAssessments = async () => {
      setIsLoading(true)
      try {
        const result = await getUserAssessments(userId, userEmail)

        // Check if result exists before accessing properties
        if (result && result.success) {
          setAssessments(result.data?.assessments || [])
          setCycles(result.data?.cycles || [])

          // We're no longer using the time restriction
          // Instead, we'll rely on the active assessment lifecycle
          setMessage(null);
        } else {
          setError((result?.message) || "Failed to fetch assessments")
        }
      } catch (err) {
        setError("An error occurred while fetching assessments")
        console.error("Error fetching assessments:", err)
      } finally {
        setIsLoading(false)
      }
    }

    // Only fetch if we have a userId and userEmail
    if (userId && userEmail) {
      fetchAssessments()
    } else {
      setIsLoading(false)
      setError("User information is missing")
    }
  }, [userId, userEmail, businessUnit])

  // Filter assessments by type and status
  const selfAssessments = assessments.filter(a => a.relationshipType === 'self')
  const managerAssessments = assessments.filter(a => a.relationshipType === 'manager')
  const peerAssessments = assessments.filter(a => a.relationshipType === 'peer')

  // Get active assessment cycles that the user can participate in
  // Filter out cycles that have interval restrictions the user doesn't meet
  const availableCycles = cycles.filter(c => {
    // Only include active cycles
    if (c.status !== 'active') return false;

    // If the cycle is recurring and has a next assessment date
    if (c.isRecurring && c.nextAssessmentDate) {
      // Check if the user can take an assessment based on the interval
      return c.canTakeAssessment;
    }

    // If not recurring or no next assessment date, include it
    return true;
  })

  const inProgressAssessments = assessments.filter(a => a.status === 'in_progress')
  const completedAssessments = assessments.filter(a => a.status === 'completed')

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800 px-2.5 py-0.5 rounded-full font-medium">
            <span className="flex items-center gap-1.5">
              <div className="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400"></div>
              Pending
            </span>
          </Badge>
        )
      case 'in_progress':
        return (
          <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/30 dark:text-amber-300 dark:border-amber-800 px-2.5 py-0.5 rounded-full font-medium">
            <span className="flex items-center gap-1.5">
              <div className="w-2 h-2 rounded-full bg-amber-500 dark:bg-amber-400 animate-pulse"></div>
              In Progress
            </span>
          </Badge>
        )
      case 'completed':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800 px-2.5 py-0.5 rounded-full font-medium">
            <span className="flex items-center gap-1.5">
              <div className="w-2 h-2 rounded-full bg-green-500 dark:bg-green-400"></div>
              Completed
            </span>
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Get relationship type badge
  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'self':
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-800 px-2.5 py-0.5 rounded-full font-medium">
            Self Assessment
          </Badge>
        )
      case 'manager':
        return (
          <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-300 dark:border-indigo-800 px-2.5 py-0.5 rounded-full font-medium">
            Manager Assessment
          </Badge>
        )
      case 'peer':
        return (
          <Badge variant="outline" className="bg-teal-50 text-teal-700 border-teal-200 dark:bg-teal-900/30 dark:text-teal-300 dark:border-teal-800 px-2.5 py-0.5 rounded-full font-medium">
            Peer Assessment
          </Badge>
        )
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  // Handle starting a new assessment
  const handleStartAssessment = (assessment: any) => {
    if (assessment.relationshipType === 'self') {
      router.push(`/assessment/self/${assessment.id}`)
    } else if (assessment.relationshipType === 'manager') {
      router.push(`/assessment/manager/${assessment.id}`)
    } else if (assessment.relationshipType === 'peer') {
      router.push(`/assessment/peer/${assessment.id}`)
    }
  }

  // Handle starting a new assessment from a cycle
  const handleStartCycleAssessment = async (cycle: any) => {
    try {
      // Look for an existing self-assessment for this user in this cycle
      const existingAssessment = assessments.find(a =>
        a.cycleId === cycle.id &&
        a.relationshipType === 'self' &&
        a.reviewerId === userId
      );

      if (existingAssessment) {
        // If assessment exists, redirect to it
        router.push(`/assessment/self/${existingAssessment.id}`)
      } else {
        // If no assessment exists, redirect to the regular self-assessment flow
        // This will create a new assessment
        router.push('/assessment/self')
      }
    } catch (error) {
      console.error('Error starting cycle assessment:', error)
      setError('Failed to start assessment')
    }
  }

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex justify-center py-8">
            <div className="h-8 w-8 rounded-full border-2 border-primary/30 border-t-primary animate-spin"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="p-4 rounded-lg border border-red-200 bg-red-50 text-red-800">
            {error}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="text-2xl font-bold text-primary">My Assessment Dashboard</CardTitle>
        <CardDescription className="text-base">
          Track your progress and manage all your assessments in one place
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Assessment Summary Section */}
        <div className="mb-8 p-5 bg-gradient-to-br from-primary/5 to-primary/10 rounded-xl border border-primary/20">
          <h3 className="text-lg font-semibold mb-4 text-primary">Assessment Summary</h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Self Assessment Summary */}
            <div className="bg-card p-4 rounded-lg shadow-sm border border-border">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/50">
                  <FileText className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <p className="font-medium">Self Assessment</p>
                  <p className="text-xs text-muted-foreground">Your self-evaluation</p>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span>Completed</span>
                  <span className="font-medium">{selfAssessments.filter(a => a.status === 'completed').length}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span>In Progress</span>
                  <span className="font-medium">{selfAssessments.filter(a => a.status === 'in_progress').length}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span>Available Cycles</span>
                  <span className="font-medium">{availableCycles.length}</span>
                </div>
              </div>
            </div>

            {/* Manager Assessment Summary */}
            <div className="bg-card p-4 rounded-lg shadow-sm border border-border">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-full bg-indigo-100 dark:bg-indigo-900/50">
                  <UserCheck className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                </div>
                <div>
                  <p className="font-medium">Manager Assessment</p>
                  <p className="text-xs text-muted-foreground">Your manager&apos;s evaluation</p>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span>Completed</span>
                  <span className="font-medium">{managerAssessments.filter(a => a.status === 'completed').length}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span>In Progress</span>
                  <span className="font-medium">{managerAssessments.filter(a => a.status === 'in_progress').length}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span>Pending</span>
                  <span className="font-medium">{managerAssessments.filter(a => a.status === 'pending').length}</span>
                </div>
              </div>
            </div>

            {/* Peer Assessment Summary */}
            <div className="bg-card p-4 rounded-lg shadow-sm border border-border">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-full bg-teal-100 dark:bg-teal-900/50">
                  <Users className="h-5 w-5 text-teal-600 dark:text-teal-400" />
                </div>
                <div>
                  <p className="font-medium">Peer Assessment</p>
                  <p className="text-xs text-muted-foreground">Feedback from peers</p>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span>Completed</span>
                  <span className="font-medium">{peerAssessments.filter(a => a.status === 'completed').length}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span>In Progress</span>
                  <span className="font-medium">{peerAssessments.filter(a => a.status === 'in_progress').length}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span>Pending</span>
                  <span className="font-medium">{peerAssessments.filter(a => a.status === 'pending').length}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/40 dark:to-blue-900/20 border-blue-200 dark:border-blue-800 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/50 shadow-inner">
                    <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Available</p>
                    <p className="text-3xl font-bold text-blue-700 dark:text-blue-300">{availableCycles.length}</p>
                    <p className="text-xs text-blue-600/70 dark:text-blue-400/70 mt-1">Active assessment cycles</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-950/40 dark:to-amber-900/20 border-amber-200 dark:border-amber-800 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-3 rounded-full bg-amber-100 dark:bg-amber-900/50 shadow-inner">
                    <Clock className="h-6 w-6 text-amber-600 dark:text-amber-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-amber-600 dark:text-amber-400">In Progress</p>
                    <p className="text-3xl font-bold text-amber-700 dark:text-amber-300">{inProgressAssessments.length}</p>
                    <p className="text-xs text-amber-600/70 dark:text-amber-400/70 mt-1">Assessments you&apos;ve started</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/40 dark:to-green-900/20 border-green-200 dark:border-green-800 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/50 shadow-inner">
                    <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-green-600 dark:text-green-400">Completed</p>
                    <p className="text-3xl font-bold text-green-700 dark:text-green-300">{completedAssessments.length}</p>
                    <p className="text-xs text-green-600/70 dark:text-green-400/70 mt-1">Assessments you&apos;ve finished</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-6 bg-muted/30 p-1 rounded-lg">
            <TabsTrigger
              value="available"
              className="data-[state=active]:bg-blue-100 data-[state=active]:text-blue-700 dark:data-[state=active]:bg-blue-900/30 dark:data-[state=active]:text-blue-300 rounded-md"
            >
              <span className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <span>Available</span>
                {availableCycles.length > 0 && (
                  <span className="ml-1 bg-blue-200 dark:bg-blue-800 text-blue-800 dark:text-blue-200 text-xs rounded-full px-2 py-0.5">
                    {availableCycles.length}
                  </span>
                )}
              </span>
            </TabsTrigger>
            <TabsTrigger
              value="in-progress"
              className="data-[state=active]:bg-amber-100 data-[state=active]:text-amber-700 dark:data-[state=active]:bg-amber-900/30 dark:data-[state=active]:text-amber-300 rounded-md"
            >
              <span className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span>In Progress</span>
                {inProgressAssessments.length > 0 && (
                  <span className="ml-1 bg-amber-200 dark:bg-amber-800 text-amber-800 dark:text-amber-200 text-xs rounded-full px-2 py-0.5">
                    {inProgressAssessments.length}
                  </span>
                )}
              </span>
            </TabsTrigger>
            <TabsTrigger
              value="completed"
              className="data-[state=active]:bg-green-100 data-[state=active]:text-green-700 dark:data-[state=active]:bg-green-900/30 dark:data-[state=active]:text-green-300 rounded-md"
            >
              <span className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                <span>Completed</span>
                {completedAssessments.length > 0 && (
                  <span className="ml-1 bg-green-200 dark:bg-green-800 text-green-800 dark:text-green-200 text-xs rounded-full px-2 py-0.5">
                    {completedAssessments.length}
                  </span>
                )}
              </span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="available">
            {/* Show interval restriction message if any cycle has one */}
            {cycles.some(c => c.isRecurring && c.nextAssessmentDate && !c.canTakeAssessment) && (
              <div className="p-6 bg-amber-50 border border-amber-200 rounded-lg mb-4">
                <div className="flex items-start gap-3">
                  <div className="p-2 rounded-full bg-amber-100 mt-1">
                    <Clock className="h-5 w-5 text-amber-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-amber-800 text-lg mb-1">Assessment Interval Restriction</h3>
                    <p className="text-amber-700">
                      {cycles.filter(c => c.isRecurring && c.nextAssessmentDate && !c.canTakeAssessment).map(c => {
                        const nextDate = new Date(c.nextAssessmentDate);
                        const formattedDate = nextDate.toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        });
                        return `You can only take an assessment for &quot;${c.name}&quot; once every ${c.intervalMonths} months. Your next assessment can be taken after ${formattedDate}.`;
                      }).join(' ')}
                    </p>
                    <p className="text-amber-700 mt-2">
                      This restriction ensures that assessments are spaced out appropriately to track your progress over time.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {availableCycles.length > 0 ? (
              <div className="space-y-4">
                {availableCycles.map((cycle) => (
                  <Card key={cycle.id} className="overflow-hidden hover:shadow-md transition-shadow border-blue-100 dark:border-blue-900/50">
                    <div className="p-5">
                      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mb-4">
                        <div>
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold text-lg text-primary">{cycle.name}</h3>
                            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800 px-2.5 py-0.5 rounded-full font-medium">
                              <span className="flex items-center gap-1.5">
                                <div className="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400"></div>
                                Active
                              </span>
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-3">{cycle.description}</p>
                          <div className="flex flex-wrap items-center gap-3 text-sm">
                            <span className="flex items-center gap-1.5 bg-muted/30 px-2 py-1 rounded-md">
                              <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                              <span>Start: {new Date(cycle.startDate).toLocaleDateString()}</span>
                            </span>
                            <span className="flex items-center gap-1.5 bg-muted/30 px-2 py-1 rounded-md">
                              <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                              <span>End: {new Date(cycle.endDate).toLocaleDateString()}</span>
                            </span>
                            {cycle.isRecurring && (
                              <span className="flex items-center gap-1.5 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-md border border-blue-100 dark:border-blue-800">
                                <Clock className="h-4 w-4" />
                                <span>Recurring: Every {cycle.intervalMonths} months</span>
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="flex flex-col gap-2">
                          <Button
                            onClick={() => handleStartCycleAssessment(cycle)}
                            className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
                          >
                            <span className="flex items-center gap-2">
                              <FileText className="h-4 w-4" />
                              Start Self Assessment
                            </span>
                          </Button>
                        </div>
                      </div>

                      {/* Assessment types available in this cycle */}
                      <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-3">
                        <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg border border-purple-100 dark:border-purple-800">
                          <div className="flex items-center gap-2 mb-1">
                            <div className="p-1.5 rounded-full bg-purple-100 dark:bg-purple-800">
                              <FileText className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                            </div>
                            <span className="font-medium text-purple-700 dark:text-purple-300">Self Assessment</span>
                          </div>
                          <div className="text-xs text-purple-600/70 dark:text-purple-400/70">
                            Assess your own skills and competencies
                          </div>
                        </div>

                        <div className="bg-indigo-50 dark:bg-indigo-900/20 p-3 rounded-lg border border-indigo-100 dark:border-indigo-800">
                          <div className="flex items-center gap-2 mb-1">
                            <div className="p-1.5 rounded-full bg-indigo-100 dark:bg-indigo-800">
                              <UserCheck className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                            </div>
                            <span className="font-medium text-indigo-700 dark:text-indigo-300">Manager Assessment</span>
                          </div>
                          <div className="text-xs text-indigo-600/70 dark:text-indigo-400/70">
                            Your manager will assess your skills
                          </div>
                        </div>

                        <div className="bg-teal-50 dark:bg-teal-900/20 p-3 rounded-lg border border-teal-100 dark:border-teal-800">
                          <div className="flex items-center gap-2 mb-1">
                            <div className="p-1.5 rounded-full bg-teal-100 dark:bg-teal-800">
                              <Users className="h-4 w-4 text-teal-600 dark:text-teal-400" />
                            </div>
                            <span className="font-medium text-teal-700 dark:text-teal-300">Peer Assessment</span>
                          </div>
                          <div className="text-xs text-teal-600/70 dark:text-teal-400/70">
                            Peer feedback on your skills
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 px-6 text-muted-foreground bg-muted/10 rounded-lg border border-dashed border-blue-200 dark:border-blue-900/50">
                <FileText className="h-12 w-12 mx-auto mb-3 text-blue-300 dark:text-blue-800" />
                <p className="text-lg font-medium mb-1">No active assessment cycles</p>
                <p className="text-sm max-w-md mx-auto mb-4">Assessment cycles must be created by an administrator before they appear here.</p>

                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-100 dark:border-blue-800 max-w-md mx-auto text-left">
                  <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-2 flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    How Assessment Cycles Work
                  </h4>
                  <ul className="text-sm space-y-2 text-blue-700 dark:text-blue-400">
                    <li className="flex items-start gap-2">
                      <span className="mt-1">1.</span>
                      <span><strong>Admin Creation:</strong> An administrator creates assessment cycles in the admin panel</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="mt-1">2.</span>
                      <span><strong>Activation:</strong> The administrator sets the cycle status to &quot;active&quot;</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="mt-1">3.</span>
                      <span><strong>Participation:</strong> Active cycles appear here for you to participate in</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="mt-1">4.</span>
                      <span><strong>Assessment:</strong> You can start a self-assessment when a cycle is available</span>
                    </li>
                  </ul>

                  <div className="mt-4 pt-3 border-t border-blue-200 dark:border-blue-800">
                    <p className="text-sm text-blue-700 dark:text-blue-400">
                      If you need to participate in an assessment cycle, please contact your administrator.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="in-progress">
            {inProgressAssessments.length > 0 ? (
              <div className="space-y-4">
                {inProgressAssessments.map((assessment) => (
                  <Card key={assessment.id} className="overflow-hidden hover:shadow-md transition-shadow border-amber-100 dark:border-amber-900/50">
                    <div className="p-5 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                      <div>
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-lg text-primary">{assessment.name}</h3>
                          {getTypeBadge(assessment.relationshipType)}
                        </div>
                        <div className="flex flex-wrap items-center gap-3 text-sm text-muted-foreground">
                          {assessment.targetUserName && (
                            <span className="flex items-center gap-1.5 bg-muted/30 px-2 py-1 rounded-md">
                              <UserCheck className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                              <span className="font-medium">{assessment.targetUserName}</span>
                            </span>
                          )}
                          <span className="flex items-center gap-1.5 bg-muted/30 px-2 py-1 rounded-md">
                            <Calendar className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                            <span>Updated: {new Date(assessment.updatedAt).toLocaleDateString()}</span>
                          </span>
                          {assessment.cycleId && (
                            <span className="flex items-center gap-1.5 bg-muted/30 px-2 py-1 rounded-md">
                              <Users className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                              <span>{cycles.find(c => c.id === assessment.cycleId)?.name || 'Assessment Cycle'}</span>
                            </span>
                          )}
                        </div>
                      </div>
                      <Button
                        onClick={() => handleStartAssessment(assessment)}
                        className="bg-amber-600 hover:bg-amber-700 text-white shadow-sm"
                      >
                        <span className="flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          Continue Assessment
                        </span>
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 px-6 text-muted-foreground bg-muted/10 rounded-lg border border-dashed border-amber-200 dark:border-amber-900/50">
                <Clock className="h-12 w-12 mx-auto mb-3 text-amber-300 dark:text-amber-800" />
                <p className="text-lg font-medium mb-1">No in-progress assessments</p>
                <p className="text-sm max-w-md mx-auto mb-4">When you start working on assessments but don&apos;t complete them, they will appear here.</p>

                <div className="bg-amber-50 dark:bg-amber-900/20 p-4 rounded-lg border border-amber-100 dark:border-amber-800 max-w-md mx-auto text-left">
                  <h4 className="font-medium text-amber-800 dark:text-amber-300 mb-2 flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    How assessments become &quot;In Progress&quot;
                  </h4>
                  <ul className="text-sm space-y-2 text-amber-700 dark:text-amber-400">
                    <li className="flex items-start gap-2">
                      <span className="mt-1">1.</span>
                      <span><strong>Start an assessment:</strong> Click &quot;Start Self Assessment&quot; on an available cycle</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="mt-1">2.</span>
                      <span><strong>Fill out partially:</strong> Start filling out the assessment form</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="mt-1">3.</span>
                      <span><strong>Save without submitting:</strong> Click &quot;Save&quot; instead of &quot;Submit&quot; (or navigate away)</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="mt-1">4.</span>
                      <span><strong>Resume later:</strong> Return to continue your assessment from where you left off</span>
                    </li>
                  </ul>

                  <div className="mt-4 pt-3 border-t border-amber-200 dark:border-amber-800">
                    <p className="text-sm text-amber-700 dark:text-amber-400">
                      Note: Assessments remain &quot;In Progress&quot; until you explicitly submit them, at which point they move to &quot;Completed&quot;.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="completed">
            {completedAssessments.length > 0 ? (
              <div className="space-y-4">
                {completedAssessments.map((assessment) => (
                  <Card key={assessment.id} className="overflow-hidden hover:shadow-md transition-shadow border-green-100 dark:border-green-900/50">
                    <div className="p-5 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                      <div>
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-lg text-primary">{assessment.name}</h3>
                          {getTypeBadge(assessment.relationshipType)}
                        </div>
                        <div className="flex flex-wrap items-center gap-3 text-sm text-muted-foreground">
                          {assessment.targetUserName && (
                            <span className="flex items-center gap-1.5 bg-muted/30 px-2 py-1 rounded-md">
                              <UserCheck className="h-4 w-4 text-green-600 dark:text-green-400" />
                              <span className="font-medium">{assessment.targetUserName}</span>
                            </span>
                          )}
                          <span className="flex items-center gap-1.5 bg-muted/30 px-2 py-1 rounded-md">
                            <Calendar className="h-4 w-4 text-green-600 dark:text-green-400" />
                            <span>Completed: {new Date(assessment.updatedAt).toLocaleDateString()}</span>
                          </span>
                          {assessment.cycleId && (
                            <span className="flex items-center gap-1.5 bg-muted/30 px-2 py-1 rounded-md">
                              <Users className="h-4 w-4 text-green-600 dark:text-green-400" />
                              <span>{cycles.find(c => c.id === assessment.cycleId)?.name || 'Assessment Cycle'}</span>
                            </span>
                          )}
                        </div>
                      </div>
                      <Button
                        onClick={() => handleStartAssessment(assessment)}
                        className="bg-green-600 hover:bg-green-700 text-white shadow-sm"
                      >
                        <span className="flex items-center gap-2">
                          <Eye className="h-4 w-4" />
                          View Results
                        </span>
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 px-6 text-muted-foreground bg-muted/10 rounded-lg border border-dashed border-green-200 dark:border-green-900/50">
                <CheckCircle className="h-12 w-12 mx-auto mb-3 text-green-300 dark:text-green-800" />
                <p className="text-lg font-medium mb-1">No completed assessments</p>
                <p className="text-sm max-w-md mx-auto mb-4">When you finish assessments, they will appear here for future reference.</p>

                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-100 dark:border-green-800 max-w-md mx-auto text-left">
                  <h4 className="font-medium text-green-800 dark:text-green-300 mb-2 flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    About Completed Assessments
                  </h4>
                  <ul className="text-sm space-y-2 text-green-700 dark:text-green-400">
                    <li className="flex items-start gap-2">
                      <span className="mt-1">•</span>
                      <span>Assessments are marked as &quot;Completed&quot; when you submit them</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="mt-1">•</span>
                      <span>Completed assessments can be viewed but not edited</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="mt-1">•</span>
                      <span>Your completed assessments provide valuable data for your skill development</span>
                    </li>
                  </ul>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
