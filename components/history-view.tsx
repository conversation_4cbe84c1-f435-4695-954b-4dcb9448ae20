"use client"

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { getSkillHistory, getAllSkillsHistory } from "@/lib/actions/assessment"
import { skillsData } from "@/lib/skills-data"
// Import consolidated chart components
import { SkillHistory<PERSON>hart, AllSkillsHistoryChart } from "@/components/history-charts"
import { format } from "date-fns"
import { ArrowLeft, Calendar } from "lucide-react"

interface HistoryViewProps {
  userId: string
  assessmentName: string
  onBack: () => void
}

export default function HistoryView({ userId, assessmentName, onBack }: HistoryViewProps) {
  const [selectedSkill, setSelectedSkill] = useState(skillsData[0].id)
  const [skillHistory, setSkillHistory] = useState<any[]>([])
  const [allHistory, setAllHistory] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadSkillHistory = useCallback(async (skillId: string) => {
    setLoading(true)
    setError(null)

    try {
      const result = await getSkillHistory(userId, assessmentName, skillId)

      if (result.success && result.data.length > 0) {
        // Format the data for the chart
        const formattedData = result.data.map((item: { date: string | number | Date; level: any }) => ({
          date: format(new Date(item.date), "MMM d, yyyy"),
          level: item.level,
        }))

        setSkillHistory(formattedData)
      } else {
        setSkillHistory([])
        if (!result.success) {
          setError(result.message || "Failed to load history data")
        }
      }
    } catch (error) {
      console.error("Error loading skill history:", error)
      setError("An error occurred while loading history data")
    } finally {
      setLoading(false)
    }
  }, [userId, assessmentName])

  const loadAllHistory = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const result = await getAllSkillsHistory(userId, assessmentName)

      if (result.success && result.data.length > 0) {
        // Format the data for the chart
        const formattedData = result.data.map((entry: { date: string | number | Date; skills: { [x: string]: any } }) => {
          // Convert the date to a formatted string
          const formattedDate = format(new Date(entry.date), "MMM d, yyyy")

          // Extract skills from the entry
          const skillsObj: { [key: string]: any } = {}

          // For each skill in the entry, add it to the skills object
          Object.keys(entry.skills).forEach(skillId => {
            skillsObj[skillId] = entry.skills[skillId]
          })

          return {
            date: formattedDate,
            ...skillsObj
          }
        })

        setAllHistory(formattedData)
      } else {
        setAllHistory([])
        if (!result.success) {
          setError(result.message || "Failed to load history data")
        }
      }
    } catch (error) {
      console.error("Error loading all skills history:", error)
      setError("An error occurred while loading history data")
      setAllHistory([])
    } finally {
      setLoading(false)
    }
  }, [userId, assessmentName])

  useEffect(() => {
    if (userId && assessmentName) {
      loadSkillHistory(selectedSkill)
      loadAllHistory()
    }
  }, [userId, assessmentName, selectedSkill, loadSkillHistory, loadAllHistory])

  const getSkillName = (skillId: string) => {
    return skillsData.find((skill) => skill.id === skillId)?.category || skillId
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center gap-3">
        <Button
          variant="outline"
          size="icon"
          onClick={onBack}
          className="bg-card hover:bg-blue-500 border-primary/40 hover:border-blue-600 text-primary hover:text-white h-10 w-10 shadow-md transition-colors duration-200"
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <div className="flex items-center gap-3">
          <div className="h-8 w-1 bg-primary rounded"></div>
          <h2 className="text-2xl font-bold text-primary">Assessment History</h2>
        </div>
      </div>

      <Tabs defaultValue="single" className="w-full">
        <TabsList className="w-full max-w-md mx-auto grid grid-cols-2 mb-6">
          <TabsTrigger value="single" className="data-[state=active]:bg-primary data-[state=active]:text-background hover:bg-blue-500 hover:text-white font-medium shadow-md border border-primary/40 transition-colors duration-200">
            <span className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Single Skill History
            </span>
          </TabsTrigger>
          <TabsTrigger value="all" className="data-[state=active]:bg-primary data-[state=active]:text-background hover:bg-blue-500 hover:text-white font-medium shadow-md border border-primary/40 transition-colors duration-200">
            <span className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              All Skills Comparison
            </span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="single" className="space-y-6">
          <div className="flex items-end gap-4 bg-card p-4 rounded-lg shadow-md border border-border">
            <div className="w-full max-w-xs">
              <label className="block text-sm font-medium mb-2 text-primary/90">Select Skill to View History</label>
              <Select value={selectedSkill} onValueChange={setSelectedSkill}>
                <SelectTrigger className="bg-card border-primary/20 focus:ring-primary/20">
                  <SelectValue placeholder="Select a skill" />
                </SelectTrigger>
                <SelectContent>
                  {skillsData.map((skill) => (
                    <SelectItem key={skill.id} value={skill.id}>
                      {skill.category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Card className="shadow-sm border-primary/10">
            <CardHeader className="bg-muted/20 border-b">
              <CardTitle className="flex items-center gap-2 text-primary/90">
                <Calendar className="h-5 w-5 text-primary" />
                {getSkillName(selectedSkill)} - Progress Over Time
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              {loading ? (
                <div className="h-80 flex items-center justify-center">
                  <div className="flex flex-col items-center gap-3">
                    <div className="h-8 w-8 rounded-full border-4 border-primary/30 border-t-primary animate-spin"></div>
                    <p className="text-muted-foreground">Loading history data...</p>
                  </div>
                </div>
              ) : error ? (
                <div className="h-80 flex items-center justify-center p-6 bg-red-50 rounded-lg border border-red-200">
                  <div className="flex items-center gap-2 text-red-600">
                    <div className="h-2 w-2 rounded-full bg-red-500"></div>
                    {error}
                  </div>
                </div>
              ) : (
                <SkillHistoryChart
                  data={skillHistory}
                  title={`${getSkillName(selectedSkill)} - Progress Over Time`}
                  skillName={getSkillName(selectedSkill)}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="all" className="space-y-6">
          <Card className="shadow-sm border-primary/10">
            <CardHeader className="bg-muted/20 border-b">
              <CardTitle className="flex items-center gap-2 text-primary/90">
                <Calendar className="h-5 w-5 text-primary" />
                All Skills - Progress Comparison
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              {loading ? (
                <div className="h-80 flex items-center justify-center">
                  <div className="flex flex-col items-center gap-3">
                    <div className="h-8 w-8 rounded-full border-4 border-primary/30 border-t-primary animate-spin"></div>
                    <p className="text-muted-foreground">Loading history data...</p>
                  </div>
                </div>
              ) : error ? (
                <div className="h-80 flex items-center justify-center p-6 bg-red-50 rounded-lg border border-red-200">
                  <div className="flex items-center gap-2 text-red-600">
                    <div className="h-2 w-2 rounded-full bg-red-500"></div>
                    {error}
                  </div>
                </div>
              ) : (
                <AllSkillsHistoryChart
                  data={allHistory}
                  title="All Skills - Progress Comparison"
                  skills={skillsData}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
