"use client"

import { useState, useEffect, useCallback } from "react"
import { saveAssessment, getUserAssessments, loadAssessment, type AssessmentData } from "@/lib/actions/assessment"
import { getProjectSkillTemplate } from "@/lib/actions/projects"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Save, Download, History } from "lucide-react"
import { ratingScale, type Skill } from "@/lib/skills-data"
import { ratingScale as icRatingScale, type ICSkill } from "@/lib/ic-skills-data"
import { type ProjectSkill } from "@/lib/project-skills-data"
import { legacyBusinessUnitMapping } from "@/lib/models/skill-template"
import ICLevelGuidance from "./ic-level-guidance"
import ResultsSummary from "./results-summary"
import HistoryView from "./history-view"
import ProjectSkillAssessment from "./project-skill-assessment"
import MultiProjectSkillAssessment from "./multi-project-skill-assessment"
import ProjectSelector from "./project-selector"



interface SkillAssessmentProps {
  userId: string
  userName: string
  userEmail: string
  businessUnit: string
  careerLevel: string
  jobRole: string
  assessmentType?: 'self' | 'manager' | 'peer'
  targetUserId?: string
  targetUserName?: string
  cycleId?: string
  projectId?: string
  projectName?: string
}

export default function SkillAssessment({
  userId,
  userName,
  userEmail,
  businessUnit,
  careerLevel,
  jobRole,
  assessmentType = 'self',
  targetUserId,
  targetUserName,
  cycleId,
  projectId: initialProjectId,
  projectName: initialProjectName,
}: SkillAssessmentProps) {
  const [assessmentData, setAssessmentData] = useState<Skill[]>([])
  const [icAssessmentData, setICAssessmentData] = useState<ICSkill[]>([])
  const [projectSkillsData, setProjectSkillsData] = useState<ProjectSkill[]>([])
  const [userProjectSkills, setUserProjectSkills] = useState<any[]>([])
  const [activeTab, setActiveTab] = useState("core")
  const [skillTemplate, setSkillTemplate] = useState<any>(null)
  const [projectSkillTemplate, setProjectSkillTemplate] = useState<any>(null)
  const [showResults, setShowResults] = useState(false)

  // Map the business unit to the consolidated business unit
  const mappedBusinessUnit = legacyBusinessUnitMapping[businessUnit.toLowerCase()] || businessUnit.toLowerCase()

  // Determine if we should use IC skills based on mapped business unit
  const useICSkills = mappedBusinessUnit === 'engineering-ic'
  const currentSkills = useICSkills ? icAssessmentData : assessmentData
  const currentRatingScale = useICSkills ? icRatingScale : ratingScale
  const [showHistory, setShowHistory] = useState(false)
  const [projectId, setProjectId] = useState<string | undefined>(initialProjectId)
  const [projectName, setProjectName] = useState<string | undefined>(initialProjectName)
  const [assessmentName, setAssessmentName] = useState(() => {
    if (assessmentType === 'self') {
      return `My Assessment ${new Date().toLocaleDateString()}`
    } else if (assessmentType === 'manager') {
      return `Manager Assessment for ${targetUserName || 'Team Member'} - ${new Date().toLocaleDateString()}`
    } else {
      return `Peer Assessment for ${targetUserName || 'Team Member'} - ${new Date().toLocaleDateString()}`
    }
  })
  const [userAssessments, setUserAssessments] = useState<{ name: string; date: string }[]>([])
  const [canTakeNewAssessment, setCanTakeNewAssessment] = useState(true)
  const [nextAssessmentDate, setNextAssessmentDate] = useState<string | null>(null)
  const [allowUpdates, setAllowUpdates] = useState(false)
  const [intervalMonths, setIntervalMonths] = useState(6)
  const [isSaving, setIsSaving] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ text: string; type: "success" | "error" } | null>(null)

  const handleSkillLevelChange = (skillId: string, level: string) => {
    const levelValue = level === "-" ? null : Number.parseInt(level)

    if (useICSkills) {
      const updatedData = icAssessmentData.map((skill) =>
        skill.id === skillId ? { ...skill, currentLevel: levelValue } : skill,
      )
      setICAssessmentData(updatedData)
    } else {
      const updatedData = assessmentData.map((skill) =>
        skill.id === skillId ? { ...skill, currentLevel: levelValue } : skill,
      )
      setAssessmentData(updatedData)
    }
  }

  const fetchUserAssessments = useCallback(async () => {
    if (!userId) return

    // Get user assessments from the database
    // If this is a manager or peer assessment, we need to pass the target user ID
    const targetId = assessmentType !== 'self' ? targetUserId : undefined
    const result = await getUserAssessments(userId, mappedBusinessUnit, assessmentType, targetId, cycleId, projectId)
    if (result.success) {
      setUserAssessments(result.data)
      setCanTakeNewAssessment(result.canTakeNewAssessment)
      setNextAssessmentDate(result.nextAssessmentDate)
      setAllowUpdates(result.allowUpdates)
      setIntervalMonths(result.intervalMonths)

      // If a skill template is available for the user's business unit, use it
      if (result.skillTemplate) {
        setSkillTemplate(result.skillTemplate)
        // Initialize with template skills if no assessment is loaded
        if (assessmentName.includes(new Date().toLocaleDateString())) {
          if (useICSkills) {
            setICAssessmentData(result.skillTemplate.coreSkills.map((skill: any) => ({ ...skill, currentLevel: null })))
          } else {
            setAssessmentData(result.skillTemplate.coreSkills.map((skill: any) => ({ ...skill, currentLevel: null })))
          }
        }
      }

      // If user project skills are available, use them
      if (result.userProjectSkills && result.userProjectSkills.length > 0) {
        setUserProjectSkills(result.userProjectSkills)

        // Initialize project skills with the first project's skills if no assessment is loaded
        if (assessmentName.includes(new Date().toLocaleDateString()) && result.userProjectSkills[0]?.skills) {
          setProjectSkillsData(result.userProjectSkills[0].skills)

          // Set the project ID and name
          if (result.userProjectSkills[0].projectId && !projectId) {
            setProjectId(result.userProjectSkills[0].projectId)
            setProjectName(result.userProjectSkills[0].projectName)
          }
        }
      } else if (result.skillTemplate) {
        // Fall back to template project skills if no user project skills
        setProjectSkillsData(result.skillTemplate.projectSkills.map((skill: any) => ({ ...skill, currentLevel: null })))
      }

      // If user can't take a new assessment and there's an interval restriction, show a message
      if (!result.canTakeNewAssessment && result.nextAssessmentDate && result.intervalMonths > 0) {
        const formattedDate = new Date(result.nextAssessmentDate).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });

        setMessage({
          text: `You can only take an assessment once every ${result.intervalMonths} months. Your next assessment can be taken after ${formattedDate}.`,
          type: "error"
        })
      }
    } else {
      // If no assessments found or error occurred, use empty array
      setUserAssessments([])
      setCanTakeNewAssessment(true)
      setNextAssessmentDate(null)
      // Use default values from the error response
      if (result.allowUpdates !== undefined) setAllowUpdates(result.allowUpdates)
      if (result.intervalMonths !== undefined) setIntervalMonths(result.intervalMonths)
    }
  }, [userId, mappedBusinessUnit, assessmentName, assessmentType, cycleId, targetUserId, projectId, useICSkills])

  useEffect(() => {
    if (userId) {
      fetchUserAssessments()
    }
  }, [userId, fetchUserAssessments, mappedBusinessUnit, assessmentType, targetUserId, cycleId, projectId])

  const handleLoadAssessment = async (name: string) => {
    if (!userId) return

    setIsLoading(true)
    // If this is a manager or peer assessment, we need to pass the target user ID
    const targetId = assessmentType !== 'self' ? targetUserId : undefined
    const result = await loadAssessment(userId, name, assessmentType, targetId, cycleId, projectId)
    setIsLoading(false)

    if (result.success && result.data) {
      setAssessmentName(name)
      setMessage({ text: "Assessment loaded successfully", type: "success" })

      // Load skills based on business unit type
      if (useICSkills) {
        setICAssessmentData(result.data.skills)
      } else {
        setAssessmentData(result.data.skills)
      }
      setProjectSkillsData(result.data.projectSkills || [])

      // Update project information if available
      if (result.data.projectId && result.data.projectName) {
        setProjectId(result.data.projectId)
        setProjectName(result.data.projectName)
      }

      // If a skill template is available for the assessment's business unit, use it
      if (result.skillTemplate) {
        setSkillTemplate(result.skillTemplate)
      }

      // If a project skill template is available, use it
      if (result.projectSkillTemplate) {
        setProjectSkillTemplate(result.projectSkillTemplate)
      }

      // If user project skills are available, use them
      if (result.userProjectSkills && result.userProjectSkills.length > 0) {
        setUserProjectSkills(result.userProjectSkills)
      }
    } else {
      setMessage({ text: result.message || "Failed to load assessment", type: "error" })
    }
  }

  const handleSaveAssessment = async () => {
    if (!userId) {
      setMessage({ text: "You must be signed in to save assessments", type: "error" })
      return
    }

    // Check if user can take a new assessment (only if interval restriction is enabled)
    if (intervalMonths > 0 && !canTakeNewAssessment) {
      if (nextAssessmentDate) {
        const formattedDate = new Date(nextAssessmentDate).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });

        setMessage({
          text: `You can only take an assessment once every ${intervalMonths} months. Your next assessment can be taken after ${formattedDate}.`,
          type: "error"
        })
      } else {
        setMessage({
          text: `You can only take an assessment once every ${intervalMonths} months.`,
          type: "error"
        })
      }
      return
    }

    // Validate assessment name
    if (!assessmentName.trim()) {
      setMessage({ text: "Please enter a valid assessment name", type: "error" })
      return
    }

    // Check if any skills have been assessed
    const hasAssessedSkills = currentSkills.some(skill => skill.currentLevel !== null)
    if (!hasAssessedSkills) {
      setMessage({ text: "Please assess at least one skill before saving", type: "error" })
      return
    }

    // Check if this name already exists in the user's assessments
    const existingAssessment = userAssessments.find(a => a.name.toLowerCase() === assessmentName.toLowerCase())
    if (existingAssessment && !allowUpdates) {
      setMessage({
        text: "Assessments cannot be updated once saved. Please create a new assessment with a different name.",
        type: "error"
      })
      return
    }

    // If updates are allowed and the assessment exists, ask for confirmation
    if (existingAssessment && allowUpdates) {
      if (!window.confirm(`An assessment named "${assessmentName}" already exists. Do you want to update it?`)) {
        return
      }
    }

    setIsSaving(true)

    // If we have multiple project skills, use the currently selected project's skills
    // Otherwise, use the projectSkillsData
    let projectSkillsToSave = projectSkillsData;

    // If we have user project skills and a selected project, use those skills
    if (userProjectSkills.length > 0 && projectId) {
      const selectedProjectSkills = userProjectSkills.find(group => group.projectId === projectId);
      if (selectedProjectSkills) {
        projectSkillsToSave = selectedProjectSkills.skills;
      }
    }

    const data: AssessmentData = {
      userId,
      name: assessmentName,
      date: new Date().toISOString(),
      skills: currentSkills as any,
      projectSkills: projectSkillsToSave,
      userName,
      userEmail,
      businessUnit,
      careerLevel,
      jobRole,
      assessmentType,
      targetUserId: targetUserId,
      targetUserName: targetUserName,
      cycleId: cycleId,
      projectId: projectId,
      projectName: projectName,
    }

    const result = await saveAssessment(data)
    setIsSaving(false)

    if (result.success) {
      setMessage({ text: result.message || "Assessment saved successfully", type: "success" })
      // Refresh the list of assessments
      fetchUserAssessments()

      // Reset the form for a new assessment
      if (assessmentType === 'self') {
        setAssessmentName(`My Assessment ${new Date().toLocaleDateString()}`)
      } else if (assessmentType === 'manager') {
        setAssessmentName(`Manager Assessment for ${targetUserName || 'Team Member'} - ${new Date().toLocaleDateString()}`)
      } else {
        setAssessmentName(`Peer Assessment for ${targetUserName || 'Team Member'} - ${new Date().toLocaleDateString()}`)
      }

      // Use the business unit template if available, otherwise use empty arrays
      if (skillTemplate) {
        if (useICSkills) {
          setICAssessmentData(skillTemplate.coreSkills.map((skill: any) => ({ ...skill, currentLevel: null })))
        } else {
          setAssessmentData(skillTemplate.coreSkills.map((skill: any) => ({ ...skill, currentLevel: null })))
        }
        setProjectSkillsData(skillTemplate.projectSkills.map((skill: any) => ({ ...skill, currentLevel: null })))
      } else {
        // Use empty arrays instead of hardcoded values
        if (useICSkills) {
          setICAssessmentData([])
        } else {
          setAssessmentData([])
        }
        setProjectSkillsData([])
      }
    } else {
      setMessage({ text: result.message || "Failed to save assessment", type: "error" })
    }
  }

  const handleProjectSelect = async (newProjectId: string, newProjectName: string) => {
    setProjectId(newProjectId)
    setProjectName(newProjectName)

    // Always switch to project skills tab when a project is selected
    setActiveTab("project")

    // Load project skill template
    try {
      const result = await getProjectSkillTemplate(newProjectId, 'system')
      if (result.success) {
        setProjectSkillTemplate(result.data)

        // Initialize project skills with template
        if (result.data && result.data.skills) {
          setProjectSkillsData(result.data.skills.map((skill: any) => ({ ...skill, currentLevel: null })))
        }
      }
    } catch (error) {
      console.error("Error loading project skill template:", error)
      setMessage({ text: "Failed to load project skills", type: "error" })
    }

    // Refresh assessments with the new project filter
    fetchUserAssessments()
  }

  const exportAssessment = () => {
    const csvContent = [
      [
        "Skill Category",
        "Description",
        "Target CL2",
        "Target CL3",
        "Target CL4",
        "Target CL5",
        "Target CL6",
        "My Current Level",
      ],
      ...currentSkills.map((skill) => [
        skill.category,
        skill.description,
        skill.targetCL2,
        skill.targetCL3,
        skill.targetCL4,
        (skill as any).targetCL5 || 0,
        (skill as any).targetCL6 || 0,
        skill.currentLevel || "",
      ]),
    ]
      .map((row) => row.join(","))
      .join("\n")

    const blob = new Blob([csvContent], { type: "text/csv" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = "skills-assessment.csv"
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const getLevelColor = (current: number | null, target: number) => {
    if (!current) return "bg-gray-500"
    if (current < target) return "bg-red-700"
    if (current === target) return "bg-amber-500"
    return "bg-green-600"
  }

  if (showHistory) {
    return <HistoryView userId={userId} assessmentName={assessmentName} onBack={() => setShowHistory(false)} />
  }

  // If there's no skill template and no assessment data, show a message
  if (!skillTemplate && currentSkills.length === 0 && projectSkillsData.length === 0) {
    return (
      <div className="space-y-6">
        <Card className="p-8 text-center shadow-md border-primary/20 bg-card">
          <h2 className="text-xl font-medium text-primary mb-3">Waiting for Assessment Template</h2>
          <p className="text-muted-foreground mb-4">
            The assessment template for the <span className="font-semibold">{mappedBusinessUnit || 'current'}</span> business unit has not been created yet.
          </p>
          <p className="text-muted-foreground mb-6">
            An administrator needs to create the assessment template in the admin panel before you can proceed.
            Please check back later or contact your administrator.
          </p>

        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="p-6 mb-6 shadow-md border-primary/20 bg-card">
        <h2 className="text-xl font-medium text-primary mb-3">
          {assessmentType === 'self' && 'Skills Self-Assessment'}
          {assessmentType === 'manager' && `Manager Assessment for ${targetUserName || 'Team Member'}`}
          {assessmentType === 'peer' && `Peer Assessment for ${targetUserName || 'Team Member'}`}
        </h2>
        <p className="text-sm text-muted-foreground mb-4">
          {assessmentType === 'self' && 'Rate your current skill level from 1-6 for each category and see how you compare to target levels for different roles.'}
          {assessmentType === 'manager' && `Rate your team member's skill level from 1-6 for each category based on your observations and feedback.`}
          {assessmentType === 'peer' && `Rate your peer's skill level from 1-6 for each category based on your observations and interactions.`}
        </p>
        {projectName && (
          <div className="mb-4 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 border border-blue-200">
            Project: {projectName}
          </div>
        )}

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
          <div className="flex items-center gap-3 p-2 rounded-lg bg-muted/20">
            <div className="w-6 h-6 rounded-full bg-red-700 flex items-center justify-center text-white text-xs font-medium shadow-md border border-white/20">3</div>
            <span className="font-medium">Below target</span>
          </div>
          <div className="flex items-center gap-3 p-2 rounded-lg bg-muted/20">
            <div className="w-6 h-6 rounded-full bg-amber-500 flex items-center justify-center text-white text-xs font-medium shadow-md border border-white/20">3</div>
            <span className="font-medium">At target</span>
          </div>
          <div className="flex items-center gap-3 p-2 rounded-lg bg-muted/20">
            <div className="w-6 h-6 rounded-full bg-green-600 flex items-center justify-center text-white text-xs font-medium shadow-md border border-white/20">3</div>
            <span className="font-medium">Above target</span>
          </div>
          <div className="flex items-center gap-3 p-2 rounded-lg bg-muted/20">
            <div className="w-6 h-6 rounded-full bg-gray-500 flex items-center justify-center text-white text-xs font-medium shadow-md border border-white/20">-</div>
            <span>Not assessed</span>
          </div>
        </div>


      </Card>

      <Card className="p-6 mb-6 shadow-md border-primary/20 bg-card">
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-end justify-between">
            <div className="w-full sm:w-1/3">
              <Label htmlFor="assessmentName" className="text-sm font-medium mb-1.5 block">Assessment Name</Label>
              <Input
                id="assessmentName"
                value={assessmentName}
                onChange={(e) => {
                  setAssessmentName(e.target.value)
                  // Clear any error message when user types
                  if (message && message.type === "error" && message.text.includes("assessment name")) {
                    setMessage(null)
                  }
                }}
                placeholder="Name your assessment"
                className={`${!assessmentName.trim() ? 'border-red-400 ring-red-400/20' : 'focus:border-primary/50'}`}
                required
              />
              {!assessmentName.trim() && (
                <p className="text-sm text-red-500 mt-1.5">Assessment name is required</p>
              )}
            </div>
            <div className="flex flex-wrap gap-2">
              <Dialog>
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    disabled={userAssessments.length === 0}
                    className="bg-card hover:bg-blue-500 border-primary/40 hover:border-blue-600 text-primary shadow-md font-medium transition-colors duration-200 hover:text-white"
                  >
                    <span className="flex items-center gap-2">
                      Load Assessment
                    </span>
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle className="text-xl text-primary">Load Assessment</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-2 max-h-[300px] overflow-y-auto mt-2">
                    {isLoading ? (
                      <div className="flex justify-center py-8">
                        <div className="h-6 w-6 rounded-full border-2 border-primary/30 border-t-primary animate-spin"></div>
                      </div>
                    ) : userAssessments.length > 0 ? (
                      userAssessments.map((assessment, index) => (
                        <div
                          key={index}
                          className="p-4 border rounded-lg hover:bg-primary/5 cursor-pointer flex justify-between items-center transition-colors card-hover"
                          onClick={() => {
                            handleLoadAssessment(assessment.name)
                            document
                              .querySelector('[role="dialog"]')
                              ?.closest('div[data-state="open"]')
                              ?.dispatchEvent(new KeyboardEvent("keydown", { key: "Escape" }))
                          }}
                        >
                          <span className="font-medium">{assessment.name}</span>
                          <span className="text-muted-foreground text-sm bg-muted px-2 py-1 rounded">
                            {new Date(assessment.date).toLocaleDateString()}
                          </span>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8 text-muted-foreground bg-muted/30 rounded-lg border border-dashed">
                        No saved assessments found
                      </div>
                    )}
                  </div>
                </DialogContent>
              </Dialog>
              <Button
                variant="outline"
                onClick={() => setShowHistory(true)}
                disabled={!assessmentName}
                className="bg-card hover:bg-blue-500 border-primary/40 hover:border-blue-600 shadow-md font-medium transition-colors duration-200 hover:text-white"
              >
                <History className="mr-2 h-4 w-4 text-primary" />
                View History
              </Button>

            </div>
          </div>

          {message && (
            <div
              className={`p-4 rounded-lg border ${message.type === "success" ? "bg-green-50 border-green-200 text-green-800" : "bg-red-50 border-red-200 text-red-800"}`}
            >
              {message.text}
            </div>
          )}
        </div>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="core">Core Competencies</TabsTrigger>
          <TabsTrigger value="project">Project Skills</TabsTrigger>
        </TabsList>

        <TabsContent value="core" className="mt-0">
          <Card className="overflow-hidden shadow-md border-primary/20 bg-card">
            <div className="p-4 bg-muted/30 border-b">
              <h2 className="text-lg font-medium text-primary">Core Competency Assessment</h2>
              <p className="text-sm text-muted-foreground mt-1">
                Rate your current level against the target levels defined for your career stage based on the foundational expectations for Web Engineers.
              </p>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-muted/50 border-b">
                    <th className="px-4 py-3 text-left font-medium text-primary/90">Skill Category</th>
                    <th className="px-4 py-3 text-left font-medium text-primary/90">Skill Description/Examples</th>
                    <th className="px-4 py-3 text-center font-medium text-primary/90">
                      <div className="flex flex-col items-center">
                        <span className="text-xs uppercase tracking-wider text-muted-foreground mb-1">Target</span>
                        <span>CL2</span>
                      </div>
                    </th>
                    <th className="px-4 py-3 text-center font-medium text-primary/90">
                      <div className="flex flex-col items-center">
                        <span className="text-xs uppercase tracking-wider text-muted-foreground mb-1">Target</span>
                        <span>CL3</span>
                      </div>
                    </th>
                    <th className="px-4 py-3 text-center font-medium text-primary/90">
                      <div className="flex flex-col items-center">
                        <span className="text-xs uppercase tracking-wider text-muted-foreground mb-1">Target</span>
                        <span>CL4</span>
                      </div>
                    </th>
                    <th className="px-4 py-3 text-center font-medium text-primary/90">
                      <div className="flex flex-col items-center">
                        <span className="text-xs uppercase tracking-wider text-muted-foreground mb-1">Target</span>
                        <span>CL5</span>
                      </div>
                    </th>
                    <th className="px-4 py-3 text-center font-medium text-primary/90">
                      <div className="flex flex-col items-center">
                        <span className="text-xs uppercase tracking-wider text-muted-foreground mb-1">Target</span>
                        <span>CL6</span>
                      </div>
                    </th>
                    <th className="px-4 py-3 text-center font-medium text-primary/90">
                      <div className="flex flex-col items-center">
                        <span className="text-xs uppercase tracking-wider text-muted-foreground mb-1">Current</span>
                        <span>Level (1-6)</span>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {currentSkills.map((skill) => (
                    <tr key={skill.id} className="border-b hover:bg-muted/10 transition-colors">
                      <td className="px-4 py-4 font-medium">
                        <div className="flex items-center gap-2">
                          <span>{skill.category}</span>
                          {useICSkills && 'icLevelDescriptions' in skill && (
                            <ICLevelGuidance
                              skill={skill as ICSkill}
                              userCareerLevel={careerLevel}
                              ratingScale={currentRatingScale}
                            />
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm text-muted-foreground">{skill.description}</td>
                      <td className="px-4 py-4 text-center">
                        <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-background shadow-md border border-white/20 ${getLevelColor(skill.currentLevel, skill.targetCL2)}`}>
                          {skill.targetCL2}
                        </div>
                      </td>
                      <td className="px-4 py-4 text-center">
                        <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-background shadow-md border border-white/20 ${getLevelColor(skill.currentLevel, skill.targetCL3)}`}>
                          {skill.targetCL3}
                        </div>
                      </td>
                      <td className="px-4 py-4 text-center">
                        <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-background shadow-md border border-white/20 ${getLevelColor(skill.currentLevel, skill.targetCL4)}`}>
                          {skill.targetCL4}
                        </div>
                      </td>
                      <td className="px-4 py-4 text-center">
                        <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-background shadow-md border border-white/20 ${getLevelColor(skill.currentLevel, (skill as any).targetCL5 || (skill as any).targetTM12)}`}>
                          {(skill as any).targetCL5 || (skill as any).targetTM12}
                        </div>
                      </td>
                      <td className="px-4 py-4 text-center">
                        <div className={`inline-flex items-center justify-center w-8 h-8 rounded-full font-medium text-background shadow-md border border-white/20 ${getLevelColor(skill.currentLevel, (skill as any).targetCL6 || (skill as any).targetTM34)}`}>
                          {(skill as any).targetCL6 || (skill as any).targetTM34}
                        </div>
                      </td>
                      <td className="px-4 py-4 text-center">
                        <div className="flex flex-col items-center">
                          <Select
                            value={skill.currentLevel?.toString() || ""}
                            onValueChange={(value) => handleSkillLevelChange(skill.id, value)}
                          >
                            <SelectTrigger className="w-32 mx-auto bg-card border-primary/20 focus:ring-primary/20">
                              <SelectValue placeholder="Select Level" />
                            </SelectTrigger>
                            <SelectContent className="w-[350px]">
                              <SelectItem value="-">
                                <span className="flex items-center gap-2">
                                  <span className="font-semibold text-gray-500">-</span>
                                  <span>Not Assessed</span>
                                </span>
                              </SelectItem>
                              {[1, 2, 3, 4, 5, 6].map((level) => {
                                const levelColors = {
                                  1: 'bg-red-700',
                                  2: 'bg-orange-600',
                                  3: 'bg-amber-500',
                                  4: 'bg-green-600',
                                  5: 'bg-blue-600',
                                  6: 'bg-purple-700'
                                };
                                const bgColor = levelColors[level as keyof typeof levelColors];
                                const descriptions = {
                                  1: 'Novice',
                                  2: 'Advanced Beginner',
                                  3: 'Competent',
                                  4: 'Proficient',
                                  5: 'Expert',
                                  6: 'Master'
                                };

                                return (
                                  <SelectItem key={level} value={level.toString()}>
                                    <span className="flex items-center gap-2">
                                      <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full font-medium text-white shadow-md border border-white/20 ${bgColor}`}>{level}</span>
                                      <span className="font-medium">{descriptions[level as keyof typeof descriptions]}</span>
                                      <span className="text-xs text-muted-foreground">- {currentRatingScale[level as keyof typeof currentRatingScale]}</span>
                                    </span>
                                  </SelectItem>
                                );
                              })}
                            </SelectContent>
                          </Select>
                          {skill.currentLevel && (
                            <span className="text-xs mt-1 font-medium text-muted-foreground">
                              {skill.currentLevel === 1 && "Novice"}
                              {skill.currentLevel === 2 && "Advanced Beginner"}
                              {skill.currentLevel === 3 && "Competent"}
                              {skill.currentLevel === 4 && "Proficient"}
                              {skill.currentLevel === 5 && "Expert"}
                              {skill.currentLevel === 6 && "Master"}
                            </span>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="project" className="mt-0">
          {/* Project Selector - Moved inside Project Skills tab */}
          {assessmentType === 'self' && (
            <div className="mb-4">
              <ProjectSelector
                userId={userId}
                userEmail={userEmail}
                onProjectSelect={handleProjectSelect}
                selectedProjectId={projectId}
              />
            </div>
          )}

          {userProjectSkills.length > 0 ? (
            <MultiProjectSkillAssessment
              projectSkillsGroups={userProjectSkills}
              onSkillsChange={(updatedProjectId, skills) => {
                // If this is the currently selected project, update the projectSkillsData
                if (updatedProjectId === projectId) {
                  setProjectSkillsData(skills);
                }

                // Update the skills in the userProjectSkills array
                setUserProjectSkills(userProjectSkills.map(group =>
                  group.projectId === updatedProjectId
                    ? { ...group, skills }
                    : group
                ));
              }}
              careerLevel={careerLevel}
              activeProjectId={projectId}
            />
          ) : (
            <ProjectSkillAssessment
              projectSkills={projectSkillsData}
              onSkillsChange={setProjectSkillsData}
              careerLevel={careerLevel}
              projectName={projectName || projectSkillTemplate?.projectName || skillTemplate?.projectName}
            />
          )}
        </TabsContent>
      </Tabs>

      <div className="flex flex-wrap gap-4 justify-between items-center">
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={handleSaveAssessment}
            disabled={isSaving || (intervalMonths > 0 && !canTakeNewAssessment)}
            title={intervalMonths > 0 && !canTakeNewAssessment && nextAssessmentDate ?
              `You can take your next assessment after ${new Date(nextAssessmentDate).toLocaleDateString()}` :
              "Save your assessment"}
            className="bg-primary hover:bg-blue-500 shadow-md border border-primary/20 font-medium transition-colors duration-200"
          >
            <Save className="mr-2 h-4 w-4" />
            {isSaving ? (
              <span className="flex items-center gap-2">
                <div className="h-4 w-4 rounded-full border-2 border-white/30 border-t-white animate-spin"></div>
                Saving...
              </span>
            ) : (
              "Save Assessment"
            )}
          </Button>
          <Button
            variant="outline"
            onClick={exportAssessment}
            className="bg-card hover:bg-blue-500 border-primary/40 hover:border-blue-600 shadow-md font-medium transition-colors duration-200 hover:text-white"
          >
            <Download className="mr-2 h-4 w-4 text-primary" />
            Export CSV
          </Button>
        </div>
        <Button
          onClick={() => setShowResults(!showResults)}
          variant="secondary"
          className="bg-secondary hover:bg-blue-500 shadow-md border border-secondary/20 font-medium transition-colors duration-200"
        >
          {showResults ? "Hide Results" : "View Results Summary"}
        </Button>
      </div>

      {showResults && <ResultsSummary assessmentData={currentSkills as any} />}


    </div>
  )
}
