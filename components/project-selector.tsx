"use client"

import { useState, useEffect } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { getUserProfile } from "@/lib/actions/user-profile"
import { Skeleton } from "@/components/ui/skeleton"

interface ProjectSelectorProps {
  userId: string
  userEmail: string
  onProjectSelect: (projectId: string, projectName: string) => void
  selectedProjectId?: string
}

export default function ProjectSelector({
  userId,
  userEmail,
  onProjectSelect,
  selectedProjectId
}: ProjectSelectorProps) {
  const [loading, setLoading] = useState(true)
  const [projects, setProjects] = useState<Array<{ projectId: string; projectName: string }>>([])

  useEffect(() => {
    async function loadUserProjects() {
      setLoading(true)
      try {
        const result = await getUserProfile(userEmail)
        if (result.success && result.data) {
          const userProjects = result.data.projects || []
          setProjects(userProjects)

          // If there's a selected project, use it
          if (selectedProjectId && userProjects.find((p: { projectId: string }) => p.projectId === selectedProjectId)) {
            // Do nothing, keep the selected project
          }
          // Otherwise, if there are projects, select the first one
          else if (userProjects.length > 0) {
            onProjectSelect(userProjects[0].projectId, userProjects[0].projectName)
          }
        }
      } catch (error) {
        console.error("Error loading user projects:", error)
      } finally {
        setLoading(false)
      }
    }

    loadUserProjects()
  }, [userEmail, onProjectSelect, selectedProjectId])

  if (loading) {
    return (
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="space-y-2">
            <Skeleton className="h-4 w-[250px]" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (projects.length === 0) {
    return (
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="text-muted-foreground text-sm">
            You are not assigned to any projects. Please contact your manager or an administrator.
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="mb-6">
      <CardContent className="pt-6">
        <div className="space-y-2">
          <Label htmlFor="project-select">Select Project</Label>
          <Select
            value={selectedProjectId || ""}
            onValueChange={(value) => {
              const project = projects.find((p: { projectId: string; projectName: string }) => p.projectId === value)
              if (project) {
                onProjectSelect(project.projectId, project.projectName)
              }
            }}
          >
            <SelectTrigger id="project-select">
              <SelectValue placeholder="Select a project" />
            </SelectTrigger>
            <SelectContent>
              {projects.map((project) => (
                <SelectItem key={project.projectId} value={project.projectId}>
                  {project.projectName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  )
}
