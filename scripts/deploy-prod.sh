#!/bin/bash

# Production deployment script for the Skills Assessment application

echo "Starting production deployment..."

# Build the production image
echo "Building production image..."
docker compose --profile prod build app-prod

# Start only the app-prod service (using MongoDB Atlas)
echo "Starting production service..."
docker compose --profile prod up app-prod -d

echo "Production deployment completed!"
echo "Your application should now be running at https://skills.stratpoint.io"
echo "Note: Make sure your domain is properly configured to point to this server."
