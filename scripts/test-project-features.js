/**
 * Test script for project-related features
 *
 * This script tests the basic functionality of the project-related features:
 * 1. Creating a project
 * 2. Creating a project skill template
 * 3. Assigning users to a project
 * 4. Creating a project-specific assessment
 *
 * Run this script with:
 * node scripts/test-project-features.js
 */

const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/skills-assessment';
const client = new MongoClient(uri);

async function main() {
  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db();

    // 1. Create a test project
    const projectResult = await db.collection('projects').insertOne({
      name: 'Test Project',
      description: 'A test project for testing project features',
      businessUnit: 'web',
      status: 'active',
      createdBy: 'test-script',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    const projectId = projectResult.insertedId.toString();
    console.log(`Created test project with ID: ${projectId}`);

    // 2. Create a project skill template
    const templateResult = await db.collection('project-skill-templates').insertOne({
      projectId,
      projectName: 'Test Project',
      businessUnit: 'web',
      skills: [
        {
          id: 'react',
          category: 'React.js',
          description: 'Core Concepts, Hooks, State Mgmt',
          projectName: 'Test Project',
          targetCL2: 2,
          targetCL3: 3,
          targetCL4: 4,
          targetCL5: 4,
          targetCL6: 5,
          currentLevel: null,
        },
        {
          id: 'nextjs',
          category: 'Next.js',
          description: 'App Router, SSR/SSG/ISR, API Routes',
          projectName: 'Test Project',
          targetCL2: 2,
          targetCL3: 3,
          targetCL4: 4,
          targetCL5: 4,
          targetCL6: 4,
          currentLevel: null,
        }
      ],
      updatedAt: new Date(),
      updatedBy: 'test-script'
    });

    console.log(`Created project skill template with ID: ${templateResult.insertedId}`);

    // 3. Find a test user
    const testUser = await db.collection('user_profiles').findOne({});

    if (!testUser) {
      console.log('No users found in the database. Please create a user first.');
      return;
    }

    // 4. Assign the user to the project
    await db.collection('user_profiles').updateOne(
      { _id: testUser._id },
      {
        $addToSet: {
          projects: {
            projectId,
            projectName: 'Test Project'
          }
        },
        $set: { updatedAt: new Date() }
      }
    );

    console.log(`Assigned user ${testUser.name} (${testUser.email}) to the test project`);

    // 5. Create a test assessment for the project
    const assessmentResult = await db.collection('assessments').insertOne({
      userId: testUser._id.toString(),
      reviewerId: testUser._id.toString(),
      name: 'Test Project Assessment',
      skills: [],
      projectSkills: [
        {
          id: 'react',
          category: 'React.js',
          description: 'Core Concepts, Hooks, State Mgmt',
          projectName: 'Test Project',
          targetCL2: 2,
          targetCL3: 3,
          targetCL4: 4,
          targetCL5: 4,
          targetCL6: 5,
          currentLevel: 3,
        },
        {
          id: 'nextjs',
          category: 'Next.js',
          description: 'App Router, SSR/SSG/ISR, API Routes',
          projectName: 'Test Project',
          targetCL2: 2,
          targetCL3: 3,
          targetCL4: 4,
          targetCL5: 4,
          targetCL6: 4,
          currentLevel: 2,
        }
      ],
      userName: testUser.name,
      userEmail: testUser.email,
      businessUnit: testUser.businessUnit,
      careerLevel: testUser.careerLevel,
      jobRole: testUser.jobRole,
      assessmentType: 'self',
      projectId,
      projectName: 'Test Project',
      createdAt: new Date(),
      updatedAt: new Date(),
      history: [
        {
          date: new Date(),
          skills: [],
          projectSkills: [
            {
              id: 'react',
              category: 'React.js',
              description: 'Core Concepts, Hooks, State Mgmt',
              projectName: 'Test Project',
              targetCL2: 2,
              targetCL3: 3,
              targetCL4: 4,
              targetCL5: 4,
              targetCL6: 5,
              currentLevel: 3,
            },
            {
              id: 'nextjs',
              category: 'Next.js',
              description: 'App Router, SSR/SSG/ISR, API Routes',
              projectName: 'Test Project',
              targetCL2: 2,
              targetCL3: 3,
              targetCL4: 4,
              targetCL5: 4,
              targetCL6: 4,
              currentLevel: 2,
            }
          ],
          businessUnit: testUser.businessUnit,
          careerLevel: testUser.careerLevel,
          jobRole: testUser.jobRole,
          projectId,
          projectName: 'Test Project',
        }
      ]
    });

    console.log(`Created test assessment with ID: ${assessmentResult.insertedId}`);

    console.log('\nTest completed successfully!');
    console.log('You can now test the project features in the UI.');

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

main().catch(console.error);
