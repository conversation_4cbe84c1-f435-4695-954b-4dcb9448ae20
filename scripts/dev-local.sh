#!/bin/bash

# Clear cache first for a fresh start
echo "Clearing Next.js cache..."
rm -rf .next .turbo node_modules/.cache

# Check if Docker is running
if docker info > /dev/null 2>&1; then
  # Start MongoDB in Docker but run Next.js locally
  echo "Starting MongoDB in Docker..."
  docker compose up -d mongodb

  # Wait for MongoDB to be ready
  echo "Waiting for MongoDB to be ready..."
  sleep 3

  # Use local MongoDB
  export MONGODB_URI="mongodb://localhost:27017/skills-assessment"
else
  # Docker is not running, use MongoDB Atlas instead
  echo "Docker is not running. Using MongoDB Atlas instead..."
  export MONGODB_URI="mongodb+srv://rcdelacruz:<EMAIL>/skills-assessment-dev?retryWrites=true&w=majority&appName=Cluster0"
fi

# Set environment variables
export MONGODB_URI="mongodb://localhost:27017/skills-assessment"
export NEXTAUTH_URL="http://localhost:3000"
export NODE_ENV="development"

# Turbopack optimizations
export TURBOPACK=1
export NEXT_TURBO_FAST_REFRESH=true

# Memory optimization for M3 Mac - reduced to 4GB for better performance
export NODE_OPTIONS="--max-old-space-size=4096"

# Disable telemetry for better performance
export NEXT_TELEMETRY_DISABLED=1

# Disable React DevTools for better performance
export REACT_DEVTOOLS_DISABLE=true

# Disable source maps for faster compilation
export GENERATE_SOURCEMAP=false

# Disable TypeScript type checking during development
export DISABLE_TYPE_CHECK=true

# Disable React strict mode for faster development
export NEXT_DEVELOPMENT_MODE=true

# Disable React profiling for faster development
export REACT_EDITOR=none

# Run Next.js with optimized Turbopack settings
echo "Starting Next.js with optimized Turbopack settings..."
npm run dev:turbo:fast
