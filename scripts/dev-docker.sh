#!/bin/bash

# Development script for the Skills Assessment application

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
  echo "Creating .env file from .env.example..."
  cp .env.example .env
  echo "Please update the .env file with your configuration."
fi

# Start the development environment
echo "Starting development environment..."
docker-compose up app-dev mongodb mongo-express

# To start the production environment, use:
# docker-compose --profile prod up app-prod mongodb mongo-express
