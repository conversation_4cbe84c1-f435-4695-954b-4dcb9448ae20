#!/bin/bash

echo "Optimizing Docker for better performance..."

# Stop all containers
docker-compose down

# Prune Docker system to free up resources
echo "Cleaning Docker system..."
docker system prune -f

# Clear Next.js cache
echo "Clearing Next.js cache..."
rm -rf .next

# Optimize Docker daemon (if possible)
if [ -f /etc/docker/daemon.json ]; then
  echo "Optimizing Docker daemon settings..."
  sudo tee /etc/docker/daemon.json > /dev/null << EOF
{
  "max-concurrent-downloads": 10,
  "max-concurrent-uploads": 10,
  "storage-driver": "overlay2",
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF
  echo "Restarting Docker daemon..."
  sudo systemctl restart docker
else
  echo "Cannot optimize Docker daemon (daemon.json not found or not accessible)"
fi

# Start containers with optimized settings
echo "Starting Docker Compose with optimized settings..."
docker-compose up app-dev
