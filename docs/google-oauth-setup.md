# Setting Up Google OAuth for Multiple Ports

When using Google OAuth for authentication, you need to configure the authorized redirect URIs in the Google Cloud Console. This guide explains how to set up Google OAuth to work with multiple ports, which is useful for development and testing.

## Google Cloud Console Setup

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project or create a new one
3. Navigate to "APIs & Services" > "Credentials"
4. Select your OAuth 2.0 Client ID or create a new one
5. Under "Authorized redirect URIs", add the following URIs:
   - `http://localhost:3000/api/auth/callback/google`
   - `http://localhost:3001/api/auth/callback/google`
   - `http://localhost:3002/api/auth/callback/google`
   - `http://localhost:3003/api/auth/callback/google`
   - Add any other ports you might use

## Why Multiple URIs?

Google OAuth requires exact matching of redirect URIs for security reasons. By adding multiple URIs with different ports, you can run your application on different ports without having to update your Google OAuth configuration each time.

## Environment Variables

In your `.env.local` file, you only need to set the following variables:

```
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

The application will automatically determine the correct callback URL based on the port it's running on.

## Testing Your Configuration

To test that your configuration works with different ports:

1. Run the application on port 3000: `npm run dev`
2. Run the application on port 3002: `docker-compose up app-dev mongodb mongo-express`
3. Run the application on port 3001: `docker-compose --profile prod up app-prod mongodb mongo-express`

You should be able to sign in with Google on all of these ports without changing your Google OAuth configuration.
