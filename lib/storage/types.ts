/**
 * Storage interface types
 * These define the common interface for all storage implementations
 */

// Generic result type for all storage operations
export interface StorageResult<T> {
  success: boolean
  data?: T
  message?: string
}

// Base storage interface that all storage implementations must implement
export interface StorageInterface {
  // Connection management
  connect(): Promise<boolean>
  disconnect(): Promise<void>
  isConnected(): Promise<boolean>

  // Basic CRUD operations
  find(collection: string, query?: Record<string, any>): Promise<any[]>
  findOne(collection: string, query?: Record<string, any>): Promise<any | null>
  insertOne(collection: string, document: Record<string, any>): Promise<any>
  updateOne(collection: string, query: Record<string, any>, update: Record<string, any>): Promise<boolean>
  deleteOne(collection: string, query: Record<string, any>): Promise<boolean>

  // These methods are implemented in the actions layer, not in the storage implementations
  getUserProfile?(email: string): Promise<StorageResult<any>>
  saveUserProfile?(profile: any): Promise<StorageResult<any>>
  saveAssessment?(data: any): Promise<StorageResult<any>>
  getUserAssessments?(userId: string): Promise<StorageResult<any[]>>
  loadAssessment?(userId: string, name: string): Promise<StorageResult<any>>
  getSkillHistory?(userId: string, name: string, skillId: string): Promise<StorageResult<any[]>>
  getAllSkillsHistory?(userId: string, name: string): Promise<StorageResult<any[]>>
}
