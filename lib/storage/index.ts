/**
 * Database abstraction layer
 * This provides a consistent interface for data storage, whether using a real database or in-memory storage
 */

import { DB_CONFIG } from '../env'
import { InMemoryStorage } from './in-memory'
import { MongoDBStorage } from './mongodb'
import type { StorageInterface } from './types'

// Export the storage interface for use in other modules
export type { StorageInterface }

// Create and export the storage instance based on configuration
let storageInstance: StorageInterface

// Initialize the storage instance
if (DB_CONFIG.USE_DATABASE) {
  // Use MongoDB storage in production or when explicitly configured
  storageInstance = new MongoDBStorage(DB_CONFIG.MONGODB_URI)
} else {
  // Use in-memory storage for development without a database
  storageInstance = new InMemoryStorage()
}

// Export the storage instance
export const storage = storageInstance
