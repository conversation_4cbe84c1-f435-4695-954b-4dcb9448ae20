/**
 * In-memory storage implementation
 * This provides a simple in-memory storage for development and testing
 */

import type { StorageInterface } from './types'

export class InMemoryStorage implements StorageInterface {
  private data: Map<string, any[]> = new Map()

  constructor() {
    // Initialize with empty collections
    this.data.set('users', [])
    this.data.set('assessments', [])
  }

  async connect(): Promise<boolean> {
    // No connection needed for in-memory storage
    return true
  }

  async disconnect(): Promise<void> {
    // No disconnection needed for in-memory storage
    return
  }

  async isConnected(): Promise<boolean> {
    // In-memory storage is always connected
    return true
  }

  async find(collection: string, query: Record<string, any> = {}): Promise<any[]> {
    const items = this.data.get(collection) || []
    
    // Simple filtering based on query
    return items.filter(item => {
      for (const [key, value] of Object.entries(query)) {
        if (item[key] !== value) {
          return false
        }
      }
      return true
    })
  }

  async findOne(collection: string, query: Record<string, any> = {}): Promise<any | null> {
    const items = await this.find(collection, query)
    return items.length > 0 ? items[0] : null
  }

  async insertOne(collection: string, document: Record<string, any>): Promise<any> {
    const items = this.data.get(collection) || []
    
    // Add id if not present
    if (!document.id) {
      document.id = Math.random().toString(36).substring(2, 15)
    }
    
    // Add timestamps
    document.createdAt = document.createdAt || new Date().toISOString()
    document.updatedAt = new Date().toISOString()
    
    items.push(document)
    this.data.set(collection, items)
    
    return { ...document }
  }

  async updateOne(
    collection: string, 
    query: Record<string, any>, 
    update: Record<string, any>
  ): Promise<boolean> {
    const items = this.data.get(collection) || []
    let updated = false
    
    const updatedItems = items.map(item => {
      // Check if this item matches the query
      let matches = true
      for (const [key, value] of Object.entries(query)) {
        if (item[key] !== value) {
          matches = false
          break
        }
      }
      
      if (matches) {
        updated = true
        // Update the item
        const updatedItem = { ...item }
        
        // Handle $set operator
        if (update.$set) {
          Object.assign(updatedItem, update.$set)
        }
        
        // Handle $push operator (simplified)
        if (update.$push) {
          for (const [key, value] of Object.entries(update.$push)) {
            if (!updatedItem[key]) {
              updatedItem[key] = []
            }
            if (Array.isArray(updatedItem[key])) {
              updatedItem[key].push(value)
            }
          }
        }
        
        // Update timestamp
        updatedItem.updatedAt = new Date().toISOString()
        
        return updatedItem
      }
      
      return item
    })
    
    this.data.set(collection, updatedItems)
    return updated
  }

  async deleteOne(collection: string, query: Record<string, any>): Promise<boolean> {
    const items = this.data.get(collection) || []
    const initialLength = items.length
    
    const filteredItems = items.filter(item => {
      // Keep items that don't match the query
      for (const [key, value] of Object.entries(query)) {
        if (item[key] !== value) {
          return true
        }
      }
      return false
    })
    
    this.data.set(collection, filteredItems)
    return filteredItems.length < initialLength
  }
}
