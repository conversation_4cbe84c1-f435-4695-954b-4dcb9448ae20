/**
 * MongoDB storage implementation
 * This provides a MongoDB-based storage for production use
 */

import { MongoClient, Db, ObjectId } from 'mongodb'
import type { StorageInterface } from './types'

export class MongoDBStorage implements StorageInterface {
  private client: MongoClient
  private db: Db | null = null
  private uri: string
  private dbName: string

  constructor(uri: string) {
    this.uri = uri
    this.client = new MongoClient(uri)

    // Extract database name from URI or use default
    try {
      // Parse the URI to extract the database name
      const uriParts = uri.split('/');
      // The database name is the last part of the URI, before any query parameters
      const dbNameWithParams = uriParts[uriParts.length - 1];
      // Remove any query parameters
      this.dbName = dbNameWithParams.split('?')[0];

      // If no database name was found, use default
      if (!this.dbName) {
        this.dbName = 'skills-assessment';
      }
    } catch (error) {
      console.warn('Failed to parse database name from URI, using default', error);
      this.dbName = 'skills-assessment';
    }

    console.log(`MongoDB connecting to database: ${this.dbName}`);
  }

  async connect(): Promise<boolean> {
    try {
      await this.client.connect()
      this.db = this.client.db(this.dbName)
      return true
    } catch (error) {
      console.error('MongoDB connection error:', error)
      return false
    }
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.close()
    }
  }

  async isConnected(): Promise<boolean> {
    try {
      // Check if the client is connected
      await this.client.db().command({ ping: 1 })
      return true
    } catch (error) {
      return false
    }
  }

  async find(collection: string, query: Record<string, any> = {}): Promise<any[]> {
    if (!this.db) {
      await this.connect()
    }

    if (!this.db) {
      throw new Error('Database not connected')
    }

    // Convert string IDs to ObjectId if needed
    const processedQuery = this.processQuery(query)

    try {
      const result = await this.db.collection(collection).find(processedQuery).toArray()
      return result.map(this.processDocument)
    } catch (error) {
      console.error(`Error finding documents in ${collection}:`, error)
      return []
    }
  }

  async findOne(collection: string, query: Record<string, any> = {}): Promise<any | null> {
    if (!this.db) {
      await this.connect()
    }

    if (!this.db) {
      throw new Error('Database not connected')
    }

    // Convert string IDs to ObjectId if needed
    const processedQuery = this.processQuery(query)

    try {
      const result = await this.db.collection(collection).findOne(processedQuery)
      return result ? this.processDocument(result) : null
    } catch (error) {
      console.error(`Error finding document in ${collection}:`, error)
      return null
    }
  }

  async insertOne(collection: string, document: Record<string, any>): Promise<any> {
    if (!this.db) {
      await this.connect()
    }

    if (!this.db) {
      throw new Error('Database not connected')
    }

    // Add timestamps
    const docWithTimestamps = {
      ...document,
      createdAt: document.createdAt || new Date(),
      updatedAt: new Date()
    }

    try {
      const result = await this.db.collection(collection).insertOne(docWithTimestamps)
      return this.processDocument({
        ...docWithTimestamps,
        _id: result.insertedId
      })
    } catch (error) {
      console.error(`Error inserting document into ${collection}:`, error)
      throw error
    }
  }

  async updateOne(
    collection: string,
    query: Record<string, any>,
    update: Record<string, any>
  ): Promise<boolean> {
    if (!this.db) {
      await this.connect()
    }

    if (!this.db) {
      throw new Error('Database not connected')
    }

    // Convert string IDs to ObjectId if needed
    const processedQuery = this.processQuery(query)

    // Add updated timestamp if using $set
    const processedUpdate = { ...update }
    if (processedUpdate.$set) {
      processedUpdate.$set = {
        ...processedUpdate.$set,
        updatedAt: new Date()
      }
    } else {
      processedUpdate.$set = { updatedAt: new Date() }
    }

    try {
      const result = await this.db.collection(collection).updateOne(processedQuery, processedUpdate)
      return result.modifiedCount > 0
    } catch (error) {
      console.error(`Error updating document in ${collection}:`, error)
      return false
    }
  }

  async deleteOne(collection: string, query: Record<string, any>): Promise<boolean> {
    if (!this.db) {
      await this.connect()
    }

    if (!this.db) {
      throw new Error('Database not connected')
    }

    // Convert string IDs to ObjectId if needed
    const processedQuery = this.processQuery(query)

    try {
      const result = await this.db.collection(collection).deleteOne(processedQuery)
      return result.deletedCount > 0
    } catch (error) {
      console.error(`Error deleting document from ${collection}:`, error)
      return false
    }
  }

  // Helper method to convert _id to string id and handle ObjectId
  private processDocument(doc: any): any {
    if (!doc) return null

    const result = { ...doc }

    // Convert _id to id
    if (result._id) {
      result.id = result._id.toString()
      delete result._id
    }

    return result
  }

  // Helper method to convert string ids to ObjectId
  private processQuery(query: Record<string, any>): Record<string, any> {
    const result = { ...query }

    // Convert id to _id with ObjectId
    if (result.id) {
      try {
        result._id = new ObjectId(result.id)
        delete result.id
      } catch (error) {
        // If not a valid ObjectId, keep the original id
        result._id = result.id
        delete result.id
      }
    }

    return result
  }
}
