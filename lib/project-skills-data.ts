export interface ProjectSkill {
  id: string
  category: string
  description: string
  projectName?: string
  targetCL2?: number
  targetCL3?: number
  targetCL4?: number
  targetCL5?: number
  targetCL6?: number
  currentLevel: number | null
  isCustom?: boolean
}

// These are example project skills for a Next.js/React project
// They should be customizable by managers for specific projects
export const projectSkillsData: ProjectSkill[] = [
  {
    id: "react",
    category: "React.js",
    description: "Core Concepts, Hooks, State Mgmt (Context/Zustand), Perf.",
    projectName: "Skills Assessment App",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 5,
    currentLevel: null,
  },
  {
    id: "nextjs",
    category: "Next.js",
    description: "App/Pages Router, SSR/SSG/ISR, API Routes, Data Fetching",
    projectName: "Skills Assessment App",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
  },
  {
    id: "typescript",
    category: "TypeScript",
    description: "Types, Interfaces, Generics",
    projectName: "Skills Assessment App",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
  },
  {
    id: "ui-library",
    category: "UI Library (Project)",
    description: "e.g., Tailwind CSS + Shadcn/ui",
    projectName: "Skills Assessment App",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
  },
  {
    id: "testing",
    category: "Testing (Project Stack)",
    description: "Jest, React Testing Library",
    projectName: "Skills Assessment App",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
  },
  {
    id: "api-interaction",
    category: "API Interaction (Project)",
    description: "Fetch/Axios, Handling Responses/Errors",
    projectName: "Skills Assessment App",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
  },
  {
    id: "build-deployment",
    category: "Build/Deployment (Project)",
    description: "Vercel Deployment, Env Variables",
    projectName: "Skills Assessment App",
    targetCL2: 1,
    targetCL3: 2,
    targetCL4: 3,
    targetCL5: 3,
    targetCL6: 4,
    currentLevel: null,
  },
]

/**
 * Create a custom project skill
 * @param id Skill ID
 * @param category Skill category
 * @param description Skill description
 * @param projectName Project name
 * @param targetLevels Target levels for different career levels
 * @returns A ProjectSkill object
 */
export function createCustomProjectSkill(
  id: string,
  category: string,
  description: string,
  projectName: string,
  targetLevels: {
    cl2?: number,
    cl3?: number,
    cl4?: number,
    cl5?: number,
    cl6?: number
  } = {}
): ProjectSkill {
  return {
    id,
    category,
    description,
    projectName,
    targetCL2: targetLevels.cl2 || 2,
    targetCL3: targetLevels.cl3 || 3,
    targetCL4: targetLevels.cl4 || 4,
    targetCL5: targetLevels.cl5 || 4,
    targetCL6: targetLevels.cl6 || 4,
    currentLevel: null,
    isCustom: true
  }
}
