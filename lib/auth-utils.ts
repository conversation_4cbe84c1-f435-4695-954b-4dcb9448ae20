/**
 * Authentication utilities
 * Helper functions for authentication
 */

/**
 * Get the base URL from the NEXTAUTH_URL environment variable
 * This is used to generate callback URLs for authentication
 *
 * @returns The base URL for the application
 */
export function getBaseUrl(): string {
  // Use NEXTAUTH_URL if available
  if (process.env.NEXTAUTH_URL) {
    return process.env.NEXTAUTH_URL;
  }

  // In development, use localhost with the port from the request
  if (process.env.NODE_ENV === 'development') {
    // Default to standard Next.js port
    return 'http://localhost:3000';
  }

  // In production, use the VERCEL_URL if available
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }

  // Fallback to localhost:3000
  return 'http://localhost:3000';
}

/**
 * Get the callback URL for authentication
 * This is used to redirect the user after authentication
 *
 * @param path The path to redirect to after authentication
 * @returns The full callback URL
 */
export function getCallbackUrl(path: string = '/'): string {
  const baseUrl = getBaseUrl();

  // If baseUrl is empty, use relative URL
  if (!baseUrl) {
    return path;
  }

  // Otherwise, use absolute URL
  return `${baseUrl}${path}`;
}
