/**
 * Enhanced in-memory cache for database queries
 * This reduces database load for frequently accessed data
 *
 * This cache is optimized for performance with:
 * - Tiered caching (memory + React cache)
 * - Configurable TTL per cache entry
 * - Automatic cache invalidation
 * - Support for cache prefixes to invalidate related entries
 */

import {
  findOne<PERSON>ached,
  find<PERSON>any<PERSON>ached,
  countDocumentsCached,
  getFromMemory<PERSON>ache,
  setInMemoryCache,
  clearMemoryCache,
  clearMemoryCacheKey,
  generateCacheKey
} from './query-cache';

type CacheEntry<T> = {
  data: T;
  expiry: number;
};

class QueryCache {
  private cache: Map<string, CacheEntry<any>> = new Map();
  private defaultTTL: number = 60 * 1000; // 1 minute default TTL
  private longTTL: number = 5 * 60 * 1000; // 5 minutes for less frequently changing data
  private shortTTL: number = 30 * 1000; // 30 seconds for frequently changing data

  /**
   * Get a value from the cache
   * @param key Cache key
   * @returns The cached value or undefined if not found or expired
   */
  get<T>(key: string): T | undefined {
    // First try the memory cache from query-cache.ts
    const memCached = getFromMemoryCache<T>(key);
    if (memCached !== null) {
      return memCached;
    }

    // Then try the local cache
    const entry = this.cache.get(key);

    if (!entry) {
      return undefined;
    }

    // Check if the entry has expired
    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      return undefined;
    }

    // Update the memory cache for faster access next time
    setInMemoryCache(key, entry.data);

    return entry.data as T;
  }

  /**
   * Set a value in the cache
   * @param key Cache key
   * @param value Value to cache
   * @param ttl Time to live in milliseconds (optional, defaults to 1 minute)
   */
  set<T>(key: string, value: T, ttl: number = this.defaultTTL): void {
    // Set in both caches
    this.cache.set(key, {
      data: value,
      expiry: Date.now() + ttl,
    });

    setInMemoryCache(key, value);
  }

  /**
   * Delete a value from the cache
   * @param key Cache key
   */
  delete(key: string): void {
    this.cache.delete(key);
    clearMemoryCacheKey(key);
  }

  /**
   * Clear all values from the cache
   */
  clear(): void {
    this.cache.clear();
    clearMemoryCache();
  }

  /**
   * Get or set a value in the cache using a factory function
   * @param key Cache key
   * @param factory Function to produce the value if not in cache
   * @param ttl Time to live in milliseconds (optional)
   * @returns The cached or newly produced value
   */
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    ttl?: number
  ): Promise<T> {
    const cached = this.get<T>(key);

    if (cached !== undefined) {
      return cached;
    }

    const value = await factory();
    this.set(key, value, ttl);
    return value;
  }

  /**
   * Get TTL for different types of data
   */
  getShortTTL(): number {
    return this.shortTTL;
  }

  getMediumTTL(): number {
    return this.defaultTTL;
  }

  getLongTTL(): number {
    return this.longTTL;
  }
}

// Export a singleton instance
export const queryCache = new QueryCache();

// Export the React cache functions
export {
  findOneCached,
  findManyCached,
  countDocumentsCached,
  generateCacheKey
};
