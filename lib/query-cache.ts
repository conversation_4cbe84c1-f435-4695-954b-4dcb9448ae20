import { cache } from "react";
import { db } from "./db";

/**
 * Cache for database queries to reduce redundant database calls
 * This uses React's cache function to deduplicate requests within the same render cycle
 */

// Cache for findOne operations
export const findOneCached = cache(
  async <T>(collection: string, query: any): Promise<T | null> => {
    return db.collection(collection).findOne(query) as Promise<T | null>;
  }
);

// Cache for find operations with pagination
export const findManyCached = cache(
  async <T>(
    collection: string,
    query: any,
    options?: {
      projection?: any;
      sort?: any;
      skip?: number;
      limit?: number;
    }
  ): Promise<T[]> => {
    // Use a simpler approach to avoid MongoDB cursor type issues
    // First get all documents matching the query
    const allDocs = await db.collection(collection).find(query).toArray();

    // Then manually apply sorting, pagination, etc. in memory
    let result = [...allDocs];

    // Apply sort if provided
    if (options?.sort) {
      const sortField = Object.keys(options.sort)[0];
      const sortDirection = options.sort[sortField];

      result.sort((a, b) => {
        if (a[sortField] < b[sortField]) return sortDirection === 1 ? -1 : 1;
        if (a[sortField] > b[sortField]) return sortDirection === 1 ? 1 : -1;
        return 0;
      });
    }

    // Apply skip and limit if provided
    if (options?.skip !== undefined || options?.limit !== undefined) {
      const skip = options?.skip || 0;
      const limit = options?.limit || result.length;

      result = result.slice(skip, skip + limit);
    }

    return result as unknown as Promise<T[]>;
  }
);

// Cache for count operations
export const countDocumentsCached = cache(
  async (collection: string, query: any): Promise<number> => {
    return db.collection(collection).countDocuments(query);
  }
);

// Generate a cache key for complex queries
export function generateCacheKey(
  collection: string,
  query: any,
  options?: any
): string {
  return `${collection}:${JSON.stringify(query)}:${options ? JSON.stringify(options) : ""
    }`;
}

// In-memory cache for non-React contexts (like API routes)
const memoryCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 60000; // 1 minute cache TTL

// Get data from memory cache
export function getFromMemoryCache<T>(key: string): T | null {
  const cached = memoryCache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data as T;
  }
  return null;
}

// Set data in memory cache
export function setInMemoryCache<T>(key: string, data: T): void {
  memoryCache.set(key, { data, timestamp: Date.now() });
}

// Clear memory cache
export function clearMemoryCache(): void {
  memoryCache.clear();
}

// Clear specific key from memory cache
export function clearMemoryCacheKey(key: string): void {
  memoryCache.delete(key);
}
