"use server"

import { revalidatePath } from "next/cache"
import { getDb } from "@/lib/db"
import { ObjectId } from "mongodb"
import type { Skill, skillsData } from "@/lib/skills-data"
import type { ProjectSkill, projectSkillsData } from "@/lib/project-skills-data"
import { assessmentConfig, authConfig } from "@/lib/config"
import { getBUSkillTemplate } from "./admin-skills"
import { getProjectSkillTemplate } from "./projects"

export interface DevelopmentPlan {
  keyAreas: string[]
  actions: string[]
  notes: string
}

export interface AssessmentData {
  userId: string
  name: string
  date: string
  skills: typeof skillsData
  projectSkills?: typeof projectSkillsData
  userName?: string
  userEmail?: string
  businessUnit?: string
  careerLevel?: string
  jobRole?: string
  assessmentType?: 'self' | 'manager' | 'peer'
  targetUserId?: string
  targetUserName?: string
  cycleId?: string
  projectId?: string
  projectName?: string
  developmentPlan?: DevelopmentPlan
}

export async function saveAssessment(data: AssessmentData) {
  try {
    const database = await getDb()

    // Build the query based on assessment type
    let query: any = { name: data.name };

    if (data.assessmentType === 'self' || !data.assessmentType) {
      // Self assessment - user is both the subject and the reviewer
      query.userId = data.userId;
      query.reviewerId = data.userId;
    } else if (data.assessmentType === 'manager') {
      // Manager assessment - user is the reviewer, target is the subject
      query.userId = data.targetUserId;
      query.reviewerId = data.userId;
    } else if (data.assessmentType === 'peer') {
      // Peer assessment - user is the reviewer, target is the subject
      query.userId = data.targetUserId;
      query.reviewerId = data.userId;
    }

    // Add cycle ID if provided
    if (data.cycleId) {
      query.cycleId = data.cycleId;
    }

    // Check if assessment with this query already exists
    const existingAssessment = await database.collection("assessments").findOne(query)

    if (existingAssessment) {
      // Check if updates are allowed based on configuration
      if (!assessmentConfig.allowUpdates) {
        return {
          success: false,
          message: "Assessments cannot be updated once saved. Please create a new assessment with a different name."
        }
      }

      // Update existing assessment if allowed
      await database.collection("assessments").updateOne(
        { userId: data.userId, name: data.name },
        {
          $set: {
            skills: data.skills,
            projectSkills: data.projectSkills || [],
            userName: data.userName,
            userEmail: data.userEmail,
            businessUnit: data.businessUnit,
            careerLevel: data.careerLevel,
            jobRole: data.jobRole,
            projectId: data.projectId,
            projectName: data.projectName,
            updatedAt: new Date(),
          },
          // Add to history array
          $push: {
            history: {
              date: new Date(),
              skills: data.skills,
              projectSkills: data.projectSkills || [],
              businessUnit: data.businessUnit,
              careerLevel: data.careerLevel,
              jobRole: data.jobRole,
              projectId: data.projectId,
              projectName: data.projectName,
            },
          },
        },
      )
      revalidatePath("/")
      return { success: true, message: "Assessment updated successfully" }
    } else {
      // Check if there's an interval restriction between assessments
      if (assessmentConfig.intervalMonths > 0) {
        // Calculate the date from which to check for recent assessments
        const restrictionDate = new Date();
        restrictionDate.setMonth(restrictionDate.getMonth() - assessmentConfig.intervalMonths);

        const recentAssessment = await database
          .collection("assessments")
          .findOne({
            userId: data.userId,
            createdAt: { $gte: restrictionDate }
          });

        if (recentAssessment) {
          // Calculate when the user can take the next assessment
          const nextAssessmentDate = new Date(recentAssessment.createdAt);
          nextAssessmentDate.setMonth(nextAssessmentDate.getMonth() + assessmentConfig.intervalMonths);

          const formattedDate = nextAssessmentDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });

          return {
            success: false,
            message: `You can only take an assessment once every ${assessmentConfig.intervalMonths} months. Your next assessment can be taken after ${formattedDate}.`
          }
        }
      }

      // Create new assessment
      await database.collection("assessments").insertOne({
        userId: data.assessmentType === 'self' || !data.assessmentType ? data.userId : data.targetUserId,
        reviewerId: data.userId,
        name: data.name,
        skills: data.skills,
        projectSkills: data.projectSkills || [],
        userName: data.userName,
        userEmail: data.userEmail,
        businessUnit: data.businessUnit,
        careerLevel: data.careerLevel,
        jobRole: data.jobRole,
        assessmentType: data.assessmentType || 'self',
        cycleId: data.cycleId,
        projectId: data.projectId,
        projectName: data.projectName,
        targetUserName: data.targetUserName,
        createdAt: new Date(),
        updatedAt: new Date(),
        history: [
          {
            date: new Date(),
            skills: data.skills,
            projectSkills: data.projectSkills || [],
            businessUnit: data.businessUnit,
            careerLevel: data.careerLevel,
            jobRole: data.jobRole,
            projectId: data.projectId,
            projectName: data.projectName,
          },
        ],
      })
      revalidatePath("/")

      let successMessage = "Assessment saved successfully.";
      if (assessmentConfig.intervalMonths > 0) {
        successMessage += ` You can take another assessment after ${assessmentConfig.intervalMonths} months.`;
      }

      return { success: true, message: successMessage }
    }
  } catch (error) {
    console.error("Error saving assessment:", error)
    return { success: false, message: "Failed to save assessment" }
  }
}

export async function getUserAssessments(
  userId: string,
  businessUnit?: string,
  assessmentType: string = 'self',
  targetUserId?: string,
  cycleId?: string,
  projectId?: string
) {
  try {
    const database = await getDb()

    // Build the query based on assessment type
    let query: any = {};

    if (assessmentType === 'self') {
      // Self assessment - user is both the subject and the reviewer
      query = { userId, reviewerId: userId };
    } else if (assessmentType === 'manager') {
      // Manager assessment - user is the reviewer, target is the subject
      query = { userId: targetUserId, reviewerId: userId };
    } else if (assessmentType === 'peer') {
      // Peer assessment - user is the reviewer, target is the subject
      query = { userId: targetUserId, reviewerId: userId };
    }

    // Add cycle ID if provided
    if (cycleId) {
      query.cycleId = cycleId;
    }

    // Add project ID if provided
    if (projectId) {
      query.projectId = projectId;
    }

    const assessments = await database
      .collection("assessments")
      .find(query)
      .project({ name: 1, updatedAt: 1, createdAt: 1 })
      .sort({ updatedAt: -1 })
      .toArray()

    // Default values if no interval restriction
    let canTakeNewAssessment = true;
    let nextAssessmentDate = null;

    // Check if there's an interval restriction between assessments
    if (assessmentConfig.intervalMonths > 0) {
      // Calculate the date from which to check for recent assessments
      const restrictionDate = new Date();
      restrictionDate.setMonth(restrictionDate.getMonth() - assessmentConfig.intervalMonths);

      const recentAssessment = assessments.find((a: { createdAt: Date }) => new Date(a.createdAt) >= restrictionDate);

      if (recentAssessment) {
        canTakeNewAssessment = false;
        nextAssessmentDate = new Date(recentAssessment.createdAt);
        nextAssessmentDate.setMonth(nextAssessmentDate.getMonth() + assessmentConfig.intervalMonths);
      }
    }

    // Get the skill template for the user's business unit if provided
    let skillTemplate = null;
    if (businessUnit) {
      const templateResult = await getBUSkillTemplate(businessUnit);
      if (templateResult.success) {
        skillTemplate = templateResult.data;
      }
    }

    // Get user's email from the database
    let userEmail = "";
    if (assessmentType === 'self') {
      // Check if userId is a valid ObjectId
      let userProfile;
      try {
        if (userId && userId.match(/^[0-9a-fA-F]{24}$/)) {
          // Valid ObjectId format
          userProfile = await database.collection('user_profiles').findOne(
            { _id: new ObjectId(userId) },
            { projection: { email: 1 } }
          );
        } else {
          // Try to find by userId directly (might be email or other identifier)
          userProfile = await database.collection('user_profiles').findOne(
            { userId: userId },
            { projection: { email: 1 } }
          );

          // If not found, try to find by email
          if (!userProfile) {
            userProfile = await database.collection('user_profiles').findOne(
              { email: userId },
              { projection: { email: 1 } }
            );
          }
        }
      } catch (error) {
        console.error("Error finding user profile:", error);
      }

      if (userProfile) {
        userEmail = userProfile.email;
      }
    } else if (targetUserId) {
      // Check if targetUserId is a valid ObjectId
      let userProfile;
      try {
        if (targetUserId && targetUserId.match(/^[0-9a-fA-F]{24}$/)) {
          // Valid ObjectId format
          userProfile = await database.collection('user_profiles').findOne(
            { _id: new ObjectId(targetUserId) },
            { projection: { email: 1 } }
          );
        } else {
          // Try to find by targetUserId directly (might be email or other identifier)
          userProfile = await database.collection('user_profiles').findOne(
            { userId: targetUserId },
            { projection: { email: 1 } }
          );

          // If not found, try to find by email
          if (!userProfile) {
            userProfile = await database.collection('user_profiles').findOne(
              { email: targetUserId },
              { projection: { email: 1 } }
            );
          }
        }
      } catch (error) {
        console.error("Error finding target user profile:", error);
      }

      if (userProfile) {
        userEmail = userProfile.email;
      }
    }

    // Get project skills for the user based on their assigned projects
    let userProjectSkills: any[] = [];
    if (userEmail) {
      const projectSkillsResult = await getUserProjectSkills(userEmail);
      if (projectSkillsResult.success) {
        userProjectSkills = projectSkillsResult.data;
      }
    }

    return {
      success: true,
      data: assessments.map((a: { name: string; updatedAt: Date }) => ({
        name: a.name,
        date: a.updatedAt.toISOString(),
      })),
      canTakeNewAssessment,
      nextAssessmentDate: nextAssessmentDate ? nextAssessmentDate.toISOString() : null,
      allowUpdates: assessmentConfig.allowUpdates,
      intervalMonths: assessmentConfig.intervalMonths,
      skillTemplate: skillTemplate,
      userProjectSkills: userProjectSkills
    }
  } catch (error) {
    console.error("Error fetching user assessments:", error)
    return {
      success: false,
      data: [],
      message: "Failed to fetch assessments",
      canTakeNewAssessment: true,
      nextAssessmentDate: null,
      allowUpdates: assessmentConfig.allowUpdates,
      intervalMonths: assessmentConfig.intervalMonths,
      userProjectSkills: []
    }
  }
}

export async function loadAssessment(
  userId: string,
  name: string,
  assessmentType: string = 'self',
  targetUserId?: string,
  cycleId?: string,
  projectId?: string
) {
  try {
    const database = await getDb()
    // Build the query based on assessment type
    let query: any = { name };

    if (assessmentType === 'self') {
      // Self assessment - user is both the subject and the reviewer
      query.userId = userId;
      query.reviewerId = userId;
    } else if (assessmentType === 'manager') {
      // Manager assessment - user is the reviewer, target is the subject
      query.userId = targetUserId;
      query.reviewerId = userId;
    } else if (assessmentType === 'peer') {
      // Peer assessment - user is the reviewer, target is the subject
      query.userId = targetUserId;
      query.reviewerId = userId;
    }

    // Add cycle ID if provided
    if (cycleId) {
      query.cycleId = cycleId;
    }

    // Add project ID if provided
    if (projectId) {
      query.projectId = projectId;
    }

    const assessment = await database.collection("assessments").findOne(query)

    if (!assessment) {
      return { success: false, message: "Assessment not found" }
    }

    // Get the skill template for the user's business unit if available
    let skillTemplate = null;
    if (assessment.businessUnit) {
      const templateResult = await getBUSkillTemplate(assessment.businessUnit);
      if (templateResult.success) {
        skillTemplate = templateResult.data;
      }
    }

    // Get project skill template if available
    let projectSkillTemplate = null;
    if (assessment.projectId) {
      try {
        const templateResult = await getProjectSkillTemplate(assessment.projectId, 'system');
        if (templateResult.success) {
          projectSkillTemplate = templateResult.data;
        }
      } catch (error) {
        console.error("Error fetching project skill template:", error);
      }
    }

    // Get user's email from the database
    let userEmail = "";
    if (assessmentType === 'self') {
      // Check if userId is a valid ObjectId
      let userProfile;
      try {
        if (userId && userId.match(/^[0-9a-fA-F]{24}$/)) {
          // Valid ObjectId format
          userProfile = await database.collection('user_profiles').findOne(
            { _id: new ObjectId(userId) },
            { projection: { email: 1 } }
          );
        } else {
          // Try to find by userId directly (might be email or other identifier)
          userProfile = await database.collection('user_profiles').findOne(
            { userId: userId },
            { projection: { email: 1 } }
          );

          // If not found, try to find by email
          if (!userProfile) {
            userProfile = await database.collection('user_profiles').findOne(
              { email: userId },
              { projection: { email: 1 } }
            );
          }
        }
      } catch (error) {
        console.error("Error finding user profile:", error);
      }

      if (userProfile) {
        userEmail = userProfile.email;
      }
    } else if (targetUserId) {
      // Check if targetUserId is a valid ObjectId
      let userProfile;
      try {
        if (targetUserId && targetUserId.match(/^[0-9a-fA-F]{24}$/)) {
          // Valid ObjectId format
          userProfile = await database.collection('user_profiles').findOne(
            { _id: new ObjectId(targetUserId) },
            { projection: { email: 1 } }
          );
        } else {
          // Try to find by targetUserId directly (might be email or other identifier)
          userProfile = await database.collection('user_profiles').findOne(
            { userId: targetUserId },
            { projection: { email: 1 } }
          );

          // If not found, try to find by email
          if (!userProfile) {
            userProfile = await database.collection('user_profiles').findOne(
              { email: targetUserId },
              { projection: { email: 1 } }
            );
          }
        }
      } catch (error) {
        console.error("Error finding target user profile:", error);
      }

      if (userProfile) {
        userEmail = userProfile.email;
      }
    }

    // Get project skills for the user based on their assigned projects
    let userProjectSkills: any[] = [];
    if (userEmail) {
      const projectSkillsResult = await getUserProjectSkills(userEmail);
      if (projectSkillsResult.success) {
        userProjectSkills = projectSkillsResult.data;
      }
    }

    return {
      success: true,
      data: {
        skills: assessment.skills,
        projectSkills: assessment.projectSkills || [],
        history: assessment.history || [],
        createdAt: assessment.createdAt,
        updatedAt: assessment.updatedAt,
        businessUnit: assessment.businessUnit,
        careerLevel: assessment.careerLevel,
        jobRole: assessment.jobRole,
        projectId: assessment.projectId,
        projectName: assessment.projectName,
      },
      skillTemplate: skillTemplate,
      projectSkillTemplate: projectSkillTemplate,
      userProjectSkills: userProjectSkills
    }
  } catch (error) {
    console.error("Error loading assessment:", error)
    return { success: false, message: "Failed to load assessment" }
  }
}

/**
 * Get project skills for a user based on their assigned projects
 * @param userEmail Email of the user
 */
export async function getUserProjectSkills(userEmail: string) {
  try {
    const database = await getDb();

    // Get the user profile to find assigned projects
    const userProfile = await database.collection('user_profiles').findOne({ email: userEmail });

    if (!userProfile || !userProfile.projects || userProfile.projects.length === 0) {
      return {
        success: true,
        data: []
      };
    }

    // Get skills for each project the user is assigned to
    const projectSkills: any[] = [];

    for (const project of userProfile.projects) {
      const templateResult = await getProjectSkillTemplate(project.projectId, 'system');

      if (templateResult.success && templateResult.data && templateResult.data.skills) {
        // Add project information to each skill
        projectSkills.push({
          projectId: project.projectId,
          projectName: project.projectName,
          skills: templateResult.data.skills.map((skill: any) => ({
            ...skill,
            projectId: project.projectId,
            projectName: project.projectName,
            currentLevel: null
          }))
        });
      }
    }

    return {
      success: true,
      data: projectSkills
    };
  } catch (error) {
    console.error("Error getting user project skills:", error);
    return {
      success: false,
      data: [],
      message: "Failed to get user project skills"
    };
  }
}

export async function getSkillHistory(userId: string, name: string, skillId: string) {
  try {
    const database = await getDb()
    const assessment = await database.collection("assessments").findOne({
      userId,
      name,
    })

    if (!assessment || !assessment.history) {
      return { success: false, data: [], message: "No history found" }
    }

    // Extract the specific skill's history
    const skillHistory = assessment.history
      .map((entry: { date: Date; skills: Array<{ id: string; currentLevel?: number | null }>; businessUnit?: string; careerLevel?: string; jobRole?: string }) => ({
        date: entry.date,
        level: entry.skills.find((s: { id: string; currentLevel?: number | null }) => s.id === skillId)?.currentLevel || null,
        businessUnit: entry.businessUnit,
        careerLevel: entry.careerLevel,
        jobRole: entry.jobRole,
      }))
      .filter((entry: { level: number | null }) => entry.level !== null)

    return { success: true, data: skillHistory }
  } catch (error) {
    console.error("Error fetching skill history:", error)
    return { success: false, data: [], message: "Failed to fetch skill history" }
  }
}

export async function getAllSkillsHistory(userId: string, name: string) {
  try {
    const database = await getDb()
    const assessment = await database.collection("assessments").findOne({
      userId,
      name,
    })

    if (!assessment || !assessment.history) {
      return { success: false, data: [], message: "No history found" }
    }

    // Format the history data for visualization
    const historyData = assessment.history.map((entry: { date: Date; skills: Array<{ id: string; currentLevel?: number | null }>; businessUnit?: string; careerLevel?: string; jobRole?: string }) => ({
      date: entry.date,
      businessUnit: entry.businessUnit,
      careerLevel: entry.careerLevel,
      jobRole: entry.jobRole,
      skills: entry.skills.reduce((acc: Record<string, number>, skill: { id: string; currentLevel?: number | null }) => {
        if (skill.currentLevel) {
          acc[skill.id] = skill.currentLevel
        }
        return acc
      }, {}),
    }))

    return { success: true, data: historyData }
  } catch (error) {
    console.error("Error fetching skills history:", error)
    return { success: false, data: [], message: "Failed to fetch skills history" }
  }
}

/**
 * Get all assessments with pagination and search
 * @param adminEmail Email of the admin requesting the assessments
 * @param page Page number (1-based)
 * @param pageSize Number of items per page
 * @param search Search query
 * @param filters Additional filters (cycleId, businessUnit, assessmentType, etc.)
 */
export async function getAllAssessments(
  adminEmail: string,
  page: number = 1,
  pageSize: number = 10,
  search: string = '',
  filters: {
    cycleId?: string,
    businessUnit?: string,
    assessmentType?: 'self' | 'manager' | 'peer',
    userId?: string,
    reviewerId?: string,
    status?: string,
    projectId?: string
  } = {}
) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to view all assessments',
        data: [],
        totalItems: 0,
        totalPages: 0
      };
    }

    const database = await getDb();

    // Calculate skip value for pagination
    const skip = (page - 1) * pageSize;

    // Build query filter
    let filter: any = {};

    // Add filters if provided
    if (filters.cycleId) {
      filter.cycleId = filters.cycleId;
    }

    if (filters.businessUnit) {
      filter.businessUnit = filters.businessUnit;
    }

    if (filters.assessmentType) {
      filter.assessmentType = filters.assessmentType;
    }

    if (filters.userId) {
      filter.userId = filters.userId;
    }

    if (filters.reviewerId) {
      filter.reviewerId = filters.reviewerId;
    }

    if (filters.status) {
      filter.status = filters.status;
    }

    if (filters.projectId) {
      filter.projectId = filters.projectId;
    }

    // Add search filter if provided
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { userName: { $regex: search, $options: 'i' } },
        { userEmail: { $regex: search, $options: 'i' } },
        { targetUserName: { $regex: search, $options: 'i' } },
        { jobRole: { $regex: search, $options: 'i' } }
      ];
    }

    // Get total count for pagination
    const totalItems = await database
      .collection("assessments")
      .countDocuments(filter);

    // Calculate total pages
    const totalPages = Math.ceil(totalItems / pageSize);

    // Get paginated data
    const assessments = await database
      .collection("assessments")
      .find(filter)
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(pageSize)
      .toArray();

    return {
      success: true,
      data: assessments.map((assessment: any) => ({
        id: assessment._id.toString(),
        name: assessment.name,
        userId: assessment.userId,
        reviewerId: assessment.reviewerId,
        userName: assessment.userName,
        userEmail: assessment.userEmail,
        businessUnit: assessment.businessUnit,
        careerLevel: assessment.careerLevel,
        jobRole: assessment.jobRole,
        assessmentType: assessment.assessmentType,
        cycleId: assessment.cycleId,
        projectId: assessment.projectId,
        projectName: assessment.projectName,
        targetUserName: assessment.targetUserName,
        status: assessment.status,
        createdAt: assessment.createdAt,
        updatedAt: assessment.updatedAt
      })),
      totalItems,
      totalPages,
      currentPage: page
    };
  } catch (error) {
    console.error('Error fetching assessments:', error);
    return {
      success: false,
      message: 'Failed to fetch assessments',
      data: [],
      totalItems: 0,
      totalPages: 0
    };
  }
}
