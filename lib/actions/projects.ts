"use server"

import { ObjectId } from 'mongodb';
import { revalidatePath } from 'next/cache';
import { getDb } from '@/lib/db';
import { authConfig } from '@/lib/config';
import { Project, serializeProject, ProjectStatus } from '@/lib/models/project';
import { ProjectSkillTemplate, serializeProjectSkillTemplate } from '@/lib/models/project-skill-template';
import { serializeUserProfile } from '@/lib/models/user-profile';
import { projectSkillsData } from '@/lib/project-skills-data';
import { broadcastMessage } from '@/lib/sse-broadcast';
import { queryCache } from '@/lib/cache';

/**
 * Get all active projects (no pagination, for use in dropdowns and selectors)
 * @param email Email of the user requesting the projects
 */
export async function getActiveProjects(email: string) {
  try {
    // Verify admin permissions (for now, only admins can access this)
    if (!authConfig.isAdmin(email)) {
      return {
        success: false,
        message: 'You do not have permission to view projects',
        data: []
      };
    }

    const database = await getDb();

    // Get only active projects
    const projects = await database
      .collection('projects')
      .find({ status: 'active' })
      .sort({ name: 1 })
      .toArray();

    return {
      success: true,
      data: projects.map(serializeProject)
    };
  } catch (error) {
    console.error('Error fetching active projects:', error);
    return {
      success: false,
      message: 'Failed to fetch active projects',
      data: []
    };
  }
}

/**
 * Get all projects
 * @param adminEmail Email of the admin requesting the projects
 * @param page Page number for pagination
 * @param limit Number of items per page
 * @param search Optional search term
 */
export async function getAllProjects(
  adminEmail: string,
  page: number = 1,
  limit: number = 10,
  search: string = ''
) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to view projects',
        data: [],
        totalItems: 0,
        currentPage: page
      };
    }

    const database = await getDb();
    const skip = (page - 1) * limit;

    // Build query
    const query: any = {};
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { businessUnit: { $regex: search, $options: 'i' } }
      ];
    }

    // Get total count
    const totalItems = await database.collection('projects').countDocuments(query);

    // Get projects with pagination
    const projects = await database
      .collection('projects')
      .find(query)
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    return {
      success: true,
      data: projects.map(serializeProject),
      totalItems,
      currentPage: page
    };
  } catch (error) {
    console.error('Error fetching projects:', error);
    return {
      success: false,
      message: 'Failed to fetch projects',
      data: [],
      totalItems: 0,
      currentPage: page
    };
  }
}

/**
 * Get a project by ID
 * @param projectId ID of the project
 * @param adminEmail Email of the admin requesting the project
 */
export async function getProjectById(projectId: string, adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to view projects'
      };
    }

    const database = await getDb();
    const project = await database
      .collection('projects')
      .findOne({ _id: new ObjectId(projectId) });

    if (!project) {
      return {
        success: false,
        message: 'Project not found'
      };
    }

    return {
      success: true,
      data: serializeProject(project)
    };
  } catch (error) {
    console.error('Error fetching project:', error);
    return {
      success: false,
      message: 'Failed to fetch project'
    };
  }
}

/**
 * Create a new project
 * @param projectData Project data
 * @param adminEmail Email of the admin creating the project
 */
export async function createProject(
  projectData: Omit<Project, '_id' | 'createdAt' | 'updatedAt' | 'createdBy'>,
  adminEmail: string
) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to create projects'
      };
    }

    const database = await getDb();

    // Create the project
    const result = await database.collection('projects').insertOne({
      ...projectData,
      createdBy: adminEmail,
      createdAt: new Date(),
      updatedAt: new Date(),
      updatedBy: adminEmail
    });

    // Create default project skill template
    await database.collection('project-skill-templates').insertOne({
      projectId: result.insertedId.toString(),
      projectName: projectData.name,
      businessUnit: projectData.businessUnit,
      skills: JSON.parse(JSON.stringify(projectSkillsData)),
      updatedAt: new Date(),
      updatedBy: adminEmail
    });

    // Broadcast change to connected clients
    broadcastMessage({
      type: 'project-change',
      action: 'create',
      projectId: result.insertedId.toString()
    });

    revalidatePath('/admin/projects');
    return {
      success: true,
      message: 'Project created successfully',
      id: result.insertedId.toString()
    };
  } catch (error) {
    console.error('Error creating project:', error);
    return {
      success: false,
      message: 'Failed to create project'
    };
  }
}

/**
 * Update a project
 * @param projectId ID of the project to update
 * @param projectData Updated project data
 * @param adminEmail Email of the admin updating the project
 */
export async function updateProject(
  projectId: string,
  projectData: Partial<Omit<Project, '_id' | 'createdAt' | 'updatedAt' | 'createdBy'>>,
  adminEmail: string
) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to update projects'
      };
    }

    const database = await getDb();

    // Update the project
    const result = await database.collection('projects').updateOne(
      { _id: new ObjectId(projectId) },
      {
        $set: {
          ...projectData,
          updatedAt: new Date(),
          updatedBy: adminEmail
        }
      }
    );

    if (result.matchedCount === 0) {
      return {
        success: false,
        message: 'Project not found'
      };
    }

    // If project name was updated, update it in the skill template too
    if (projectData.name) {
      await database.collection('project-skill-templates').updateOne(
        { projectId },
        {
          $set: {
            projectName: projectData.name,
            updatedAt: new Date(),
            updatedBy: adminEmail
          }
        }
      );
    }

    // Broadcast change to connected clients
    broadcastMessage({
      type: 'project-change',
      action: 'update',
      projectId
    });

    revalidatePath('/admin/projects');
    revalidatePath(`/admin/projects/${projectId}`);
    return {
      success: true,
      message: 'Project updated successfully'
    };
  } catch (error) {
    console.error('Error updating project:', error);
    return {
      success: false,
      message: 'Failed to update project'
    };
  }
}

/**
 * Delete a project
 * @param projectId ID of the project to delete
 * @param adminEmail Email of the admin deleting the project
 */
export async function deleteProject(projectId: string, adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to delete projects'
      };
    }

    const database = await getDb();

    // Delete the project
    const result = await database.collection('projects').deleteOne({
      _id: new ObjectId(projectId)
    });

    if (result.deletedCount === 0) {
      return {
        success: false,
        message: 'Project not found'
      };
    }

    // Delete the project skill template
    await database.collection('project-skill-templates').deleteOne({
      projectId
    });

    // Remove project from user profiles
    await database.collection('user_profiles').updateMany(
      { 'projects.projectId': projectId },
      { $pull: { projects: { projectId } } }
    );

    // Broadcast change to connected clients
    broadcastMessage({
      type: 'project-change',
      action: 'delete',
      projectId
    });

    revalidatePath('/admin/projects');
    return {
      success: true,
      message: 'Project deleted successfully'
    };
  } catch (error) {
    console.error('Error deleting project:', error);
    return {
      success: false,
      message: 'Failed to delete project'
    };
  }
}

/**
 * Get project skill template
 * @param projectId ID of the project
 * @param adminEmail Email of the admin requesting the template
 */
export async function getProjectSkillTemplate(projectId: string, adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to view project skill templates'
      };
    }

    const database = await getDb();
    const template = await database
      .collection('project-skill-templates')
      .findOne({ projectId });

    // If no template exists, create a default one
    if (!template) {
      // Get the project first
      const project = await database
        .collection('projects')
        .findOne({ _id: new ObjectId(projectId) });

      if (!project) {
        return {
          success: false,
          message: 'Project not found'
        };
      }

      // Create default template
      const defaultTemplate: ProjectSkillTemplate = {
        projectId,
        projectName: project.name,
        businessUnit: project.businessUnit,
        skills: JSON.parse(JSON.stringify(projectSkillsData)),
        updatedAt: new Date(),
        updatedBy: 'system'
      };

      return {
        success: true,
        data: serializeProjectSkillTemplate(defaultTemplate),
        isDefault: true
      };
    }

    return {
      success: true,
      data: serializeProjectSkillTemplate(template),
      isDefault: false
    };
  } catch (error) {
    console.error('Error fetching project skill template:', error);
    return {
      success: false,
      message: 'Failed to fetch project skill template'
    };
  }
}

/**
 * Save project skill template
 * @param projectId ID of the project
 * @param templateData Template data to save
 * @param adminEmail Email of the admin saving the template
 */
export async function saveProjectSkillTemplate(
  projectId: string,
  templateData: { skills: any[] },
  adminEmail: string
) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to update project skill templates'
      };
    }

    const database = await getDb();

    // Get the project first
    const project = await database
      .collection('projects')
      .findOne({ _id: new ObjectId(projectId) });

    if (!project) {
      return {
        success: false,
        message: 'Project not found'
      };
    }

    // Check if template exists
    const existingTemplate = await database
      .collection('project-skill-templates')
      .findOne({ projectId });

    if (existingTemplate) {
      // Update existing template
      await database.collection('project-skill-templates').updateOne(
        { projectId },
        {
          $set: {
            skills: templateData.skills,
            updatedAt: new Date(),
            updatedBy: adminEmail
          }
        }
      );
    } else {
      // Create new template
      await database.collection('project-skill-templates').insertOne({
        projectId,
        projectName: project.name,
        businessUnit: project.businessUnit,
        skills: templateData.skills,
        updatedAt: new Date(),
        updatedBy: adminEmail
      });
    }

    // Broadcast change to connected clients
    broadcastMessage({
      type: 'project-skill-template-change',
      projectId
    });

    revalidatePath(`/admin/projects/${projectId}/skills`);
    return {
      success: true,
      message: 'Project skill template saved successfully'
    };
  } catch (error) {
    console.error('Error saving project skill template:', error);
    return {
      success: false,
      message: 'Failed to save project skill template'
    };
  }
}

/**
 * Assign users to a project
 * @param projectId ID of the project
 * @param userIds Array of user IDs to assign to the project
 * @param adminEmail Email of the admin making the assignment
 */
export async function assignUsersToProject(
  projectId: string,
  userIds: string[],
  adminEmail: string
) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to assign users to projects'
      };
    }

    const database = await getDb();

    // Get the project
    const project = await database
      .collection('projects')
      .findOne({ _id: new ObjectId(projectId) });

    if (!project) {
      return {
        success: false,
        message: 'Project not found'
      };
    }

    // Create project assignment object
    const projectAssignment = {
      projectId,
      projectName: project.name
    };

    // Update each user profile
    let updated = 0;
    for (const userId of userIds) {
      try {
        // Update user profile
        const result = await database.collection('user_profiles').updateOne(
          { _id: new ObjectId(userId) },
          {
            $addToSet: { projects: projectAssignment },
            $set: { updatedAt: new Date() }
          }
        );

        if (result.matchedCount > 0) {
          updated++;
          // Invalidate cache for this user
          const user = await database.collection('user_profiles').findOne({ _id: new ObjectId(userId) });
          if (user && user.email) {
            queryCache.delete(`user_profile:${user.email}`);
          }
        }
      } catch (error) {
        console.error(`Error assigning user ${userId} to project:`, error);
      }
    }

    revalidatePath(`/admin/projects/${projectId}/users`);
    return {
      success: true,
      message: `${updated} users assigned to project successfully`
    };
  } catch (error) {
    console.error('Error assigning users to project:', error);
    return {
      success: false,
      message: 'Failed to assign users to project'
    };
  }
}

/**
 * Remove users from a project
 * @param projectId ID of the project
 * @param userIds Array of user IDs to remove from the project
 * @param adminEmail Email of the admin making the change
 */
export async function removeUsersFromProject(
  projectId: string,
  userIds: string[],
  adminEmail: string
) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to remove users from projects'
      };
    }

    const database = await getDb();

    // Update each user profile
    let updated = 0;
    for (const userId of userIds) {
      try {
        // Update user profile
        const result = await database.collection('user_profiles').updateOne(
          { _id: new ObjectId(userId) },
          {
            $pull: { projects: { projectId } },
            $set: { updatedAt: new Date() }
          }
        );

        if (result.matchedCount > 0) {
          updated++;
          // Invalidate cache for this user
          const user = await database.collection('user_profiles').findOne({ _id: new ObjectId(userId) });
          if (user && user.email) {
            queryCache.delete(`user_profile:${user.email}`);
          }
        }
      } catch (error) {
        console.error(`Error removing user ${userId} from project:`, error);
      }
    }

    revalidatePath(`/admin/projects/${projectId}/users`);
    return {
      success: true,
      message: `${updated} users removed from project successfully`
    };
  } catch (error) {
    console.error('Error removing users from project:', error);
    return {
      success: false,
      message: 'Failed to remove users from project'
    };
  }
}

/**
 * Get users assigned to a project
 * @param projectId ID of the project
 * @param adminEmail Email of the admin requesting the users
 */
export async function getUsersInProject(
  projectId: string,
  adminEmail: string,
  page: number = 1,
  limit: number = 10,
  search: string = ''
) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to view project users',
        data: [],
        totalItems: 0,
        currentPage: page
      };
    }

    const database = await getDb();
    const skip = (page - 1) * limit;

    // Build query
    const query: any = { 'projects.projectId': projectId };
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    // Get total count
    const totalItems = await database.collection('user_profiles').countDocuments(query);

    // Get users with pagination
    const users = await database
      .collection('user_profiles')
      .find(query)
      .sort({ name: 1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    return {
      success: true,
      data: users.map(serializeUserProfile),
      totalItems,
      currentPage: page
    };
  } catch (error) {
    console.error('Error fetching project users:', error);
    return {
      success: false,
      message: 'Failed to fetch project users',
      data: [],
      totalItems: 0,
      currentPage: page
    };
  }
}

/**
 * Export all projects with their team members and skills in CSV format
 * @param adminEmail Email of the admin requesting the export
 */
export async function exportProjectsToCSV(adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to export projects'
      };
    }

    const database = await getDb();

    // Get all projects
    const projects = await database.collection('projects').find({}).toArray();

    const csvRows: string[][] = [];

    // Add header row
    csvRows.push([
      'project_name',
      'project_description',
      'business_unit',
      'status',
      'member_email',
      'member_name',
      'skill_category',
      'skill_description',
      'target_cl2',
      'target_cl3',
      'target_cl4',
      'target_cl5',
      'target_cl6'
    ]);

    // Process each project
    for (const project of projects) {
      const projectId = project._id.toString();

      // Get project team members
      const teamMembers = await database
        .collection('user_profiles')
        .find({ 'projects.projectId': projectId })
        .toArray();

      // Get project skills template
      const skillTemplate = await database
        .collection('project-skill-templates')
        .findOne({ projectId });

      const skills = skillTemplate?.skills || [];

      // If project has no team members or skills, add a basic row
      if (teamMembers.length === 0 && skills.length === 0) {
        csvRows.push([
          project.name || '',
          project.description || '',
          project.businessUnit || '',
          project.status || '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          ''
        ]);
      } else if (teamMembers.length === 0) {
        // Project has skills but no team members
        for (const skill of skills) {
          csvRows.push([
            project.name || '',
            project.description || '',
            project.businessUnit || '',
            project.status || '',
            '',
            '',
            skill.category || '',
            skill.description || '',
            skill.targetCL2?.toString() || '',
            skill.targetCL3?.toString() || '',
            skill.targetCL4?.toString() || '',
            skill.targetTM12?.toString() || '',
            skill.targetTM34?.toString() || ''
          ]);
        }
      } else if (skills.length === 0) {
        // Project has team members but no skills
        for (const member of teamMembers) {
          csvRows.push([
            project.name || '',
            project.description || '',
            project.businessUnit || '',
            project.status || '',
            member.email || '',
            member.name || '',
            '',
            '',
            '',
            '',
            '',
            '',
            ''
          ]);
        }
      } else {
        // Project has both team members and skills
        for (const member of teamMembers) {
          for (const skill of skills) {
            csvRows.push([
              project.name || '',
              project.description || '',
              project.businessUnit || '',
              project.status || '',
              member.email || '',
              member.name || '',
              skill.category || '',
              skill.description || '',
              skill.targetCL2?.toString() || '',
              skill.targetCL3?.toString() || '',
              skill.targetCL4?.toString() || '',
              skill.targetCL5?.toString() || '',
              skill.targetCL6?.toString() || ''
            ]);
          }
        }
      }
    }

    // Convert to CSV string
    const csvContent = csvRows
      .map(row => row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(','))
      .join('\n');

    return {
      success: true,
      data: csvContent
    };
  } catch (error) {
    console.error('Error exporting projects to CSV:', error);
    return {
      success: false,
      message: 'Failed to export projects'
    };
  }
}

/**
 * Import projects from CSV content
 * @param csvContent The CSV content as string
 * @param adminEmail Email of the admin importing projects
 */
export async function importProjectsFromCSV(csvContent: string, adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to import projects'
      };
    }

    const database = await getDb();

    // Parse CSV content
    const lines = csvContent.split('\n').filter(line => line.trim());
    if (lines.length < 2) {
      return {
        success: false,
        message: 'CSV file must contain at least a header row and one data row'
      };
    }

    // Extract headers
    const headers = lines[0].split(',').map(header => header.trim().replace(/"/g, ''));

    // Validate required headers
    const requiredHeaders = ['project_name', 'business_unit', 'status'];
    const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));
    if (missingHeaders.length > 0) {
      return {
        success: false,
        message: `Missing required headers: ${missingHeaders.join(', ')}`
      };
    }

    // Parse data rows
    const projects = new Map<string, any>();
    const projectMembers = new Map<string, Set<string>>();
    const projectSkills = new Map<string, any[]>();

    let processedRows = 0;
    let errorRows = 0;

    for (let i = 1; i < lines.length; i++) {
      try {
        const values = lines[i].split(',').map(value => value.trim().replace(/^"|"$/g, ''));
        const row: any = {};

        headers.forEach((header, index) => {
          row[header] = values[index] || '';
        });

        // Validate required fields
        if (!row.project_name || !row.business_unit || !row.status) {
          errorRows++;
          continue;
        }

        const projectKey = row.project_name.toLowerCase();

        // Store project basic info
        if (!projects.has(projectKey)) {
          projects.set(projectKey, {
            name: row.project_name,
            description: row.project_description || '',
            businessUnit: row.business_unit,
            status: row.status
          });
          projectMembers.set(projectKey, new Set());
          projectSkills.set(projectKey, []);
        }

        // Add team member if provided
        if (row.member_email && row.member_name) {
          projectMembers.get(projectKey)?.add(JSON.stringify({
            email: row.member_email,
            name: row.member_name
          }));
        }

        // Add skill if provided
        if (row.skill_category && row.skill_description) {
          const skill = {
            id: row.skill_category.toLowerCase().replace(/[^a-z0-9]/g, ''),
            category: row.skill_category,
            description: row.skill_description,
            targetCL2: parseInt(row.target_cl2) || 2,
            targetCL3: parseInt(row.target_cl3) || 3,
            targetCL4: parseInt(row.target_cl4) || 4,
            targetCL5: parseInt(row.target_cl5) || 4,
            targetCL6: parseInt(row.target_cl6) || 5,
            currentLevel: null,
            isCustom: true
          };

          // Check if skill already exists for this project
          const existingSkills = projectSkills.get(projectKey) || [];
          const skillExists = existingSkills.some(s => s.id === skill.id);
          if (!skillExists) {
            existingSkills.push(skill);
            projectSkills.set(projectKey, existingSkills);
          }
        }

        processedRows++;
      } catch (error) {
        console.error(`Error processing row ${i + 1}:`, error);
        errorRows++;
      }
    }

    // Create projects in database
    let createdProjects = 0;
    let updatedProjects = 0;
    let createdUsers = 0;
    let assignedUsers = 0;

    for (const [projectKey, projectData] of projects) {
      try {
        // Check if project already exists
        const existingProject = await database.collection('projects').findOne({
          name: { $regex: new RegExp(`^${projectData.name}$`, 'i') }
        });

        let projectId: string;

        if (existingProject) {
          // Update existing project
          await database.collection('projects').updateOne(
            { _id: existingProject._id },
            {
              $set: {
                ...projectData,
                updatedAt: new Date(),
                updatedBy: adminEmail
              }
            }
          );
          projectId = existingProject._id.toString();
          updatedProjects++;
        } else {
          // Create new project
          const result = await database.collection('projects').insertOne({
            ...projectData,
            createdBy: adminEmail,
            createdAt: new Date(),
            updatedAt: new Date()
          });
          projectId = result.insertedId.toString();
          createdProjects++;
        }

        // Handle team members
        const members = Array.from(projectMembers.get(projectKey) || [])
          .map(memberStr => JSON.parse(memberStr));

        for (const member of members) {
          // Find user by email
          let user = await database.collection('user_profiles').findOne({
            email: member.email
          });

          if (!user) {
            // Create user if they don't exist
            const newUser = {
              email: member.email,
              name: member.name,
              businessUnit: projectData.businessUnit, // Use project's business unit as default
              careerLevel: 'CL2', // Default career level
              jobRole: 'Team Member', // Default job role
              profileCompleted: false,
              hasLoggedIn: false,
              createdBy: adminEmail,
              projects: [{
                projectId,
                projectName: projectData.name
              }],
              createdAt: new Date(),
              updatedAt: new Date()
            };

            const result = await database.collection('user_profiles').insertOne(newUser);
            createdUsers++;
            console.log(`Created new user: ${member.name} (${member.email}) for project ${projectData.name}`);
          } else {
            // Add project to existing user's projects if not already assigned
            const userProjects = user.projects || [];
            const isAssigned = userProjects.some((p: any) => p.projectId === projectId);

            if (!isAssigned) {
              await database.collection('user_profiles').updateOne(
                { _id: user._id },
                {
                  $push: {
                    projects: {
                      projectId,
                      projectName: projectData.name
                    }
                  },
                  $set: { updatedAt: new Date() }
                }
              );
              assignedUsers++;
              console.log(`Assigned existing user: ${user.name} (${user.email}) to project ${projectData.name}`);
            }
          }
        }

        // Handle project skills
        const skills = projectSkills.get(projectKey) || [];
        if (skills.length > 0) {
          // Update or create project skill template
          await database.collection('project-skill-templates').updateOne(
            { projectId },
            {
              $set: {
                projectId,
                projectName: projectData.name,
                businessUnit: projectData.businessUnit,
                skills,
                updatedAt: new Date(),
                updatedBy: adminEmail
              }
            },
            { upsert: true }
          );
        }

      } catch (error) {
        console.error(`Error creating/updating project ${projectData.name}:`, error);
      }
    }

    revalidatePath('/admin/projects');

    return {
      success: true,
      message: `Import completed: ${createdProjects} projects created, ${updatedProjects} projects updated, ${createdUsers} users created, ${assignedUsers} users assigned, ${processedRows} rows processed, ${errorRows} rows had errors`
    };

  } catch (error) {
    console.error('Error importing projects from CSV:', error);
    return {
      success: false,
      message: 'Failed to import projects from CSV'
    };
  }
}
