"use server"

import { ObjectId } from 'mongodb';
import { revalidatePath } from 'next/cache';
import { getDb } from '@/lib/db';
import { skillsData } from '@/lib/skills-data';
import { projectSkillsData } from '@/lib/project-skills-data';
import { icSkillsData } from '@/lib/ic-skills-data';
import { BUSkillTemplate, businessUnitNames, legacyBusinessUnitMapping, serializeBUSkillTemplate } from '@/lib/models/skill-template';
import { authConfig } from '@/lib/config';
import { redirect } from 'next/navigation';
import { broadcastMessage } from '@/lib/sse-broadcast';

/**
 * Get all business unit skill templates
 */
export async function getBUSkillTemplates() {
  try {
    console.log('Fetching skill templates...');
    const database = await getDb();
    console.log('Database connection successful');

    // Check if the collection exists
    const collections = await database.listCollections({ name: 'skill-templates' }).toArray();
    console.log('Collections check:', collections);

    const templates = await database
      .collection('skill-templates')
      .find({})
      .sort({ businessUnit: 1 })
      .toArray();

    console.log('Raw templates from DB:', templates);
    const serializedTemplates = templates.map((template: any) => serializeBUSkillTemplate(template));
    console.log('Serialized templates:', serializedTemplates);

    return {
      success: true,
      data: serializedTemplates
    };
  } catch (error) {
    console.error('Error fetching BU skill templates:', error);
    return {
      success: false,
      message: 'Failed to fetch business unit skill templates',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Get a specific business unit skill template
 * @param businessUnit The business unit code
 */
export async function getBUSkillTemplate(businessUnit: string) {
  try {
    const database = await getDb();

    // Use case-insensitive matching for business unit
    const template = await database
      .collection('skill-templates')
      .findOne({
        businessUnit: { $regex: new RegExp(`^${businessUnit}$`, 'i') }
      });

    if (!template) {
      // If no template exists, create a default one based on the IC skills for engineering or legacy skills for QA
      const coreSkills = businessUnit === 'engineering-ic'
        ? JSON.parse(JSON.stringify(icSkillsData))
        : JSON.parse(JSON.stringify(skillsData));

      const defaultTemplate: BUSkillTemplate = {
        businessUnit,
        name: businessUnitNames[businessUnit] || businessUnit,
        coreSkills,
        projectSkills: JSON.parse(JSON.stringify(projectSkillsData)),
        projectName: `${businessUnitNames[businessUnit] || businessUnit} Sample Project`,
        updatedAt: new Date(),
        updatedBy: 'system'
      };

      return {
        success: true,
        data: serializeBUSkillTemplate(defaultTemplate),
        isDefault: true
      };
    }

    return {
      success: true,
      data: serializeBUSkillTemplate(template),
      isDefault: false
    };
  } catch (error) {
    console.error(`Error fetching BU skill template for ${businessUnit}:`, error);
    return {
      success: false,
      message: `Failed to fetch skill template for ${businessUnit}`
    };
  }
}

/**
 * Save or update a business unit skill template
 * @param data The template data to save
 * @param adminEmail The email of the admin making the change
 */
export async function saveBUSkillTemplate(data: Omit<BUSkillTemplate, '_id'>, adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to manage skill templates'
      };
    }

    const database = await getDb();

    // Check if template already exists (case-insensitive)
    const existingTemplate = await database
      .collection('skill-templates')
      .findOne({
        businessUnit: { $regex: new RegExp(`^${data.businessUnit}$`, 'i') }
      });

    if (existingTemplate) {
      // Update existing template (using case-insensitive matching)
      await database.collection('skill-templates').updateOne(
        { businessUnit: { $regex: new RegExp(`^${data.businessUnit}$`, 'i') } },
        {
          $set: {
            name: data.name,
            coreSkills: data.coreSkills,
            projectSkills: data.projectSkills,
            projectName: data.projectName,
            updatedAt: new Date(),
            updatedBy: adminEmail
          }
        }
      );
    } else {
      // Create new template
      await database.collection('skill-templates').insertOne({
        businessUnit: data.businessUnit,
        name: data.name,
        coreSkills: data.coreSkills,
        projectSkills: data.projectSkills,
        projectName: data.projectName,
        updatedAt: new Date(),
        updatedBy: adminEmail
      });
    }

    // Broadcast a message to all connected clients
    try {
      await broadcastMessage({
        type: 'template_saved',
        businessUnit: data.businessUnit
      });
    } catch (broadcastError) {
      console.error('Error broadcasting template save:', broadcastError);
      // Continue even if broadcasting fails
    }

    revalidatePath('/admin/skills');
    return {
      success: true,
      message: `Skill template for ${data.name} saved successfully`
    };
  } catch (error) {
    console.error('Error saving BU skill template:', error);
    return {
      success: false,
      message: 'Failed to save skill template'
    };
  }
}

/**
 * Delete a business unit skill template
 * @param businessUnit The business unit code to delete
 * @param adminEmail The email of the admin making the change
 */
export async function deleteBUSkillTemplate(businessUnit: string, adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to manage skill templates'
      };
    }

    const database = await getDb();

    // Delete the template (using case-insensitive matching)
    await database.collection('skill-templates').deleteOne({
      businessUnit: { $regex: new RegExp(`^${businessUnit}$`, 'i') }
    });

    // Broadcast a message to all connected clients
    try {
      await broadcastMessage({
        type: 'template_deleted',
        businessUnit: businessUnit
      });
    } catch (broadcastError) {
      console.error('Error broadcasting template deletion:', broadcastError);
      // Continue even if broadcasting fails
    }

    revalidatePath('/admin/skills');
    return {
      success: true,
      message: `Skill template for ${businessUnit} deleted successfully`
    };
  } catch (error) {
    console.error(`Error deleting BU skill template for ${businessUnit}:`, error);
    return {
      success: false,
      message: `Failed to delete skill template for ${businessUnit}`
    };
  }
}

/**
 * Clear all assessments from the database
 * This is useful for testing purposes
 * @param adminEmail The email of the admin making the change
 */
export async function clearAllAssessments(adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to clear assessments'
      };
    }

    const database = await getDb();

    // Delete all assessments
    const result = await database.collection('assessments').deleteMany({});

    revalidatePath('/admin/skills');
    return {
      success: true,
      message: `Cleared ${result.deletedCount} assessments successfully`
    };
  } catch (error) {
    console.error('Error clearing assessments:', error);
    return {
      success: false,
      message: 'Failed to clear assessments'
    };
  }
}

/**
 * Export all skill templates to CSV format
 * @param adminEmail Email of the admin requesting the export
 */
export async function exportSkillTemplatesToCSV(adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to export skill templates'
      };
    }

    const database = await getDb();

    // Get all skill templates
    const templates = await database.collection('skill-templates').find({}).toArray();

    const csvRows: string[][] = [];

    // Add header row
    csvRows.push([
      'business_unit',
      'template_name',
      'skill_type',
      'skill_category',
      'skill_description',
      'target_cl2',
      'target_cl3',
      'target_cl4',
      'target_cl5',
      'target_cl6'
    ]);

    // Process each template
    for (const template of templates) {
      const businessUnit = template.businessUnit;
      const templateName = template.name;

      // Add core skills
      if (template.coreSkills && Array.isArray(template.coreSkills)) {
        for (const skill of template.coreSkills) {
          csvRows.push([
            businessUnit,
            templateName,
            'core',
            skill.category || '',
            skill.description || '',
            skill.targetCL2?.toString() || '',
            skill.targetCL3?.toString() || '',
            skill.targetCL4?.toString() || '',
            skill.targetTM12?.toString() || '',
            skill.targetTM34?.toString() || ''
          ]);
        }
      }

      // Add project skills
      if (template.projectSkills && Array.isArray(template.projectSkills)) {
        for (const skill of template.projectSkills) {
          csvRows.push([
            businessUnit,
            templateName,
            'project',
            skill.category || '',
            skill.description || '',
            skill.targetCL2?.toString() || '',
            skill.targetCL3?.toString() || '',
            skill.targetCL4?.toString() || '',
            skill.targetCL5?.toString() || '',
            skill.targetCL6?.toString() || ''
          ]);
        }
      }
    }

    // Convert to CSV string
    const csvContent = csvRows
      .map(row => row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(','))
      .join('\n');

    return {
      success: true,
      data: csvContent
    };
  } catch (error) {
    console.error('Error exporting skill templates to CSV:', error);
    return {
      success: false,
      message: 'Failed to export skill templates'
    };
  }
}

/**
 * Import skill templates from CSV content
 * @param csvContent The CSV content as string
 * @param adminEmail Email of the admin importing templates
 */
export async function importSkillTemplatesFromCSV(csvContent: string, adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to import skill templates'
      };
    }

    const database = await getDb();

    // Parse CSV content
    const lines = csvContent.split('\n').filter(line => line.trim());
    if (lines.length < 2) {
      return {
        success: false,
        message: 'CSV file must contain at least a header row and one data row'
      };
    }

    // Extract headers
    const headers = lines[0].split(',').map(header => header.trim().replace(/"/g, ''));

    // Validate required headers
    const requiredHeaders = ['business_unit', 'template_name', 'skill_type', 'skill_category'];
    const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));
    if (missingHeaders.length > 0) {
      return {
        success: false,
        message: `Missing required headers: ${missingHeaders.join(', ')}`
      };
    }

    // Parse data rows and group by business unit
    const templates = new Map<string, any>();
    let processedRows = 0;
    let errorRows = 0;

    for (let i = 1; i < lines.length; i++) {
      try {
        const values = lines[i].split(',').map(value => value.trim().replace(/^"|"$/g, ''));
        const row: any = {};

        headers.forEach((header, index) => {
          row[header] = values[index] || '';
        });

        // Validate required fields
        if (!row.business_unit || !row.template_name || !row.skill_type || !row.skill_category) {
          errorRows++;
          continue;
        }

        const businessUnit = row.business_unit.toLowerCase();
        const skillType = row.skill_type.toLowerCase();

        // Validate skill type
        if (skillType !== 'core' && skillType !== 'project') {
          errorRows++;
          continue;
        }

        // Initialize template if not exists
        if (!templates.has(businessUnit)) {
          templates.set(businessUnit, {
            businessUnit: row.business_unit,
            name: row.template_name,
            coreSkills: [],
            projectSkills: [],
            projectName: `${row.template_name} Sample Project`
          });
        }

        // Create skill object
        const skill = {
          id: row.skill_category.toLowerCase().replace(/[^a-z0-9]/g, ''),
          category: row.skill_category,
          description: row.skill_description || '',
          targetCL2: parseInt(row.target_cl2) || 2,
          targetCL3: parseInt(row.target_cl3) || 3,
          targetCL4: parseInt(row.target_cl4) || 4,
          targetCL5: parseInt(row.target_cl5) || 4,
          targetCL6: parseInt(row.target_cl6) || 5,
          currentLevel: null,
          isCustom: true
        };

        // Add skill to appropriate array
        const template = templates.get(businessUnit);
        if (skillType === 'core') {
          // Check if skill already exists
          const existingSkill = template.coreSkills.find((s: any) => s.id === skill.id);
          if (!existingSkill) {
            template.coreSkills.push(skill);
          }
        } else {
          // Check if skill already exists
          const existingSkill = template.projectSkills.find((s: any) => s.id === skill.id);
          if (!existingSkill) {
            template.projectSkills.push(skill);
          }
        }

        processedRows++;
      } catch (error) {
        console.error(`Error processing row ${i + 1}:`, error);
        errorRows++;
      }
    }

    // Save templates to database
    let createdTemplates = 0;
    let updatedTemplates = 0;

    for (const [businessUnit, templateData] of templates) {
      try {
        // Check if template already exists
        const existingTemplate = await database.collection('skill-templates').findOne({
          businessUnit: { $regex: new RegExp(`^${templateData.businessUnit}$`, 'i') }
        });

        if (existingTemplate) {
          // Update existing template
          await database.collection('skill-templates').updateOne(
            { businessUnit: { $regex: new RegExp(`^${templateData.businessUnit}$`, 'i') } },
            {
              $set: {
                name: templateData.name,
                coreSkills: templateData.coreSkills,
                projectSkills: templateData.projectSkills,
                projectName: templateData.projectName,
                updatedAt: new Date(),
                updatedBy: adminEmail
              }
            }
          );
          updatedTemplates++;
        } else {
          // Create new template
          await database.collection('skill-templates').insertOne({
            businessUnit: templateData.businessUnit,
            name: templateData.name,
            coreSkills: templateData.coreSkills,
            projectSkills: templateData.projectSkills,
            projectName: templateData.projectName,
            updatedAt: new Date(),
            updatedBy: adminEmail
          });
          createdTemplates++;
        }

        // Broadcast template update
        try {
          await broadcastMessage({
            type: 'template_saved',
            businessUnit: templateData.businessUnit
          });
        } catch (broadcastError) {
          console.error('Error broadcasting template save:', broadcastError);
        }

      } catch (error) {
        console.error(`Error saving template for ${templateData.businessUnit}:`, error);
      }
    }

    revalidatePath('/admin/skills');

    return {
      success: true,
      message: `Import completed: ${createdTemplates} templates created, ${updatedTemplates} templates updated, ${processedRows} rows processed, ${errorRows} rows had errors`
    };

  } catch (error) {
    console.error('Error importing skill templates from CSV:', error);
    return {
      success: false,
      message: 'Failed to import skill templates from CSV'
    };
  }
}
