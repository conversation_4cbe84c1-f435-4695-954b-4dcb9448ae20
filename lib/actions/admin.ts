"use server"

import { revalidatePath } from "next/cache"
import { db, getDb } from "@/lib/db"
import { authConfig } from "@/lib/config"
import { skillsData } from "@/lib/skills-data"
import { businessUnitOptions, careerLevelOptions } from "@/lib/models/user-profile"
import { AdminDashboardData, AdminDashboardResponse } from "@/lib/models/admin-dashboard"
import { ObjectId } from "mongodb"

// Define interfaces for MongoDB documents
interface AssessmentDocument {
  _id: ObjectId;
  userId: string;
  name: string;
  userName?: string;
  userEmail?: string;
  businessUnit?: string;
  careerLevel?: string;
  jobRole?: string;
  skills?: Array<{
    id: string;
    currentLevel: number | null;
    [key: string]: any;
  }>;
  history?: Array<{
    date: Date;
    skills: Array<{
      id: string;
      currentLevel: number | null;
      [key: string]: any;
    }>;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

interface UserProfileDocument {
  _id: ObjectId;
  email: string;
  name: string;
  businessUnit: string;
  careerLevel: string;
  jobRole: string;
  createdAt: Date;
  updatedAt: Date;
}

interface UserSummary {
  id: string;
  name: string;
  email: string;
  businessUnit: string;
  careerLevel: string;
  averageSkillLevel: number;
  topSkill?: {
    id: string;
    category: string;
    level: number;
  };
  lastLogin?: string;
  lastAssessment?: string;
  assessmentCount?: number;
}

/**
 * Get all assessments for admin reporting
 * This function is restricted to admin users only
 */
export async function getAllAssessments(adminEmail: string) {
  try {
    // Check if the user is an admin
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: "Unauthorized: Admin access required",
        data: []
      }
    }

    const database = await getDb()
    const assessments = await database
      .collection("assessments")
      .find({})
      .project({
        userId: 1,
        name: 1,
        userName: 1,
        userEmail: 1,
        businessUnit: 1,
        careerLevel: 1,
        jobRole: 1,
        skills: 1,
        createdAt: 1,
        updatedAt: 1
      })
      .sort({ updatedAt: -1 })
      .toArray()

    return {
      success: true,
      data: assessments.map((assessment: AssessmentDocument) => ({
        id: assessment._id.toString(),
        userId: assessment.userId,
        name: assessment.name,
        userName: assessment.userName || "Unknown",
        userEmail: assessment.userEmail || "Unknown",
        businessUnit: assessment.businessUnit || "Unknown",
        careerLevel: assessment.careerLevel || "Unknown",
        jobRole: assessment.jobRole || "Unknown",
        skills: assessment.skills || [],
        createdAt: assessment.createdAt,
        updatedAt: assessment.updatedAt
      }))
    }
  } catch (error) {
    console.error("Error fetching all assessments:", error)
    return {
      success: false,
      message: "Failed to fetch assessments",
      data: []
    }
  }
}

/**
 * Get aggregated skills data for admin dashboard
 * This function is restricted to admin users only
 */
export async function getSkillsAggregateData(adminEmail: string): Promise<AdminDashboardResponse> {
  try {
    // Check if the user is an admin
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: "Unauthorized: Admin access required",
        data: {
          totalAssessments: 0,
          totalUsers: 0,
          activeUsers: 0,
          inactiveUsers: 0,
          adminCreatedUsers: 0,
          selfRegisteredUsers: 0,
          skillAverages: {},
          businessUnitBreakdown: {},
          careerLevelBreakdown: {},
          skillsByBusinessUnit: {},
          skillsByCareerLevel: {},
          topSkills: [],
          improvementAreas: [],
          topUsers: [],
          recentlyActiveUsers: []
        }
      }
    }

    const database = await getDb()
    const assessments = await database
      .collection("assessments")
      .find({})
      .toArray()

    // Get user profiles to calculate user stats
    // Count the total number of user profiles directly
    const totalUsers = await database
      .collection("user_profiles")
      .countDocuments();

    // Get user profiles for detailed stats
    const userProfiles = await database
      .collection("user_profiles")
      .find({})
      .toArray();

    // Calculate user statistics with fallbacks for missing fields
    // If hasLoggedIn is not present, assume the user is active
    const activeUsers = userProfiles.filter((user: any) =>
      user.hasLoggedIn === true || (user.hasLoggedIn === undefined && user.email)
    ).length;

    const inactiveUsers = totalUsers - activeUsers;

    // If createdBy is not present, categorize based on email domain
    const adminCreatedUsers = userProfiles.filter((user: any) => {
      if (user.createdBy === 'admin') return true;
      if (user.createdBy === undefined) {
        // If email contains stratpoint.com, assume admin created
        return user.email && user.email.includes('stratpoint.com');
      }
      return false;
    }).length;

    // All other users are considered self-registered
    const selfRegisteredUsers = totalUsers - adminCreatedUsers;

    // If there are no assessments, return data with user counts but empty skill data
    if (assessments.length === 0) {
      return {
        success: true,
        data: {
          totalAssessments: 0,
          totalUsers,
          activeUsers,
          inactiveUsers,
          adminCreatedUsers,
          selfRegisteredUsers,
          skillAverages: {},
          businessUnitBreakdown: {},
          careerLevelBreakdown: {},
          skillsByBusinessUnit: {},
          skillsByCareerLevel: {},
          topSkills: [],
          improvementAreas: [],
          topUsers: [],
          recentlyActiveUsers: []
        }
      }
    }

    const aggregateData = {
      totalAssessments: assessments.length,
      totalUsers: totalUsers,
      activeUsers: Math.min(activeUsers, totalUsers), // Ensure we don't exceed total
      inactiveUsers: Math.min(inactiveUsers, totalUsers), // Ensure we don't exceed total
      adminCreatedUsers: Math.min(adminCreatedUsers, totalUsers), // Ensure we don't exceed total
      selfRegisteredUsers: Math.min(selfRegisteredUsers, totalUsers), // Ensure we don't exceed total
      skillAverages: {} as Record<string, number>,
      businessUnitBreakdown: {} as Record<string, number>,
      careerLevelBreakdown: {} as Record<string, number>,
      skillsByBusinessUnit: {} as Record<string, Record<string, number>>,
      skillsByCareerLevel: {} as Record<string, Record<string, number>>,
      topSkills: [] as { id: string, category: string, average: number }[],
      improvementAreas: [] as { id: string, category: string, average: number }[],
      topUsers: [] as UserSummary[],
      recentlyActiveUsers: [] as UserSummary[]
    }

    // Initialize business unit and career level counters
    businessUnitOptions.forEach(bu => {
      aggregateData.businessUnitBreakdown[bu] = 0
      aggregateData.skillsByBusinessUnit[bu] = {}
      skillsData.forEach(skill => {
        aggregateData.skillsByBusinessUnit[bu][skill.id] = 0
      })
    })

    careerLevelOptions.forEach(cl => {
      aggregateData.careerLevelBreakdown[cl] = 0
      aggregateData.skillsByCareerLevel[cl] = {}
      skillsData.forEach(skill => {
        aggregateData.skillsByCareerLevel[cl][skill.id] = 0
      })
    })

    // Initialize skill averages
    skillsData.forEach(skill => {
      aggregateData.skillAverages[skill.id] = 0
    })

    // Count assessments by business unit and career level
    assessments.forEach((assessment: AssessmentDocument) => {
      const bu = assessment.businessUnit || "Unknown"
      const cl = assessment.careerLevel || "Unknown"

      if (aggregateData.businessUnitBreakdown[bu] !== undefined) {
        aggregateData.businessUnitBreakdown[bu]++
      } else {
        aggregateData.businessUnitBreakdown[bu] = 1
      }

      if (aggregateData.careerLevelBreakdown[cl] !== undefined) {
        aggregateData.careerLevelBreakdown[cl]++
      } else {
        aggregateData.careerLevelBreakdown[cl] = 1
      }

      // Calculate skill averages
      if (assessment.skills && Array.isArray(assessment.skills)) {
        assessment.skills.forEach((skill: { id: string; currentLevel: number | null }) => {
          if (skill.currentLevel) {
            // Add to overall averages
            if (!aggregateData.skillAverages[skill.id]) {
              aggregateData.skillAverages[skill.id] = 0
            }
            aggregateData.skillAverages[skill.id] += skill.currentLevel

            // Add to business unit breakdown
            if (bu && aggregateData.skillsByBusinessUnit[bu]) {
              if (!aggregateData.skillsByBusinessUnit[bu][skill.id]) {
                aggregateData.skillsByBusinessUnit[bu][skill.id] = 0
              }
              aggregateData.skillsByBusinessUnit[bu][skill.id] += skill.currentLevel
            }

            // Add to career level breakdown
            if (cl && aggregateData.skillsByCareerLevel[cl]) {
              if (!aggregateData.skillsByCareerLevel[cl][skill.id]) {
                aggregateData.skillsByCareerLevel[cl][skill.id] = 0
              }
              aggregateData.skillsByCareerLevel[cl][skill.id] += skill.currentLevel
            }
          }
        })
      }
    })

    // Calculate averages
    const skillCounts = {} as Record<string, number>
    const buSkillCounts = {} as Record<string, Record<string, number>>
    const clSkillCounts = {} as Record<string, Record<string, number>>

    // Initialize counters
    skillsData.forEach(skill => {
      skillCounts[skill.id] = 0
    })

    businessUnitOptions.forEach(bu => {
      buSkillCounts[bu] = {}
      skillsData.forEach(skill => {
        buSkillCounts[bu][skill.id] = 0
      })
    })

    careerLevelOptions.forEach(cl => {
      clSkillCounts[cl] = {}
      skillsData.forEach(skill => {
        clSkillCounts[cl][skill.id] = 0
      })
    })

    // Count skills
    assessments.forEach((assessment: AssessmentDocument) => {
      const bu = assessment.businessUnit || "Unknown"
      const cl = assessment.careerLevel || "Unknown"

      if (assessment.skills && Array.isArray(assessment.skills)) {
        assessment.skills.forEach((skill: { id: string; currentLevel: number | null }) => {
          if (skill.currentLevel) {
            // Count overall
            skillCounts[skill.id]++

            // Count by business unit
            if (bu && buSkillCounts[bu]) {
              buSkillCounts[bu][skill.id]++
            }

            // Count by career level
            if (cl && clSkillCounts[cl]) {
              clSkillCounts[cl][skill.id]++
            }
          }
        })
      }
    })

    // Calculate final averages
    Object.keys(aggregateData.skillAverages).forEach(skillId => {
      if (skillCounts[skillId] > 0) {
        aggregateData.skillAverages[skillId] = parseFloat((aggregateData.skillAverages[skillId] / skillCounts[skillId]).toFixed(2))
      } else {
        aggregateData.skillAverages[skillId] = 0
      }
    })

    // Calculate business unit averages
    Object.keys(aggregateData.skillsByBusinessUnit).forEach(bu => {
      Object.keys(aggregateData.skillsByBusinessUnit[bu]).forEach(skillId => {
        if (buSkillCounts[bu] && buSkillCounts[bu][skillId] > 0) {
          aggregateData.skillsByBusinessUnit[bu][skillId] = parseFloat(
            (aggregateData.skillsByBusinessUnit[bu][skillId] / buSkillCounts[bu][skillId]).toFixed(2)
          )
        } else {
          aggregateData.skillsByBusinessUnit[bu][skillId] = 0
        }
      })
    })

    // Calculate career level averages
    Object.keys(aggregateData.skillsByCareerLevel).forEach(cl => {
      Object.keys(aggregateData.skillsByCareerLevel[cl]).forEach(skillId => {
        if (clSkillCounts[cl] && clSkillCounts[cl][skillId] > 0) {
          aggregateData.skillsByCareerLevel[cl][skillId] = parseFloat(
            (aggregateData.skillsByCareerLevel[cl][skillId] / clSkillCounts[cl][skillId]).toFixed(2)
          )
        } else {
          aggregateData.skillsByCareerLevel[cl][skillId] = 0
        }
      })
    })

    // Calculate top skills and improvement areas
    const skillAveragesArray = Object.entries(aggregateData.skillAverages).map(([id, average]) => {
      const skillInfo = skillsData.find(s => s.id === id)
      return {
        id,
        category: skillInfo?.category || id,
        average
      }
    })

    // Sort by average (descending for top skills, ascending for improvement areas)
    aggregateData.topSkills = [...skillAveragesArray].sort((a, b) => b.average - a.average).slice(0, 5)
    aggregateData.improvementAreas = [...skillAveragesArray].sort((a, b) => a.average - b.average)
      .filter(skill => skill.average > 0) // Only include skills that have been assessed
      .slice(0, 5)

    // Calculate user performance data
    // Group assessments by user
    const userAssessments: Record<string, any[]> = {}
    const userSkillAverages: Record<string, Record<string, number>> = {}
    const userSkillCounts: Record<string, Record<string, number>> = {}

    // Initialize user data structures
    userProfiles.forEach((user: any) => {
      const userId = user._id.toString()
      userAssessments[userId] = []
      userSkillAverages[userId] = {}
      userSkillCounts[userId] = {}

      skillsData.forEach(skill => {
        userSkillAverages[userId][skill.id] = 0
        userSkillCounts[userId][skill.id] = 0
      })
    })

    // Group assessments by user and calculate skill averages
    assessments.forEach((assessment: AssessmentDocument) => {
      if (!assessment.userId) return

      const userId = assessment.userId.toString()
      if (!userAssessments[userId]) {
        userAssessments[userId] = []
        userSkillAverages[userId] = {}
        userSkillCounts[userId] = {}

        skillsData.forEach(skill => {
          userSkillAverages[userId][skill.id] = 0
          userSkillCounts[userId][skill.id] = 0
        })
      }

      userAssessments[userId].push(assessment)

      // Calculate skill averages for this user
      if (assessment.skills && Array.isArray(assessment.skills)) {
        assessment.skills.forEach((skill: { id: string; currentLevel: number | null }) => {
          if (skill.currentLevel) {
            if (!userSkillAverages[userId][skill.id]) {
              userSkillAverages[userId][skill.id] = 0
              userSkillCounts[userId][skill.id] = 0
            }
            userSkillAverages[userId][skill.id] += skill.currentLevel
            userSkillCounts[userId][skill.id]++
          }
        })
      }
    })

    // Calculate final user averages and find top skill for each user
    const userSummaries: UserSummary[] = []

    userProfiles.forEach((user: any) => {
      const userId = user._id.toString()
      if (!userAssessments[userId] || userAssessments[userId].length === 0) return

      // Calculate average skill level for this user
      let totalSkillLevel = 0
      let totalSkillCount = 0
      let topSkillId = ''
      let topSkillLevel = 0

      Object.keys(userSkillAverages[userId]).forEach(skillId => {
        if (userSkillCounts[userId][skillId] > 0) {
          const avgLevel = userSkillAverages[userId][skillId] / userSkillCounts[userId][skillId]
          userSkillAverages[userId][skillId] = parseFloat(avgLevel.toFixed(2))

          totalSkillLevel += avgLevel
          totalSkillCount++

          // Track top skill
          if (avgLevel > topSkillLevel) {
            topSkillLevel = avgLevel
            topSkillId = skillId
          }
        }
      })

      const averageSkillLevel = totalSkillCount > 0 ?
        parseFloat((totalSkillLevel / totalSkillCount).toFixed(2)) : 0

      // Find skill info for top skill
      const topSkillInfo = skillsData.find(s => s.id === topSkillId)

      userSummaries.push({
        id: userId,
        name: user.name || 'Unknown',
        email: user.email || '',
        businessUnit: user.businessUnit || 'Unknown',
        careerLevel: user.careerLevel || 'Unknown',
        averageSkillLevel,
        topSkill: topSkillId ? {
          id: topSkillId,
          category: topSkillInfo?.category || topSkillId,
          level: topSkillLevel
        } : undefined
      })
    })

    // Sort users by average skill level (descending)
    aggregateData.topUsers = [...userSummaries]
      .sort((a, b) => b.averageSkillLevel - a.averageSkillLevel)
      .slice(0, 5)

    // Sort users by most recent activity (using the most recent assessment)
    aggregateData.recentlyActiveUsers = [...userSummaries]
      .sort((a, b) => {
        const aAssessments = userAssessments[a.id] || []
        const bAssessments = userAssessments[b.id] || []

        const aLatest = aAssessments.length > 0 ?
          new Date(aAssessments[0].updatedAt || aAssessments[0].createdAt).getTime() : 0
        const bLatest = bAssessments.length > 0 ?
          new Date(bAssessments[0].updatedAt || bAssessments[0].createdAt).getTime() : 0

        return bLatest - aLatest // Descending order (most recent first)
      })
      .slice(0, 5)

    return {
      success: true,
      data: aggregateData
    }
  } catch (error) {
    // Handle error
    return {
      success: false,
      message: error instanceof Error ? `Failed to fetch aggregate data: ${error.message}` : "Failed to fetch aggregate data",
      data: {
        totalAssessments: 0,
        totalUsers: 0,
        activeUsers: 0,
        inactiveUsers: 0,
        adminCreatedUsers: 0,
        selfRegisteredUsers: 0,
        skillAverages: {},
        businessUnitBreakdown: {},
        careerLevelBreakdown: {},
        skillsByBusinessUnit: {},
        skillsByCareerLevel: {},
        topSkills: [],
        improvementAreas: [],
        topUsers: [],
        recentlyActiveUsers: []
      }
    }
  }
}

/**
 * Get user profiles for admin dashboard
 * This function is restricted to admin users only
 * @deprecated Use getAllUserProfiles from admin-users.ts instead
 */
export async function getAllUserProfiles(
  adminEmail: string,
  page: number = 1,
  pageSize: number = 10,
  search: string = ""
) {
  // Import the new function to avoid code duplication
  const { getAllUserProfiles: getProfiles } = await import('./admin-users');
  return getProfiles(adminEmail, page, pageSize, search);
}

/**
 * Get assessment history for a specific user
 * This function is restricted to admin users only
 */
export async function getUserAssessmentHistory(adminEmail: string, userEmail: string) {
  try {
    // Check if the user is an admin
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: "Unauthorized: Admin access required",
        data: []
      }
    }

    const database = await getDb()
    const assessments = await database
      .collection("assessments")
      .find({ userEmail })
      .sort({ updatedAt: -1 })
      .toArray()

    return {
      success: true,
      data: assessments.map((assessment: AssessmentDocument) => ({
        id: assessment._id.toString(),
        name: assessment.name,
        skills: assessment.skills || [],
        history: assessment.history || [],
        businessUnit: assessment.businessUnit,
        careerLevel: assessment.careerLevel,
        jobRole: assessment.jobRole,
        createdAt: assessment.createdAt,
        updatedAt: assessment.updatedAt
      }))
    }
  } catch (error) {
    // Handle error
    return {
      success: false,
      message: "Failed to fetch user assessment history",
      data: []
    }
  }
}
