'use server'

import 'server-only'

import {
  getAdminEmails,
  isAdmin as checkIsAdmin,
  addAdminEmail,
  removeAdminEmail,
  getAssessmentIntervalMonths,
  setAssessmentIntervalMonths,
  getAllowAssessmentUpdates,
  setAllowAssessmentUpdates,
  initializeAdminEmails
} from '@/lib/services/settings-service';
import { createDatabaseIndexes } from '@/lib/db-indexes';

/**
 * Initialize settings from environment variables
 * This should be called during application startup
 */
export async function initializeSettings(): Promise<boolean> {
  try {
    console.log('Initializing application settings...');

    // Initialize admin emails
    const adminResult = await initializeAdminEmails();
    console.log('Admin emails initialization:', adminResult ? 'success' : 'failed');

    // Initialize database indexes (non-blocking)
    createDatabaseIndexes().then(indexResult => {
      console.log('Database indexes initialization:', indexResult ? 'success' : 'failed');
    }).catch(error => {
      console.error('Error initializing database indexes:', error);
    });

    return adminResult;
  } catch (error) {
    console.error('Error initializing settings:', error);
    return false;
  }
}

/**
 * Check if a user is an admin
 * @param email User email address
 * @returns True if the user is an admin, false otherwise
 */
export async function isAdmin(email: string): Promise<boolean> {
  if (!email) return false;
  return await checkIsAdmin(email);
}

/**
 * Get all admin emails
 * @returns Array of admin email addresses
 */
export async function getAdmins(): Promise<string[]> {
  return await getAdminEmails();
}

/**
 * Add an admin
 * @param email Email address to add
 * @returns True if successful, false otherwise
 */
export async function addAdmin(email: string): Promise<boolean> {
  if (!email) return false;
  return await addAdminEmail(email);
}

/**
 * Remove an admin
 * @param email Email address to remove
 * @returns True if successful, false otherwise
 */
export async function removeAdmin(email: string): Promise<boolean> {
  if (!email) return false;
  return await removeAdminEmail(email);
}

/**
 * Get assessment settings
 * @returns Assessment settings
 */
export async function getAssessmentSettings(): Promise<{
  intervalMonths: number;
  allowUpdates: boolean;
}> {
  const [intervalMonths, allowUpdates] = await Promise.all([
    getAssessmentIntervalMonths(),
    getAllowAssessmentUpdates()
  ]);

  return {
    intervalMonths,
    allowUpdates
  };
}

/**
 * Update assessment settings
 * @param settings Assessment settings
 * @returns True if successful, false otherwise
 */
export async function updateAssessmentSettings(settings: {
  intervalMonths?: number;
  allowUpdates?: boolean;
}): Promise<boolean> {
  const promises: Promise<boolean>[] = [];

  if (typeof settings.intervalMonths === 'number') {
    promises.push(setAssessmentIntervalMonths(settings.intervalMonths));
  }

  if (typeof settings.allowUpdates === 'boolean') {
    promises.push(setAllowAssessmentUpdates(settings.allowUpdates));
  }

  if (promises.length === 0) {
    return true;
  }

  const results = await Promise.all(promises);
  return results.every(result => result);
}
