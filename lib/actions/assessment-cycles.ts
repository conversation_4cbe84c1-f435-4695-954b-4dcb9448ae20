"use server"

import { revalidatePath } from "next/cache";
import { getDb } from "@/lib/db";
import { authConfig } from "@/lib/config";
import { ObjectId } from "mongodb";
import { broadcastMessage } from "@/lib/sse-broadcast";
import {
  AssessmentCycle,
  CycleTeamMember,
  PeerReviewer,
  serializeAssessmentCycle,
  serializeAssessment,
  Assessment,
  AssessmentStatus
} from "@/lib/models/assessment-cycle";

/**
 * Create a new assessment cycle
 * @param data Assessment cycle data
 * @param adminEmail Email of the admin creating the cycle
 */
export async function createAssessmentCycle(data: Omit<AssessmentCycle, '_id'>, adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to create assessment cycles'
      };
    }

    const database = await getDb();

    // Ensure required fields have default values
    const defaultData = {
      startDate: data.startDate || new Date(),
      endDate: data.endDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      teamMembers: data.teamMembers || [],
      projects: data.projects || [], // Include projects or empty array
      assessmentTypes: data.assessmentTypes || {
        self: true,
        manager: false,
        peer: false,
        peerReviewsPerUser: 0
      },
      // Interval settings
      isRecurring: data.isRecurring || false,
      intervalMonths: data.intervalMonths || 6, // Default to 6 months
      lastAssessmentDate: null, // Will be set when an assessment is completed
      status: 'draft' // Always ensure status is set
    };

    // Create the assessment cycle
    const result = await database.collection('assessment-cycles').insertOne({
      ...data,
      ...defaultData,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: adminEmail,
      updatedBy: adminEmail,
      status: 'draft'
    });

    // Broadcast SSE event for cycle creation
    await broadcastMessage({
      type: 'cycle_created',
      cycleId: result.insertedId.toString(),
      cycleName: data.name,
      businessUnit: data.businessUnit,
      timestamp: new Date().toISOString()
    });

    revalidatePath('/admin/assessments');
    return {
      success: true,
      message: 'Assessment cycle created successfully',
      cycleId: result.insertedId.toString()
    };
  } catch (error) {
    console.error('Error creating assessment cycle:', error);
    return {
      success: false,
      message: 'Failed to create assessment cycle'
    };
  }
}

/**
 * Get all assessment cycles with pagination and search
 * @param adminEmail Email of the admin requesting the cycles
 * @param page Page number (1-based)
 * @param pageSize Number of items per page
 * @param search Search query
 * @param status Filter by status (optional)
 */
export async function getAllAssessmentCycles(
  adminEmail: string,
  page: number = 1,
  pageSize: number = 10,
  search: string = '',
  status?: 'draft' | 'active' | 'completed'
) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to view assessment cycles',
        data: [],
        totalItems: 0,
        totalPages: 0
      };
    }

    const database = await getDb();

    // Calculate skip value for pagination
    const skip = (page - 1) * pageSize;

    // Build query filter
    let filter: any = {};

    // Add status filter if provided
    if (status) {
      filter.status = status;
    }

    // Add search filter if provided
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Get total count for pagination
    const totalItems = await database
      .collection('assessment-cycles')
      .countDocuments(filter);

    // Calculate total pages
    const totalPages = Math.ceil(totalItems / pageSize);

    // Get paginated data
    const cycles = await database
      .collection('assessment-cycles')
      .find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(pageSize)
      .toArray();

    return {
      success: true,
      data: cycles.map((cycle: any) => serializeAssessmentCycle(cycle)),
      totalItems,
      totalPages,
      currentPage: page
    };
  } catch (error) {
    console.error('Error fetching assessment cycles:', error);
    return {
      success: false,
      message: 'Failed to fetch assessment cycles',
      data: [],
      totalItems: 0,
      totalPages: 0
    };
  }
}

/**
 * Get a specific assessment cycle
 * @param cycleId ID of the assessment cycle
 * @param adminEmail Email of the admin requesting the cycle
 */
export async function getAssessmentCycle(cycleId: string, adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to view assessment cycles'
      };
    }

    const database = await getDb();

    // Get the assessment cycle
    const cycle = await database
      .collection('assessment-cycles')
      .findOne({ _id: new ObjectId(cycleId) });

    if (!cycle) {
      return {
        success: false,
        message: 'Assessment cycle not found'
      };
    }

    return {
      success: true,
      data: serializeAssessmentCycle(cycle)
    };
  } catch (error) {
    console.error(`Error fetching assessment cycle ${cycleId}:`, error);
    return {
      success: false,
      message: 'Failed to fetch assessment cycle'
    };
  }
}

/**
 * Update an assessment cycle
 * @param cycleId ID of the assessment cycle
 * @param data Updated assessment cycle data
 * @param adminEmail Email of the admin updating the cycle
 */
export async function updateAssessmentCycle(cycleId: string, data: Partial<AssessmentCycle>, adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to update assessment cycles'
      };
    }

    const database = await getDb();

    // Update the assessment cycle
    await database.collection('assessment-cycles').updateOne(
      { _id: new ObjectId(cycleId) },
      { $set: { ...data, updatedAt: new Date(), updatedBy: adminEmail } }
    );

    revalidatePath('/admin/assessments');
    revalidatePath(`/admin/assessments/cycles/${cycleId}`);

    return {
      success: true,
      message: 'Assessment cycle updated successfully'
    };
  } catch (error) {
    console.error(`Error updating assessment cycle ${cycleId}:`, error);
    return {
      success: false,
      message: 'Failed to update assessment cycle'
    };
  }
}

/**
 * Delete an assessment cycle
 * @param cycleId ID of the assessment cycle
 * @param adminEmail Email of the admin deleting the cycle
 */
export async function deleteAssessmentCycle(cycleId: string, adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to delete assessment cycles'
      };
    }

    const database = await getDb();

    // Delete the assessment cycle
    await database.collection('assessment-cycles').deleteOne({ _id: new ObjectId(cycleId) });

    // Delete all assessments associated with this cycle
    await database.collection('assessments').deleteMany({ cycleId });

    revalidatePath('/admin/assessments');

    return {
      success: true,
      message: 'Assessment cycle deleted successfully'
    };
  } catch (error) {
    console.error(`Error deleting assessment cycle ${cycleId}:`, error);
    return {
      success: false,
      message: 'Failed to delete assessment cycle'
    };
  }
}

/**
 * Assign team members to an assessment cycle
 * @param cycleId ID of the assessment cycle
 * @param teamMembers Array of team members to assign
 * @param adminEmail Email of the admin assigning team members
 */
export async function assignTeamMembers(
  cycleId: string,
  teamMembers: CycleTeamMember[],
  adminEmail: string
) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to assign team members'
      };
    }

    const database = await getDb();

    // Update the assessment cycle with the team members
    await database.collection('assessment-cycles').updateOne(
      { _id: new ObjectId(cycleId) },
      {
        $set: {
          teamMembers,
          updatedAt: new Date(),
          updatedBy: adminEmail
        }
      }
    );

    revalidatePath('/admin/assessments');
    revalidatePath(`/admin/assessments/cycles/${cycleId}`);

    return {
      success: true,
      message: 'Team members assigned successfully'
    };
  } catch (error) {
    console.error(`Error assigning team members to cycle ${cycleId}:`, error);
    return {
      success: false,
      message: 'Failed to assign team members'
    };
  }
}

/**
 * Assign managers to team members in an assessment cycle
 * @param cycleId ID of the assessment cycle
 * @param managerAssignments Object mapping team member IDs to manager data
 * @param adminEmail Email of the admin assigning managers
 */
export async function assignManagers(
  cycleId: string,
  managerAssignments: Record<string, { userId: string; name: string; email: string }>,
  adminEmail: string
) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to assign managers'
      };
    }

    const database = await getDb();

    // Get the current cycle
    const cycle = await database
      .collection('assessment-cycles')
      .findOne({ _id: new ObjectId(cycleId) });

    if (!cycle) {
      return {
        success: false,
        message: 'Assessment cycle not found'
      };
    }

    // Update each team member with their assigned manager
    const updatedTeamMembers = cycle.teamMembers.map((member: CycleTeamMember) => {
      if (managerAssignments[member.userId]) {
        return {
          ...member,
          assessments: {
            ...member.assessments,
            manager: {
              ...managerAssignments[member.userId],
              status: 'pending' as AssessmentStatus
            }
          }
        };
      }
      return member;
    });

    // Update the assessment cycle
    await database.collection('assessment-cycles').updateOne(
      { _id: new ObjectId(cycleId) },
      {
        $set: {
          teamMembers: updatedTeamMembers,
          updatedAt: new Date(),
          updatedBy: adminEmail
        }
      }
    );

    revalidatePath('/admin/assessments');
    revalidatePath(`/admin/assessments/cycles/${cycleId}`);

    return {
      success: true,
      message: 'Managers assigned successfully'
    };
  } catch (error) {
    console.error(`Error assigning managers to cycle ${cycleId}:`, error);
    return {
      success: false,
      message: 'Failed to assign managers'
    };
  }
}

/**
 * Assign peer reviewers to team members in an assessment cycle
 * @param cycleId ID of the assessment cycle
 * @param peerAssignments Object mapping team member IDs to arrays of peer reviewer data
 * @param adminEmail Email of the admin assigning peer reviewers
 */
export async function assignPeerReviewers(
  cycleId: string,
  peerAssignments: Record<string, PeerReviewer[]>,
  adminEmail: string
) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to assign peer reviewers'
      };
    }

    const database = await getDb();

    // Get the current cycle
    const cycle = await database
      .collection('assessment-cycles')
      .findOne({ _id: new ObjectId(cycleId) });

    if (!cycle) {
      return {
        success: false,
        message: 'Assessment cycle not found'
      };
    }

    // Update each team member with their assigned peer reviewers
    const updatedTeamMembers = cycle.teamMembers.map((member: CycleTeamMember) => {
      if (peerAssignments[member.userId]) {
        return {
          ...member,
          assessments: {
            ...member.assessments,
            peers: peerAssignments[member.userId]
          }
        };
      }
      return member;
    });

    // Update the assessment cycle
    await database.collection('assessment-cycles').updateOne(
      { _id: new ObjectId(cycleId) },
      {
        $set: {
          teamMembers: updatedTeamMembers,
          updatedAt: new Date(),
          updatedBy: adminEmail
        }
      }
    );

    revalidatePath('/admin/assessments');
    revalidatePath(`/admin/assessments/cycles/${cycleId}`);

    return {
      success: true,
      message: 'Peer reviewers assigned successfully'
    };
  } catch (error) {
    console.error(`Error assigning peer reviewers to cycle ${cycleId}:`, error);
    return {
      success: false,
      message: 'Failed to assign peer reviewers'
    };
  }
}

/**
 * Activate an assessment cycle and create all necessary assessments
 * @param cycleId ID of the assessment cycle
 * @param adminEmail Email of the admin activating the cycle
 */
export async function activateAssessmentCycle(cycleId: string, adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to activate assessment cycles'
      };
    }

    const database = await getDb();

    // Get the current cycle
    const cycle = await database
      .collection('assessment-cycles')
      .findOne({ _id: new ObjectId(cycleId) });

    if (!cycle) {
      return {
        success: false,
        message: 'Assessment cycle not found'
      };
    }

    // Check if the cycle is already active
    if (cycle.status === 'active') {
      return {
        success: false,
        message: 'Assessment cycle is already active'
      };
    }

    // Create assessments for each team member
    const assessments: Omit<Assessment, '_id'>[] = [];
    const now = new Date();
    const dateStr = now.toISOString();

    // If there are no team members, add the current admin as a team member
    if (!cycle.teamMembers || cycle.teamMembers.length === 0) {
      console.log('No team members found, adding current admin as a team member');

      // Create a default team member with the admin's email
      cycle.teamMembers = [{
        userId: adminEmail,
        email: adminEmail,
        name: adminEmail.split('@')[0], // Use part before @ as name
        businessUnit: 'Default',
        careerLevel: 'Default',
        assessments: {
          self: 'pending',
          manager: { userId: '', email: '', name: '', status: 'pending' },
          peers: []
        }
      }];

      // Update the cycle with the new team member
      await database.collection('assessment-cycles').updateOne(
        { _id: new ObjectId(cycleId) },
        {
          $set: {
            teamMembers: cycle.teamMembers,
            updatedAt: now
          }
        }
      );
    }

    cycle.teamMembers.forEach((member: any) => {
      // Self assessment
      if (cycle.assessmentTypes.self) {
        assessments.push({
          cycleId: cycleId,
          userId: member.userId,
          reviewerId: member.userId, // Self assessment
          relationshipType: 'self',
          name: `${cycle.name} - Self Assessment`,
          date: dateStr,
          skills: [],
          projectSkills: [],
          status: 'pending',
          createdAt: now,
          updatedAt: now
        });
      }

      // Manager assessment
      if (cycle.assessmentTypes.manager && member.assessments.manager) {
        assessments.push({
          cycleId: cycleId,
          userId: member.userId,
          reviewerId: member.assessments.manager.userId,
          relationshipType: 'manager',
          name: `${cycle.name} - Manager Assessment for ${member.name}`,
          date: dateStr,
          skills: [],
          projectSkills: [],
          status: 'pending',
          createdAt: now,
          updatedAt: now
        });
      }

      // Peer assessments
      if (cycle.assessmentTypes.peer && member.assessments.peers) {
        member.assessments.peers.forEach((peer: any) => {
          assessments.push({
            cycleId: cycleId,
            userId: member.userId,
            reviewerId: peer.userId,
            relationshipType: 'peer',
            name: `${cycle.name} - Peer Assessment for ${member.name}`,
            date: dateStr,
            skills: [],
            projectSkills: [],
            status: 'pending',
            createdAt: now,
            updatedAt: now
          });
        });
      }
    });

    // Insert all assessments
    if (assessments.length > 0) {
      await database.collection('assessments').insertMany(assessments);
    }

    // Update the cycle status to active
    await database.collection('assessment-cycles').updateOne(
      { _id: new ObjectId(cycleId) },
      {
        $set: {
          status: 'active',
          updatedAt: now,
          updatedBy: adminEmail
        }
      }
    );

    // Broadcast SSE event for cycle activation
    await broadcastMessage({
      type: 'cycle_activated',
      cycleId: cycleId,
      cycleName: cycle.name,
      businessUnit: cycle.businessUnit,
      timestamp: new Date().toISOString()
    });

    revalidatePath('/admin/assessments');
    revalidatePath(`/admin/assessments/cycles/${cycleId}`);

    return {
      success: true,
      message: 'Assessment cycle activated successfully',
      assessmentsCreated: assessments.length
    };
  } catch (error) {
    console.error(`Error activating assessment cycle ${cycleId}:`, error);
    return {
      success: false,
      message: 'Failed to activate assessment cycle'
    };
  }
}

/**
 * Complete an assessment cycle
 * @param cycleId ID of the assessment cycle
 * @param adminEmail Email of the admin completing the cycle
 */
export async function completeAssessmentCycle(cycleId: string, adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to complete assessment cycles'
      };
    }

    const database = await getDb();

    // Update the cycle status to completed
    await database.collection('assessment-cycles').updateOne(
      { _id: new ObjectId(cycleId) },
      {
        $set: {
          status: 'completed',
          updatedAt: new Date(),
          updatedBy: adminEmail
        }
      }
    );

    // Mark all pending assessments as completed
    await database.collection('assessments').updateMany(
      {
        cycleId: cycleId,
        status: 'pending'
      },
      {
        $set: {
          status: 'completed',
          updatedAt: new Date()
        }
      }
    );

    revalidatePath('/admin/assessments');
    revalidatePath(`/admin/assessments/cycles/${cycleId}`);

    return {
      success: true,
      message: 'Assessment cycle completed successfully'
    };
  } catch (error) {
    console.error(`Error completing assessment cycle ${cycleId}:`, error);
    return {
      success: false,
      message: 'Failed to complete assessment cycle'
    };
  }
}

/**
 * Get assessments for a user
 * @param userId ID of the user
 * @param userEmail Email of the user
 */
export async function getUserAssessments(userId: string, userEmail: string) {
  try {
    const database = await getDb();

    // Get all assessments where the user is the reviewer
    const assessments = await database
      .collection('assessments')
      .find({ reviewerId: userId })
      .toArray();

    // Get all assessment cycles and auto-fix missing status fields
    const allCycles = await database
      .collection('assessment-cycles')
      .find({})
      .toArray();

    // Auto-fix cycles without status field (permanent migration)
    const cyclesWithoutStatus = allCycles.filter((cycle: any) => !cycle.status);
    if (cyclesWithoutStatus.length > 0) {
      console.log(`Auto-fixing ${cyclesWithoutStatus.length} cycles without status field...`);

      await database.collection('assessment-cycles').updateMany(
        { status: { $exists: false } },
        {
          $set: {
            status: 'active',
            updatedAt: new Date()
          }
        }
      );

      // Broadcast SSE event for auto-fixed cycles
      for (const cycle of cyclesWithoutStatus) {
        await broadcastMessage({
          type: 'cycle_activated',
          cycleId: cycle._id.toString(),
          cycleName: cycle.name,
          businessUnit: cycle.businessUnit,
          timestamp: new Date().toISOString()
        });
      }
    }

    // Now get all active assessment cycles
    const cycles = await database
      .collection('assessment-cycles')
      .find({
        status: 'active'
      })
      .toArray();

    // Check for completed self-assessments to update lastAssessmentDate
    const selfAssessments = assessments.filter((a: any) =>
      a.relationshipType === 'self' && a.status === 'completed'
    );

    // If there are completed self-assessments, update the lastAssessmentDate for recurring cycles
    if (selfAssessments.length > 0) {
      // Sort by date, newest first
      selfAssessments.sort((a: any, b: any) =>
        new Date(b.updatedAt || b.createdAt).getTime() -
        new Date(a.updatedAt || a.createdAt).getTime()
      );

      // Get the most recent assessment
      const latestAssessment: any = selfAssessments[0];
      const assessmentDate = new Date(latestAssessment.updatedAt || latestAssessment.createdAt);

      // Update lastAssessmentDate for recurring cycles
      for (const cycle of cycles) {
        if (cycle.isRecurring) {
          // Update the lastAssessmentDate for this user in this cycle
          await database.collection('assessment-cycles').updateOne(
            { _id: cycle._id },
            {
              $set: {
                lastAssessmentDate: assessmentDate
              }
            }
          );
        }
      }
    }

    return {
      success: true,
      data: {
        assessments: assessments.map((assessment: any) => serializeAssessment(assessment)),
        cycles: cycles.map((cycle: any) => serializeAssessmentCycle(cycle))
      }
    };
  } catch (error) {
    console.error(`Error fetching assessments for user ${userId}:`, error);
    return {
      success: false,
      message: 'Failed to fetch assessments',
      data: { assessments: [], cycles: [] }
    };
  }
}

/**
 * Get assessment details
 * @param assessmentId ID of the assessment
 * @param userEmail Email of the user requesting the assessment
 */
export async function getAssessmentDetails(assessmentId: string, userEmail: string) {
  try {
    const database = await getDb();

    // Get the assessment
    const assessment = await database
      .collection('assessments')
      .findOne({ _id: new ObjectId(assessmentId) });

    if (!assessment) {
      return {
        success: false,
        message: 'Assessment not found'
      };
    }

    // Check if the user is authorized to view this assessment
    const isAdmin = authConfig.isAdmin(userEmail);
    const isReviewer = assessment.reviewerId === userEmail;
    const isAssessee = assessment.userId === userEmail;

    if (!isAdmin && !isReviewer && !isAssessee) {
      return {
        success: false,
        message: 'You do not have permission to view this assessment'
      };
    }

    // Get the assessment cycle
    const cycle = await database
      .collection('assessment-cycles')
      .findOne({ _id: new ObjectId(assessment.cycleId) });

    // Get the user being assessed
    const userProfile = await database
      .collection('user_profiles')
      .findOne({ email: assessment.userId });

    return {
      success: true,
      data: {
        assessment: serializeAssessment(assessment),
        cycle: cycle ? serializeAssessmentCycle(cycle) : null,
        userProfile
      }
    };
  } catch (error) {
    console.error(`Error fetching assessment ${assessmentId}:`, error);
    return {
      success: false,
      message: 'Failed to fetch assessment details'
    };
  }
}

/**
 * Update an assessment
 * @param assessmentId ID of the assessment
 * @param data Updated assessment data
 * @param userEmail Email of the user updating the assessment
 */
export async function updateAssessment(
  assessmentId: string,
  data: Partial<Assessment>,
  userEmail: string
) {
  try {
    const database = await getDb();

    // Get the assessment
    const assessment = await database
      .collection('assessments')
      .findOne({ _id: new ObjectId(assessmentId) });

    if (!assessment) {
      return {
        success: false,
        message: 'Assessment not found'
      };
    }

    // Check if the user is authorized to update this assessment
    const isAdmin = authConfig.isAdmin(userEmail);
    const isReviewer = assessment.reviewerId === userEmail;

    if (!isAdmin && !isReviewer) {
      return {
        success: false,
        message: 'You do not have permission to update this assessment'
      };
    }

    // Update the assessment
    await database.collection('assessments').updateOne(
      { _id: new ObjectId(assessmentId) },
      {
        $set: {
          ...data,
          updatedAt: new Date()
        }
      }
    );

    // If the assessment status is being updated to 'completed', update the cycle
    if (data.status === 'completed') {
      // Get the cycle
      const cycle = await database
        .collection('assessment-cycles')
        .findOne({ _id: new ObjectId(assessment.cycleId) });

      if (cycle) {
        // Update the team member's assessment status
        const updatedTeamMembers = cycle.teamMembers.map((member: CycleTeamMember) => {
          if (member.userId === assessment.userId) {
            const updatedAssessments = { ...member.assessments };

            if (assessment.relationshipType === 'self') {
              updatedAssessments.self = 'completed';
            } else if (assessment.relationshipType === 'manager') {
              updatedAssessments.manager.status = 'completed';
            } else if (assessment.relationshipType === 'peer') {
              updatedAssessments.peers = updatedAssessments.peers.map(peer => {
                if (peer.userId === assessment.reviewerId) {
                  return { ...peer, status: 'completed' };
                }
                return peer;
              });
            }

            return {
              ...member,
              assessments: updatedAssessments
            };
          }
          return member;
        });

        // Update the cycle
        await database.collection('assessment-cycles').updateOne(
          { _id: new ObjectId(assessment.cycleId) },
          {
            $set: {
              teamMembers: updatedTeamMembers,
              updatedAt: new Date(),
              updatedBy: userEmail
            }
          }
        );
      }
    }

    revalidatePath('/admin/assessments');
    revalidatePath(`/admin/assessments/cycles/${assessment.cycleId}`);
    revalidatePath(`/assessment/${assessmentId}`);

    return {
      success: true,
      message: 'Assessment updated successfully'
    };
  } catch (error) {
    console.error(`Error updating assessment ${assessmentId}:`, error);
    return {
      success: false,
      message: 'Failed to update assessment'
    };
  }
}

/**
 * Get comparison data for a user's assessments in a cycle
 * @param cycleId ID of the assessment cycle
 * @param userId ID of the user
 * @param adminEmail Email of the admin requesting the comparison
 */
export async function getAssessmentComparison(cycleId: string, userId: string, adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: 'You do not have permission to view assessment comparisons'
      };
    }

    const database = await getDb();

    // Get all assessments for this user in this cycle
    const assessments = await database
      .collection('assessments')
      .find({
        cycleId,
        userId,
        status: 'completed'
      })
      .toArray();

    if (assessments.length === 0) {
      return {
        success: false,
        message: 'No completed assessments found for this user in this cycle'
      };
    }

    // Organize assessments by type
    const selfAssessment = assessments.find((a: any) => a.relationshipType === 'self');
    const managerAssessment = assessments.find((a: any) => a.relationshipType === 'manager');
    const peerAssessments = assessments.filter((a: any) => a.relationshipType === 'peer');

    // Get the user profile
    const userProfile = await database
      .collection('user_profiles')
      .findOne({ email: userId });

    // Get the cycle
    const cycle = await database
      .collection('assessment-cycles')
      .findOne({ _id: new ObjectId(cycleId) });

    // Prepare comparison data
    const comparisonData = {
      user: userProfile,
      cycle: cycle ? serializeAssessmentCycle(cycle) : null,
      self: selfAssessment ? serializeAssessment(selfAssessment) : null,
      manager: managerAssessment ? serializeAssessment(managerAssessment) : null,
      peers: peerAssessments.map((assessment: any) => serializeAssessment(assessment)),
      skillComparison: prepareSkillComparison(selfAssessment, managerAssessment, peerAssessments)
    };

    return {
      success: true,
      data: comparisonData
    };
  } catch (error) {
    console.error(`Error fetching assessment comparison for user ${userId} in cycle ${cycleId}:`, error);
    return {
      success: false,
      message: 'Failed to fetch assessment comparison'
    };
  }
}

/**
 * Helper function to prepare skill comparison data
 */
function prepareSkillComparison(selfAssessment: any, managerAssessment: any, peerAssessments: any[]) {
  // Get all unique skill IDs
  const skillIds = new Set<string>();

  if (selfAssessment?.skills) {
    selfAssessment.skills.forEach((skill: any) => skillIds.add(skill.id));
  }

  if (managerAssessment?.skills) {
    managerAssessment.skills.forEach((skill: any) => skillIds.add(skill.id));
  }

  peerAssessments.forEach(assessment => {
    if (assessment.skills) {
      assessment.skills.forEach((skill: any) => skillIds.add(skill.id));
    }
  });

  // Prepare comparison data for each skill
  const comparison: any[] = [];

  skillIds.forEach(skillId => {
    const selfSkill = selfAssessment?.skills?.find((s: any) => s.id === skillId);
    const managerSkill = managerAssessment?.skills?.find((s: any) => s.id === skillId);

    const peerSkills = peerAssessments
      .map(assessment => assessment.skills?.find((s: any) => s.id === skillId))
      .filter(Boolean);

    const peerAvg = peerSkills.length > 0
      ? peerSkills.reduce((sum, skill) => sum + (skill.currentLevel || 0), 0) / peerSkills.length
      : null;

    // Calculate gap between self and manager assessment
    const gap = selfSkill?.currentLevel && managerSkill?.currentLevel
      ? selfSkill.currentLevel - managerSkill.currentLevel
      : null;

    comparison.push({
      id: skillId,
      category: selfSkill?.category || managerSkill?.category || peerSkills[0]?.category || skillId,
      description: selfSkill?.description || managerSkill?.description || peerSkills[0]?.description || '',
      target: selfSkill?.targetCL3 || managerSkill?.targetCL3 || peerSkills[0]?.targetCL3 || 3,
      self: selfSkill?.currentLevel || null,
      manager: managerSkill?.currentLevel || null,
      peerAvg: peerAvg,
      gap: gap
    });
  });

  return comparison;
}

/**
 * Get the active assessment cycle
 * @returns The active assessment cycle, if any
 */
export async function getActiveAssessmentCycle() {
  try {
    const database = await getDb();

    // Get the active assessment cycle
    const cycle = await database
      .collection('assessment-cycles')
      .findOne({ status: 'active' });

    if (!cycle) {
      return {
        success: false,
        message: 'No active assessment cycle found'
      };
    }

    return {
      success: true,
      data: serializeAssessmentCycle(cycle)
    };
  } catch (error) {
    console.error('Error fetching active assessment cycle:', error);
    return {
      success: false,
      message: 'Failed to fetch active assessment cycle'
    };
  }
}
