"use server";

import { revalidatePath } from "next/cache";
import { db, getDb } from "@/lib/db";
import { authConfig } from "@/lib/config";
import { ObjectId } from "mongodb";
import { serializeUserProfile } from "@/lib/models/user-profile";
import { queryCache } from "@/lib/cache";

/**
 * Get all user profiles with pagination and search
 * @param adminEmail Email of the admin requesting the profiles
 * @param page Page number (1-based)
 * @param pageSize Number of items per page
 * @param search Search query
 */
export async function getAllUserProfiles(
  adminEmail: string,
  page: number = 1,
  pageSize: number = 10,
  search: string = ""
) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: "You do not have permission to view user profiles",
        data: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: page,
      };
    }

    // Calculate skip value for pagination
    const skip = (page - 1) * pageSize;

    // Build query filter
    let filter = {};
    if (search) {
      // Log the search term for debugging
      console.log(`🔍 Server-side search for: "${search}"`);

      // Search in name, email, business unit, career level, job role, and manager email
      filter = {
        $or: [
          { name: { $regex: search, $options: "i" } },
          { email: { $regex: search, $options: "i" } },
          { businessUnit: { $regex: search, $options: "i" } },
          { careerLevel: { $regex: search, $options: "i" } },
          { jobRole: { $regex: search, $options: "i" } },
          { managerEmail: { $regex: search, $options: "i" } },
        ],
      };
    }

    // Get total count for pagination
    const totalItems = await db
      .collection("user_profiles")
      .countDocuments(filter);

    // Calculate total pages
    const totalPages = Math.ceil(totalItems / pageSize);

    // Get paginated data
    const database = await getDb();
    const cursor = database.collection("user_profiles").find(filter);
    cursor.sort({ name: 1 });
    cursor.skip(skip);
    cursor.limit(pageSize);
    const profiles = await cursor.toArray();

    // Serialize profiles
    const serializedProfiles = profiles.map((profile: any) => serializeUserProfile(profile));

    return {
      success: true,
      data: serializedProfiles,
      totalItems,
      totalPages,
      currentPage: page,
    };
  } catch (error) {
    console.error("Error fetching user profiles:", error);
    return {
      success: false,
      message: "Failed to fetch user profiles",
      data: [],
      totalItems: 0,
      totalPages: 0,
      currentPage: page,
    };
  }
}

/**
 * Get a specific user profile
 * @param userId ID of the user profile
 * @param adminEmail Email of the admin requesting the profile
 */
export async function getUserProfileById(userId: string, adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: "You do not have permission to view user profiles",
      };
    }

    const profile = await db
      .collection("user_profiles")
      .findOne({ _id: new ObjectId(userId) });

    if (!profile) {
      return {
        success: false,
        message: "User profile not found",
      };
    }

    return {
      success: true,
      data: serializeUserProfile(profile),
    };
  } catch (error) {
    console.error(`Error fetching user profile ${userId}:`, error);
    return {
      success: false,
      message: "Failed to fetch user profile",
    };
  }
}

/**
 * Create or update a user profile
 * @param userData User profile data
 * @param adminEmail Email of the admin creating/updating the user
 */
export async function saveUserProfileByAdmin(
  userData: any,
  adminEmail: string
) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: "You do not have permission to manage users",
      };
    }

    // Validate required fields
    if (!userData.email || !userData.name) {
      return {
        success: false,
        message: "Email and name are required",
      };
    }

    // Check if user already exists
    const existingUser = await db
      .collection("user_profiles")
      .findOne({ email: userData.email });

    if (existingUser) {
      // Update existing user
      await db.collection("user_profiles").updateOne(
        { email: userData.email },
        {
          $set: {
            name: userData.name,
            businessUnit:
              userData.businessUnit === "none"
                ? ""
                : userData.businessUnit ?? "",
            careerLevel:
              userData.careerLevel === "none" ? "" : userData.careerLevel ?? "",
            jobRole: userData.jobRole ?? existingUser.jobRole ?? "",
            managerEmail:
              userData.managerEmail ?? existingUser.managerEmail ?? "",
            profileCompleted:
              !!(userData.businessUnit && userData.businessUnit !== "none") &&
              !!(userData.careerLevel && userData.careerLevel !== "none") &&
              !!userData.jobRole,
            updatedAt: new Date(),
            updatedBy: adminEmail,
          },
        }
      );
    } else {
      // Create new user
      await db.collection("user_profiles").insertOne({
        email: userData.email,
        name: userData.name,
        businessUnit:
          userData.businessUnit === "none" ? "" : userData.businessUnit ?? "",
        careerLevel:
          userData.careerLevel === "none" ? "" : userData.careerLevel ?? "",
        jobRole: userData.jobRole ?? "",
        managerEmail: userData.managerEmail ?? "",
        profileCompleted:
          !!(userData.businessUnit && userData.businessUnit !== "none") &&
          !!(userData.careerLevel && userData.careerLevel !== "none") &&
          !!userData.jobRole,
        hasLoggedIn: false,
        createdBy: "admin",
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    // Invalidate the cache for this user profile
    queryCache.delete(`user_profile:${userData.email}`);

    revalidatePath("/admin/users");

    return {
      success: true,
      message: existingUser
        ? "User updated successfully"
        : "User created successfully",
    };
  } catch (error) {
    console.error("Error saving user profile:", error);
    return {
      success: false,
      message: "Failed to save user profile",
    };
  }
}

/**
 * Delete a user profile
 * @param userId ID of the user to delete
 * @param adminEmail Email of the admin deleting the user
 */
export async function deleteUserProfile(userId: string, adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: "You do not have permission to delete users",
      };
    }

    // Get the user profile first to invalidate cache
    const userProfile = await db
      .collection("user_profiles")
      .findOne({ _id: new ObjectId(userId) });

    if (!userProfile) {
      return {
        success: false,
        message: "User not found",
      };
    }

    // Delete the user profile
    await db
      .collection("user_profiles")
      .deleteOne({ _id: new ObjectId(userId) });

    // Invalidate the cache for this user profile
    if (userProfile.email) {
      queryCache.delete(`user_profile:${userProfile.email}`);
    }

    revalidatePath("/admin/users");

    return {
      success: true,
      message: "User deleted successfully",
    };
  } catch (error) {
    console.error(`Error deleting user ${userId}:`, error);
    return {
      success: false,
      message: "Failed to delete user",
    };
  }
}

/**
 * Import users manually
 * @param users Array of user objects
 * @param adminEmail Email of the admin importing users
 */
export async function importUsersManually(users: any[], adminEmail: string) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: "You do not have permission to import users",
      };
    }

    if (!Array.isArray(users)) {
      return {
        success: false,
        message: "Invalid input format. Expected an array of users.",
      };
    }

    let imported = 0;
    let updated = 0;
    let errors = 0;

    for (const user of users) {
      try {
        // Validate required fields
        if (!user.email || !user.name) {
          errors++;
          continue;
        }

        // Check if user already exists
        const existingUser = await db
          .collection("user_profiles")
          .findOne({ email: user.email });

        if (existingUser) {
          // Update existing user
          await db.collection("user_profiles").updateOne(
            { email: user.email },
            {
              $set: {
                name: user.name,
                businessUnit:
                  user.businessUnit === "none"
                    ? ""
                    : user.businessUnit ?? existingUser.businessUnit ?? "",
                careerLevel:
                  user.careerLevel === "none"
                    ? ""
                    : user.careerLevel ?? existingUser.careerLevel ?? "",
                jobRole: user.jobRole ?? existingUser.jobRole ?? "",
                managerEmail:
                  user.managerEmail ?? existingUser.managerEmail ?? "",
                profileCompleted:
                  !!(user.businessUnit && user.businessUnit !== "none") &&
                  !!(user.careerLevel && user.careerLevel !== "none") &&
                  !!user.jobRole,
                updatedAt: new Date(),
                updatedBy: adminEmail,
              },
            }
          );

          // Invalidate the cache for this user profile
          queryCache.delete(`user_profile:${user.email}`);

          updated++;
        } else {
          // Create new user
          await db.collection("user_profiles").insertOne({
            email: user.email,
            name: user.name,
            businessUnit:
              user.businessUnit === "none" ? "" : user.businessUnit ?? "",
            careerLevel:
              user.careerLevel === "none" ? "" : user.careerLevel ?? "",
            jobRole: user.jobRole ?? "",
            managerEmail: user.managerEmail ?? "",
            profileCompleted:
              !!(user.businessUnit && user.businessUnit !== "none") &&
              !!(user.careerLevel && user.careerLevel !== "none") &&
              !!user.jobRole,
            hasLoggedIn: false,
            createdBy: "admin",
            createdAt: new Date(),
            updatedAt: new Date(),
          });
          imported++;
        }
      } catch (err) {
        errors++;
      }
    }

    revalidatePath("/admin/users");

    return {
      success: true,
      message: `Imported ${imported} new users, updated ${updated} existing users. ${errors} errors.`,
    };
  } catch (error) {
    console.error("Error importing users manually:", error);
    return {
      success: false,
      message: "Failed to import users",
    };
  }
}

/**
 * Import users from a file (CSV or JSON)
 * @param fileContent The content of the file as string
 * @param fileType The type of file ('csv' or 'json')
 * @param adminEmail Email of the admin importing users
 */
export async function importUsersFromFile(
  fileContent: string,
  fileType: "csv" | "json",
  adminEmail: string
) {
  try {
    // Verify admin permissions
    if (!authConfig.isAdmin(adminEmail)) {
      return {
        success: false,
        message: "You do not have permission to import users",
      };
    }

    let users: any[] = [];

    // Parse the file content based on file type
    if (fileType === "json") {
      try {
        users = JSON.parse(fileContent);
        if (!Array.isArray(users)) {
          return {
            success: false,
            message: "Invalid JSON format. Expected an array of users.",
          };
        }
      } catch (error) {
        return {
          success: false,
          message: "Invalid JSON format. Please check your file.",
        };
      }
    } else if (fileType === "csv") {
      try {
        // Parse CSV content
        const lines = fileContent.split("\n");

        // Extract headers from the first line
        const headers = lines[0].split(",").map((header) => header.trim());

        // Validate required headers
        if (!headers.includes("email") || !headers.includes("name")) {
          return {
            success: false,
            message: 'CSV file must include "email" and "name" columns',
          };
        }

        // Process each line (skip the header)
        for (let i = 1; i < lines.length; i++) {
          const line = lines[i].trim();
          if (!line) continue; // Skip empty lines

          const values = line.split(",").map((value) => value.trim());

          // Create user object from CSV line
          const user: any = {};
          headers.forEach((header, index) => {
            if (values[index] !== undefined) {
              user[header] = values[index];
            }
          });

          users.push(user);
        }
      } catch (error) {
        return {
          success: false,
          message: "Invalid CSV format. Please check your file.",
        };
      }
    } else {
      return {
        success: false,
        message: "Unsupported file type. Please use CSV or JSON.",
      };
    }

    // Now that we have the users array, use the existing import function
    return await importUsersManually(users, adminEmail);
  } catch (error) {
    console.error("Error importing users from file:", error);
    return {
      success: false,
      message: "Failed to import users",
    };
  }
}
