"use server"

import { db } from "@/lib/db"
import type { UserProfile } from "@/lib/models/user-profile"
import { revalidatePath } from "next/cache"
import { queryCache } from "@/lib/cache"

export async function getUserProfile(email: string) {
  try {
    // Use cache with 5-minute TTL for user profiles
    const profile = await queryCache.getOrSet(
      `user_profile:${email}`,
      async () => await db.collection("user_profiles").findOne({ email }),
      5 * 60 * 1000 // 5 minutes
    )
    return { success: true, data: profile }
  } catch (error) {
    console.error("Error fetching user profile:", error)
    return { success: false, message: "Failed to fetch user profile" }
  }
}

export async function saveUserProfile(profile: Partial<UserProfile>) {
  try {
    const { email } = profile

    if (!email) {
      return { success: false, message: "Email is required" }
    }

    const existingProfile = await db.collection("user_profiles").findOne({ email })

    if (existingProfile) {
      // Update existing profile
      await db.collection("user_profiles").updateOne(
        { email },
        {
          $set: {
            ...profile,
            profileCompleted: true,
            hasLoggedIn: true,
            lastLogin: new Date(),
            updatedAt: new Date(),
          },
        },
      )
    } else {
      // Create new profile
      await db.collection("user_profiles").insertOne({
        ...profile,
        profileCompleted: true,
        hasLoggedIn: true,
        lastLogin: new Date(),
        createdBy: 'self',
        createdAt: new Date(),
        updatedAt: new Date(),
      })
    }

    // Invalidate the cache for this user profile
    queryCache.delete(`user_profile:${email}`)

    revalidatePath("/")
    return { success: true, message: "Profile saved successfully" }
  } catch (error) {
    console.error("Error saving user profile:", error)
    return { success: false, message: "Failed to save user profile" }
  }
}
