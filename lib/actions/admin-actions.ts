'use server'

import { isAdmin as checkIsAdmin, getAdminEmails, addAdminEmail, removeAdminEmail } from '@/lib/services/config-service';

/**
 * Check if a user is an admin
 * @param email User email address
 * @returns True if the user is an admin, false otherwise
 */
export async function isAdmin(email: string): Promise<boolean> {
  if (!email) return false;
  return await checkIsAdmin(email);
}

/**
 * Get all admin emails
 * @returns Array of admin email addresses
 */
export async function getAdmins(): Promise<string[]> {
  return await getAdminEmails();
}

/**
 * Add an admin
 * @param email Email address to add
 * @returns True if successful, false otherwise
 */
export async function addAdmin(email: string): Promise<boolean> {
  if (!email) return false;
  return await addAdminEmail(email);
}

/**
 * Remove an admin
 * @param email Email address to remove
 * @returns True if successful, false otherwise
 */
export async function removeAdmin(email: string): Promise<boolean> {
  if (!email) return false;
  return await removeAdminEmail(email);
}
