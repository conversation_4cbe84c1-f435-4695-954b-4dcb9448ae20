export interface Skill {
  id: string
  category: string
  description: string
  targetCL2: number
  targetCL3: number
  targetCL4: number
  targetCL5: number
  targetCL6: number
  currentLevel: number | null
}

// Rating scale description
export const ratingScale = {
  1: "Basic",
  2: "Beginner",
  3: "Intermediate",
  4: "Advanced",
  5: "Expert",
  6: "Mastery"
}

export const skillsData: Skill[] = [
  {
    id: "frontend",
    category: "Front-End Development",
    description: "HTML, CSS, JS Fundamentals, Framework Basics, Responsive Design, Perf. Opt. Basics",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
  },
  {
    id: "backend",
    category: "Back-End Development",
    description: "Server-side Scripting Basics, DB Concepts, API Basics, Perf. Opt. Basics",
    targetCL2: 1,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
  },
  {
    id: "databases",
    category: "Databases",
    description: "Basic DB Concepts, Management (as needed for Back-End)",
    targetCL2: 1,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
  },
  {
    id: "cloud",
    category: "Cloud Computing",
    description: "Basic Awareness of Concepts/Platforms (relevant to web hosting/deployment)",
    targetCL2: 1,
    targetCL3: 1,
    targetCL4: 2,
    targetCL5: 2,
    targetCL6: 3,
    currentLevel: null,
  },
  {
    id: "devops",
    category: "DevOps",
    description: "Version Control (Git), Basic CI/CD Awareness, SDLC, Code Review Principles",
    targetCL2: 1,
    targetCL3: 2,
    targetCL4: 3,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
  },
  {
    id: "security",
    category: "Security",
    description: "Secure Coding Basics, Vulnerability Awareness, OWASP Top 10 Basics",
    targetCL2: 1,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 5,
    currentLevel: null,
  },
  {
    id: "api",
    category: "API Design & Development",
    description: "Understanding REST Basics, Consuming APIs",
    targetCL2: 1,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
  },
  {
    id: "testing",
    category: "Testing & QA",
    description: "Unit Testing Basics, Debugging, Understanding Test Plans",
    targetCL2: 1,
    targetCL3: 2,
    targetCL4: 3,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
  },
  {
    id: "communication",
    category: "Communication",
    description: "Teamwork, Clarity, Active Listening, Basic Documentation",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 5,
    targetCL6: 5,
    currentLevel: null,
  },
  {
    id: "problemsolving",
    category: "Problem Solving",
    description: "Analysis, Troubleshooting Simple Issues, Following Guidance",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
  },
  {
    id: "leadership",
    category: "Leadership",
    description: "N/A",
    targetCL2: 1,
    targetCL3: 2,
    targetCL4: 4,
    targetCL5: 5,
    targetCL6: 6,
    currentLevel: null,
  },
]
