/**
 * Environment configuration for the application
 * This allows the application to run in different environments with different configurations
 */

// Authentication configuration
export const AUTH_CONFIG = {
  // Whether authentication is required
  // In production, this should always be true
  // In development, this can be set to false to bypass authentication
  REQUIRE_AUTH: process.env.REQUIRE_AUTH !== 'false',
  
  // Google OAuth credentials
  GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
  
  // NextAuth configuration
  NEXTAUTH_URL: process.env.NEXTAUTH_URL || (process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : 'http://localhost:3000'),
  NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET || 'development-secret-do-not-use-in-production',
  
  // Allowed email domain for authentication
  ALLOWED_DOMAIN: process.env.ALLOWED_DOMAIN || 'example.com',
}

// Database configuration
export const DB_CONFIG = {
  // Whether to use a real database
  // In production, this should always be true
  // In development, this can be set to false to use in-memory storage
  USE_DATABASE: process.env.USE_DATABASE !== 'false',
  
  // MongoDB connection string
  MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017/skills-assessment',
}

// Application configuration
export const APP_CONFIG = {
  // Application name
  APP_NAME: 'Skills Self-Assessment Tool',
  
  // Application version
  APP_VERSION: '1.0.0',
  
  // Whether the application is running in development mode
  IS_DEV: process.env.NODE_ENV === 'development',
}
