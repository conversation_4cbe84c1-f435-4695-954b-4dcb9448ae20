// Define the type for SSE connections
type SSEWriter = {
  write: (chunk: Uint8Array) => Promise<void>;
  closed?: boolean | Promise<void>;
};

// Store connections in a global variable
declare global {
  var connections: SSEWriter[];
  var connectionCount: number;
}

// Initialize global variables if they don't exist
if (global.connections === undefined) {
  global.connections = [];
}

if (global.connectionCount === undefined) {
  global.connectionCount = 0;
}

/**
 * Helper function to broadcast messages to all connected clients
 * @param message The message to broadcast
 * @param logInfo Whether to log information about the broadcast
 */
export async function broadcastMessage(message: any, logInfo: boolean = false) {
  if (!global.connections || global.connections.length === 0) {
    if (logInfo) {
      console.log('No active SSE connections to broadcast to');
    }
    return;
  }

  const encoder = new TextEncoder();
  const encodedMessage = encoder.encode(`data: ${JSON.stringify(message)}\n\n`);

  // Keep track of dead connections to remove
  const deadConnections: number[] = [];

  // Send the message to all connections
  await Promise.all(
    global.connections.map(async (writer, index) => {
      try {
        await writer.write(encodedMessage);
      } catch (error) {
        // Mark this connection as dead
        deadConnections.push(index);

        // Only log detailed errors if requested
        if (logInfo) {
          console.error(`Error broadcasting to connection ${index}:`, error);
        }
      }
    })
  );

  // Remove dead connections (in reverse order to avoid index shifting issues)
  if (deadConnections.length > 0) {
    for (let i = deadConnections.length - 1; i >= 0; i--) {
      const index = deadConnections[i];
      global.connections.splice(index, 1);
    }

    if (logInfo) {
      console.log(`Removed ${deadConnections.length} dead SSE connections. ${global.connections.length} connections remaining.`);
    }
  }

  if (logInfo) {
    console.log(`Broadcast message of type "${message.type}" to ${global.connections.length} clients`);
  }
}

/**
 * Get the current number of active SSE connections
 */
export function getConnectionCount(): number {
  return global.connections?.length || 0;
}
