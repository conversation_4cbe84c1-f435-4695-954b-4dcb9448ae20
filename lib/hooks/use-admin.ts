'use client';

import { useState, useEffect } from 'react';
import { useSessionContext } from '@/components/auth/session-context';
import { isAdmin } from '@/lib/actions/settings-actions';

/**
 * Hook to check if the current user is an admin
 * @returns Object with isAdmin and isLoading flags
 */
export function useAdminStatus() {
  // Use our optimized session context instead of useSession directly
  const { session, status } = useSessionContext();
  const [isAdminUser, setIsAdminUser] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAdminStatus = async () => {
      setIsLoading(true);

      try {
        if (status === 'authenticated' && session?.user?.email) {
          // Check if the user is an admin using the server action
          const adminResult = await isAdmin(session.user.email);
          setIsAdminUser(adminResult);
        } else {
          setIsAdminUser(false);
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
        setIsAdminUser(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAdminStatus();
  }, [session?.user?.email, status]);

  return { isAdmin: isAdminUser, isLoading };
}
