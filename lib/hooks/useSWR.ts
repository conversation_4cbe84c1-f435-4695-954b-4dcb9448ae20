import useSWR, { SWRConfiguration, SWRResponse } from 'swr';
import useSWRInfinite, { SWRInfiniteConfiguration, SWRInfiniteResponse } from 'swr/infinite';

/**
 * Default fetcher for SWR that handles JSON responses
 * @param url URL to fetch
 * @returns JSON response
 */
export const defaultFetcher = async (url: string) => {
  const res = await fetch(url);
  
  // If the status code is not in the range 200-299, throw an error
  if (!res.ok) {
    const error = new Error('An error occurred while fetching the data.');
    // Add extra info to the error object
    (error as any).info = await res.json();
    (error as any).status = res.status;
    throw error;
  }
  
  return res.json();
};

/**
 * Custom hook for data fetching with SWR
 * @param url URL to fetch
 * @param config SWR configuration
 * @returns SWR response
 */
export function useData<Data = any, Error = any>(
  url: string | null,
  config: SWRConfiguration = {}
): SWRResponse<Data, Error> {
  // Use default configuration with custom options
  const defaultConfig: SWRConfiguration = {
    fetcher: defaultFetcher,
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 5000, // 5 seconds
    focusThrottleInterval: 5000, // 5 seconds
    errorRetryCount: 3,
    ...config,
  };
  
  return useSWR<Data, Error>(url, defaultConfig);
}

/**
 * Custom hook for infinite loading with SWR
 * @param getKey Function to get the key for each page
 * @param config SWR infinite configuration
 * @returns SWR infinite response
 */
export function useInfiniteData<Data = any, Error = any>(
  getKey: (pageIndex: number, previousPageData: Data | null) => string | null,
  config: SWRInfiniteConfiguration = {}
): SWRInfiniteResponse<Data, Error> {
  // Use default configuration with custom options
  const defaultConfig: SWRInfiniteConfiguration = {
    fetcher: defaultFetcher,
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 5000, // 5 seconds
    persistSize: true,
    ...config,
  };
  
  return useSWRInfinite<Data, Error>(getKey, defaultConfig);
}

/**
 * Custom hook for data fetching with automatic polling
 * @param url URL to fetch
 * @param interval Polling interval in milliseconds
 * @param config SWR configuration
 * @returns SWR response
 */
export function usePollingData<Data = any, Error = any>(
  url: string | null,
  interval: number = 10000, // 10 seconds default
  config: SWRConfiguration = {}
): SWRResponse<Data, Error> {
  return useData<Data, Error>(url, {
    refreshInterval: interval,
    ...config,
  });
}

/**
 * Custom hook for data fetching with optimistic updates
 * @param url URL to fetch
 * @param config SWR configuration
 * @returns SWR response with optimistic update function
 */
export function useOptimisticData<Data = any, Error = any>(
  url: string | null,
  config: SWRConfiguration = {}
) {
  const { data, error, mutate, ...rest } = useData<Data, Error>(url, config);
  
  /**
   * Update data optimistically
   * @param updateFn Function to update the data
   * @param options Options for the optimistic update
   */
  const optimisticUpdate = async (
    updateFn: (currentData: Data) => Data,
    options: { rollbackOnError?: boolean } = { rollbackOnError: true }
  ) => {
    if (!data) return;
    
    // Create optimistic data
    const optimisticData = updateFn(data);
    
    try {
      // Update the cache optimistically
      await mutate(optimisticData, false);
      
      // Revalidate to get the latest data
      await mutate();
    } catch (updateError) {
      // Rollback on error if specified
      if (options.rollbackOnError) {
        await mutate(data, false);
      }
      throw updateError;
    }
  };
  
  return {
    data,
    error,
    mutate,
    optimisticUpdate,
    ...rest,
  };
}
