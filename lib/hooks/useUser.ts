import { useData } from './useSWR';
import { UserProfile } from '@/types/user';

/**
 * Custom hook for fetching user profile data
 * @param userId User ID to fetch
 * @returns SWR response with user data
 */
export function useUser(userId?: string) {
  const { data, error, isLoading, mutate } = useData<UserProfile>(
    userId ? `/api/users/${userId}` : null,
    {
      revalidateOnFocus: false,
      dedupingInterval: 60000, // 1 minute
    }
  );

  return {
    user: data,
    isLoading,
    isError: error,
    mutate,
  };
}

/**
 * Custom hook for fetching the current user's profile
 * @returns SWR response with current user data
 */
export function useCurrentUser() {
  const { data, error, isLoading, mutate } = useData<UserProfile>(
    '/api/users/me',
    {
      revalidateOnFocus: false,
      dedupingInterval: 60000, // 1 minute
    }
  );

  return {
    user: data,
    isLoading,
    isError: error,
    mutate,
  };
}

/**
 * Custom hook for fetching multiple user profiles
 * @param userIds Array of user IDs to fetch
 * @returns SWR response with user data array
 */
export function useUsers(userIds?: string[]) {
  const shouldFetch = userIds && userIds.length > 0;
  const queryString = shouldFetch ? `?ids=${userIds.join(',')}` : null;
  
  const { data, error, isLoading, mutate } = useData<UserProfile[]>(
    shouldFetch ? `/api/users/batch${queryString}` : null,
    {
      revalidateOnFocus: false,
      dedupingInterval: 60000, // 1 minute
    }
  );

  return {
    users: data || [],
    isLoading,
    isError: error,
    mutate,
  };
}
