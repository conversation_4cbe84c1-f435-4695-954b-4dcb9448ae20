'use client';

import { useEffect, useState, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';

type SSEOptions = {
  autoRefresh?: boolean;
  maxRetries?: number;
  initialRetryDelay?: number;
};

export function useSSE(options: SSEOptions = {}) {
  const {
    autoRefresh = true,
    maxRetries = 5,
    initialRetryDelay = 1000
  } = options;

  const router = useRouter();
  const [lastEvent, setLastEvent] = useState<any>(null);
  const [connected, setConnected] = useState(false);

  // Use refs to track connection state across renders
  const eventSourceRef = useRef<EventSource | null>(null);
  const retryCountRef = useRef(0);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Create or recreate the EventSource connection
  const createEventSource = useCallback(() => {
    // Clean up any existing connection
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }

    // Clear any pending retry timeout
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }

    // Create a new EventSource with cache-busting query parameter
    // This helps prevent browser caching issues with SSE connections
    const timestamp = Date.now();
    eventSourceRef.current = new EventSource(`/api/sse?t=${timestamp}`);

    // Handle connection open
    eventSourceRef.current.onopen = () => {
      setConnected(true);
      retryCountRef.current = 0; // Reset retry count on successful connection
      console.log('SSE connection established');
    };

    // Handle messages
    eventSourceRef.current.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        setLastEvent(data);

        // Auto-refresh the page when template or cycle changes occur
        if (autoRefresh) {
          if (data.type === 'template_saved' || data.type === 'template_deleted') {
            console.log('Template change detected, refreshing data...');
            router.refresh();
          } else if (data.type === 'cycle_created' || data.type === 'cycle_activated' || data.type === 'cycle_updated') {
            console.log('Assessment cycle change detected, refreshing data...');
            router.refresh();
          }
        }
      } catch (error) {
        console.error('Error parsing SSE message:', error);
      }
    };

    // Handle errors with exponential backoff retry
    eventSourceRef.current.onerror = (error) => {
      // Log the error with more context
      console.error('SSE connection error:', {
        error,
        retryCount: retryCountRef.current,
        maxRetries
      });

      setConnected(false);

      // Close the current connection
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }

      // Implement exponential backoff for retries
      if (retryCountRef.current < maxRetries) {
        const retryDelay = initialRetryDelay * Math.pow(2, retryCountRef.current);
        console.log(`Retrying SSE connection in ${retryDelay}ms (attempt ${retryCountRef.current + 1}/${maxRetries})`);

        retryTimeoutRef.current = setTimeout(() => {
          retryCountRef.current++;
          createEventSource();
        }, retryDelay);
      } else {
        console.warn(`SSE connection failed after ${maxRetries} attempts. Will not retry automatically.`);
      }
    };
  }, [router, autoRefresh, maxRetries, initialRetryDelay]);

  useEffect(() => {
    // Initialize the connection
    createEventSource();

    // Clean up on unmount
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }

      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
        retryTimeoutRef.current = null;
      }
    };
  }, [createEventSource]);

  return {
    connected,
    lastEvent,
    // Add a reconnect function that can be called manually if needed
    reconnect: () => {
      retryCountRef.current = 0;
      createEventSource();
    }
  };
}
