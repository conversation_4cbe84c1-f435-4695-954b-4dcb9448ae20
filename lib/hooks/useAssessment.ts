import { useData, useInfiniteData } from './useSWR';
import { Assessment, AssessmentCycle, AssessmentTemplate } from '@/types/assessment';

/**
 * Custom hook for fetching assessment data
 * @param assessmentId Assessment ID to fetch
 * @returns SWR response with assessment data
 */
export function useAssessment(assessmentId?: string) {
  const { data, error, isLoading, mutate } = useData<Assessment>(
    assessmentId ? `/api/assessments/${assessmentId}` : null,
    {
      revalidateOnFocus: false,
      dedupingInterval: 30000, // 30 seconds
    }
  );

  return {
    assessment: data,
    isLoading,
    isError: error,
    mutate,
  };
}

/**
 * Custom hook for fetching user assessments
 * @param userId User ID to fetch assessments for
 * @param cycleId Optional cycle ID to filter by
 * @returns SWR response with assessments data
 */
export function useUserAssessments(userId?: string, cycleId?: string) {
  const queryParams = new URLSearchParams();
  if (cycleId) queryParams.set('cycleId', cycleId);
  
  const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
  
  const { data, error, isLoading, mutate } = useData<Assessment[]>(
    userId ? `/api/users/${userId}/assessments${queryString}` : null,
    {
      revalidateOnFocus: false,
      dedupingInterval: 30000, // 30 seconds
    }
  );

  return {
    assessments: data || [],
    isLoading,
    isError: error,
    mutate,
  };
}

/**
 * Custom hook for fetching assessment cycles with pagination
 * @param limit Number of items per page
 * @returns SWR infinite response with assessment cycles data
 */
export function useAssessmentCycles(limit: number = 10) {
  const getKey = (pageIndex: number, previousPageData: { cycles: AssessmentCycle[], hasMore: boolean } | null) => {
    // Reached the end
    if (previousPageData && !previousPageData.hasMore) return null;
    
    // First page
    if (pageIndex === 0) return `/api/assessment-cycles?limit=${limit}`;
    
    // Add the cursor to the API endpoint
    const lastItem = previousPageData!.cycles[previousPageData!.cycles.length - 1];
    return `/api/assessment-cycles?cursor=${lastItem._id}&limit=${limit}`;
  };

  const { data, error, size, setSize, isLoading, mutate } = useInfiniteData<{ cycles: AssessmentCycle[], hasMore: boolean }>(getKey);

  // Flatten the pages
  const cycles = data ? data.flatMap(page => page.cycles) : [];
  const hasMore = data ? data[data.length - 1]?.hasMore : false;

  return {
    cycles,
    hasMore,
    isLoading,
    isError: error,
    loadMore: () => setSize(size + 1),
    mutate,
  };
}

/**
 * Custom hook for fetching assessment templates
 * @param businessUnit Optional business unit to filter by
 * @param templateType Optional template type to filter by
 * @returns SWR response with assessment templates data
 */
export function useAssessmentTemplates(businessUnit?: string, templateType?: string) {
  const queryParams = new URLSearchParams();
  if (businessUnit) queryParams.set('businessUnit', businessUnit);
  if (templateType) queryParams.set('templateType', templateType);
  
  const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
  
  const { data, error, isLoading, mutate } = useData<AssessmentTemplate[]>(
    `/api/assessment-templates${queryString}`,
    {
      revalidateOnFocus: false,
      dedupingInterval: 60000, // 1 minute
    }
  );

  return {
    templates: data || [],
    isLoading,
    isError: error,
    mutate,
  };
}
