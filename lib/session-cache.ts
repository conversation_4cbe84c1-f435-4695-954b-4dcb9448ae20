import { getServerSession } from "next-auth";
import { cache } from "react";
import { authOptions } from "./auth";
import { cookies } from "next/headers";

/**
 * Cached version of getServerSession to reduce redundant session fetches
 * This uses React's cache function to deduplicate requests within the same render cycle
 */
export const getServerSessionCached = cache(async () => {
  // Get cookies to pass to getServerSession for App Router
  const cookieStore = cookies();
  const req = {
    headers: {
      cookie: cookieStore.toString(),
    },
  };

  return await getServerSession(req as any, {} as any, authOptions);
});
