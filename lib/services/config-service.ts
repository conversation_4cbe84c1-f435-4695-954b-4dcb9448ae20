import { getCollection } from '@/lib/mongodb';
import { ConfigDocument, CONFIG_KEYS, DEFAULT_CONFIG } from '@/lib/models/config';

// Collection name
const COLLECTION_NAME = 'config';

/**
 * Get a configuration value from the database
 * @param key Configuration key
 * @returns Configuration value or default value if not found
 */
export async function getConfig<T>(key: string): Promise<T> {
  try {
    const collection = await getCollection<ConfigDocument>(COLLECTION_NAME);
    const config = await collection.findOne({ key });
    
    if (config) {
      return config.value as T;
    }
    
    // Return default value if not found
    return (DEFAULT_CONFIG[key] || null) as T;
  } catch (error) {
    console.error(`Error getting config ${key}:`, error);
    return (DEFAULT_CONFIG[key] || null) as T;
  }
}

/**
 * Set a configuration value in the database
 * @param key Configuration key
 * @param value Configuration value
 * @param description Optional description
 * @returns True if successful, false otherwise
 */
export async function setConfig(key: string, value: any, description?: string): Promise<boolean> {
  try {
    const collection = await getCollection<ConfigDocument>(COLLECTION_NAME);
    
    const result = await collection.updateOne(
      { key },
      { 
        $set: { 
          value,
          description,
          updatedAt: new Date()
        } 
      },
      { upsert: true }
    );
    
    return result.acknowledged;
  } catch (error) {
    console.error(`Error setting config ${key}:`, error);
    return false;
  }
}

/**
 * Get all admin emails from the database
 * @returns Array of admin email addresses
 */
export async function getAdminEmails(): Promise<string[]> {
  const adminEmails = await getConfig<string[]>(CONFIG_KEYS.ADMIN_EMAILS);
  return Array.isArray(adminEmails) ? adminEmails : [];
}

/**
 * Check if a user is an admin
 * @param email User email address
 * @returns True if the user is an admin, false otherwise
 */
export async function isAdmin(email: string): Promise<boolean> {
  if (!email) return false;
  
  const adminEmails = await getAdminEmails();
  return adminEmails.includes(email);
}

/**
 * Add an admin email to the database
 * @param email Email address to add
 * @returns True if successful, false otherwise
 */
export async function addAdminEmail(email: string): Promise<boolean> {
  if (!email) return false;
  
  const adminEmails = await getAdminEmails();
  
  // Check if email already exists
  if (adminEmails.includes(email)) {
    return true;
  }
  
  // Add email to the list
  adminEmails.push(email);
  
  // Update the database
  return await setConfig(
    CONFIG_KEYS.ADMIN_EMAILS, 
    adminEmails,
    'List of admin email addresses'
  );
}

/**
 * Remove an admin email from the database
 * @param email Email address to remove
 * @returns True if successful, false otherwise
 */
export async function removeAdminEmail(email: string): Promise<boolean> {
  if (!email) return false;
  
  const adminEmails = await getAdminEmails();
  
  // Check if email exists
  const index = adminEmails.indexOf(email);
  if (index === -1) {
    return true;
  }
  
  // Remove email from the list
  adminEmails.splice(index, 1);
  
  // Update the database
  return await setConfig(
    CONFIG_KEYS.ADMIN_EMAILS, 
    adminEmails,
    'List of admin email addresses'
  );
}
