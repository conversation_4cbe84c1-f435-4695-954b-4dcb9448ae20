import 'server-only';

import { getCollection } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

// Define the settings document interface
export interface SettingsDocument {
  _id?: ObjectId;
  key: string;
  value: any;
  description?: string;
  updatedAt: Date;
}

// Define settings keys
export const SETTINGS_KEYS = {
  ADMIN_EMAILS: 'admin_emails',
  ASSESSMENT_INTERVAL_MONTHS: 'assessment_interval_months',
  ALLOW_ASSESSMENT_UPDATES: 'allow_assessment_updates',
};

// Define default settings
export const DEFAULT_SETTINGS = {
  [SETTINGS_KEYS.ADMIN_EMAILS]: [],
  [SETTINGS_KEYS.ASSESSMENT_INTERVAL_MONTHS]: 6,
  [SETTINGS_KEYS.ALLOW_ASSESSMENT_UPDATES]: false,
};

// Collection name
const COLLECTION_NAME = 'settings';

/**
 * Get a setting from the database
 * @param key Setting key
 * @returns Setting value or default value if not found
 */
export async function getSetting<T>(key: string): Promise<T> {
  try {
    const collection = await getCollection<SettingsDocument>(COLLECTION_NAME);
    const setting = await collection.findOne({ key });

    if (setting) {
      return setting.value as T;
    }

    // Return default value if not found
    return (DEFAULT_SETTINGS[key] || null) as T;
  } catch (error) {
    console.error(`Error getting setting ${key}:`, error);
    return (DEFAULT_SETTINGS[key] || null) as T;
  }
}

/**
 * Set a setting in the database
 * @param key Setting key
 * @param value Setting value
 * @param description Optional description
 * @returns True if successful, false otherwise
 */
export async function setSetting(key: string, value: any, description?: string): Promise<boolean> {
  try {
    const collection = await getCollection<SettingsDocument>(COLLECTION_NAME);

    const result = await collection.updateOne(
      { key },
      {
        $set: {
          value,
          description,
          updatedAt: new Date()
        }
      },
      { upsert: true }
    );

    return result.acknowledged;
  } catch (error) {
    console.error(`Error setting setting ${key}:`, error);
    return false;
  }
}

/**
 * Get all admin emails from the database
 * @returns Array of admin email addresses
 */
export async function getAdminEmails(): Promise<string[]> {
  const adminEmails = await getSetting<string[]>(SETTINGS_KEYS.ADMIN_EMAILS);
  return Array.isArray(adminEmails) ? adminEmails : [];
}

/**
 * Check if a user is an admin
 * @param email User email address
 * @returns True if the user is an admin, false otherwise
 */
export async function isAdmin(email: string): Promise<boolean> {
  if (!email) return false;

  const adminEmails = await getAdminEmails();

  // If no admin emails are in the database, fall back to environment variable
  if (adminEmails.length === 0) {
    const envAdmins = (process.env.ADMIN_EMAILS || '').split(',')
      .map(e => e.trim())
      .filter(e => e);
    return envAdmins.includes(email);
  }

  return adminEmails.includes(email);
}

/**
 * Add an admin email to the database
 * @param email Email address to add
 * @returns True if successful, false otherwise
 */
export async function addAdminEmail(email: string): Promise<boolean> {
  if (!email) return false;

  const adminEmails = await getAdminEmails();

  // Check if email already exists
  if (adminEmails.includes(email)) {
    return true;
  }

  // Add email to the list
  adminEmails.push(email);

  // Update the database
  return await setSetting(
    SETTINGS_KEYS.ADMIN_EMAILS,
    adminEmails,
    'List of admin email addresses'
  );
}

/**
 * Remove an admin email from the database
 * @param email Email address to remove
 * @returns True if successful, false otherwise
 */
export async function removeAdminEmail(email: string): Promise<boolean> {
  if (!email) return false;

  const adminEmails = await getAdminEmails();

  // Check if email exists
  const index = adminEmails.indexOf(email);
  if (index === -1) {
    return true;
  }

  // Remove email from the list
  adminEmails.splice(index, 1);

  // Update the database
  return await setSetting(
    SETTINGS_KEYS.ADMIN_EMAILS,
    adminEmails,
    'List of admin email addresses'
  );
}

/**
 * Initialize admin emails from environment variables
 * @returns True if successful, false otherwise
 */
export async function initializeAdminEmails(): Promise<boolean> {
  try {
    // Get existing admin emails from the database
    const existingAdmins = await getAdminEmails();

    // If admin emails already exist in the database, don't initialize
    if (existingAdmins.length > 0) {
      return true;
    }

    // Get admin emails from environment variables
    const envAdmins = (process.env.ADMIN_EMAILS || '').split(',')
      .map(email => email.trim())
      .filter(email => email);

    // If no admin emails in environment variables, don't initialize
    if (envAdmins.length === 0) {
      return true;
    }

    // Set admin emails in the database
    return await setSetting(
      SETTINGS_KEYS.ADMIN_EMAILS,
      envAdmins,
      'List of admin email addresses initialized from environment variables'
    );
  } catch (error) {
    console.error('Error initializing admin emails:', error);
    return false;
  }
}

/**
 * Get assessment interval months from the database
 * @returns Assessment interval months
 */
export async function getAssessmentIntervalMonths(): Promise<number> {
  const months = await getSetting<number>(SETTINGS_KEYS.ASSESSMENT_INTERVAL_MONTHS);
  return typeof months === 'number' ? months : DEFAULT_SETTINGS[SETTINGS_KEYS.ASSESSMENT_INTERVAL_MONTHS] as number;
}

/**
 * Set assessment interval months in the database
 * @param months Assessment interval months
 * @returns True if successful, false otherwise
 */
export async function setAssessmentIntervalMonths(months: number): Promise<boolean> {
  return await setSetting(
    SETTINGS_KEYS.ASSESSMENT_INTERVAL_MONTHS,
    months,
    'Number of months between assessments (0 = no restriction)'
  );
}

/**
 * Get allow assessment updates setting from the database
 * @returns True if assessment updates are allowed, false otherwise
 */
export async function getAllowAssessmentUpdates(): Promise<boolean> {
  const allow = await getSetting<boolean>(SETTINGS_KEYS.ALLOW_ASSESSMENT_UPDATES);
  return typeof allow === 'boolean' ? allow : DEFAULT_SETTINGS[SETTINGS_KEYS.ALLOW_ASSESSMENT_UPDATES] as boolean;
}

/**
 * Set allow assessment updates setting in the database
 * @param allow True to allow assessment updates, false otherwise
 * @returns True if successful, false otherwise
 */
export async function setAllowAssessmentUpdates(allow: boolean): Promise<boolean> {
  return await setSetting(
    SETTINGS_KEYS.ALLOW_ASSESSMENT_UPDATES,
    allow,
    'Whether to allow updating existing assessments'
  );
}
