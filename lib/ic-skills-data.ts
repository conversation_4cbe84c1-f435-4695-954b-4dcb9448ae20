export interface ICSkill {
  id: string
  category: string
  description: string
  skillNumber: number
  categoryGroup: string
  targetCL2: number
  targetCL3: number
  targetCL4: number
  targetCL5: number
  targetCL6: number
  currentLevel: number | null
  icLevelDescriptions: {
    L1: string
    L2: string
    L3: string
    L4: string
    L5: string
    L6: string
  }
}

// Rating scale description
export const ratingScale = {
  1: "Basic",
  2: "Beginner",
  3: "Intermediate",
  4: "Advanced",
  5: "Expert",
  6: "Mastery"
}

// IC Career Level Skill Categories
export const icSkillCategories = {
  "technical": "I. Technical Proficiency & Engineering Craft",
  "problemSolving": "II. Problem Solving & Execution",
  "collaboration": "III. Collaboration, Leadership & Impact"
}

// Official 18 IC Skills from Stratpoint Career Matrices
export const icSkillsData: ICSkill[] = [
  // I. Technical Proficiency & Engineering Craft (10 skills)
  {
    id: "core-programming-tooling",
    category: "Core Programming & Tooling",
    description: "Programming languages, frameworks, and development tools relevant to engineering discipline",
    skillNumber: 1,
    categoryGroup: "technical",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 5,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Shows eagerness to learn foundational programming concepts and tools relevant to the team's focus. Can complete very simple, well-defined tasks with explicit instructions and close supervision.",
      L2: "Demonstrates foundational proficiency in at least one core programming language or engineering toolset relevant to the team's focus. Able to complete basic tasks with guidance.",
      L3: "Demonstrates solid proficiency in core programming languages and tools. Can work independently on moderately complex tasks and contribute meaningfully to team projects.",
      L4: "Demonstrates advanced proficiency across multiple programming languages and tools. Can lead technical implementation and mentor others in best practices.",
      L5: "Recognized as a technical authority and thought leader in multiple critical technology areas. Architects and leads implementation of highly complex, scalable systems spanning multiple teams.",
      L6: "Sets technical vision and strategy for major parts of the organization. Recognized as a foremost expert with industry-wide influence in multiple complex domains."
    }
  },
  {
    id: "software-engineering-fundamentals",
    category: "Software Engineering Fundamentals",
    description: "Core concepts including data structures, algorithms, and domain-specific engineering principles",
    skillNumber: 2,
    categoryGroup: "technical",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Demonstrates an interest in and begins to grasp foundational software engineering concepts. Learning about basic data types and conceptual awareness of algorithms and data structures.",
      L2: "Understands and can apply foundational concepts of their chosen engineering discipline. Understands basic data structures and common algorithms at a conceptual level.",
      L3: "Applies solid understanding of software engineering fundamentals to solve moderately complex problems. Can design and implement efficient solutions using appropriate data structures and algorithms.",
      L4: "Demonstrates deep understanding of software engineering principles and can apply advanced concepts to complex problems. Can evaluate trade-offs and make informed architectural decisions.",
      L5: "Applies profound understanding of software engineering fundamentals to solve the most challenging technical problems and guide architectural direction of major systems.",
      L6: "Innovates on fundamental principles and sets new standards for software engineering practices across the organization and industry."
    }
  },
  {
    id: "system-design-architecture",
    category: "System Design & Architecture",
    description: "Understanding of system components, architectural patterns, and design principles",
    skillNumber: 3,
    categoryGroup: "technical",
    targetCL2: 1,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Observes and learns about how systems are structured by shadowing senior engineers or through guided explanations. Understands that systems are made of multiple interacting components.",
      L2: "Understands the components of well-defined systems relevant to their work. Can follow and contribute to simple design discussions with guidance.",
      L3: "Can design moderately complex systems and contribute meaningfully to architectural discussions. Understands common design patterns and can apply them appropriately.",
      L4: "Can design complex systems and lead architectural decisions. Demonstrates deep understanding of design patterns, scalability, and system trade-offs.",
      L5: "Leads architecture and design of complex, multi-system solutions with significant business impact. Defines and champions architectural vision and long-term strategy.",
      L6: "Sets architectural standards and vision for the entire organization. Recognized as a foremost expert in system design with industry-wide influence."
    }
  },
  {
    id: "code-quality-standards",
    category: "Code Quality & Standards",
    description: "Clean code practices, coding standards, and engineering best practices",
    skillNumber: 4,
    categoryGroup: "technical",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Learns about the importance of clean code and following team coding standards through observation and guidance. Attempts to apply simple formatting and naming conventions.",
      L2: "Learns and consistently applies established engineering practices, patterns, and coding standards with guidance. Writes clean, maintainable code with some oversight.",
      L3: "Consistently writes high-quality, maintainable code following established standards. Can review others' code and provide constructive feedback.",
      L4: "Champions code quality and establishes coding standards for the team. Can mentor others in best practices and drive continuous improvement in code quality.",
      L5: "Sets the bar for code quality and engineering excellence across multiple teams or a significant domain. Drives initiatives that lead to substantial improvements in codebase health, maintainability, and performance at an organizational level.",
      L6: "Defines and drives the highest standards of code quality and engineering excellence across the entire organization. Influences industry practices and contributes to open-source standards."
    }
  },
  {
    id: "testing-debugging",
    category: "Testing & Debugging",
    description: "Unit testing, integration testing, debugging techniques, and quality assurance practices",
    skillNumber: 5,
    categoryGroup: "technical",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Learns about the purpose of testing. May run pre-written tests. With significant guidance, can use basic debugging tools to understand code flow for very simple issues.",
      L2: "Understands and can apply basic debugging techniques. Writes effective unit tests for their code using relevant frameworks. May assist with basic integration tests under supervision.",
      L3: "Writes comprehensive tests including unit, integration, and end-to-end tests. Can debug complex issues independently and help others with debugging.",
      L4: "Establishes testing strategies and standards for the team. Can design and implement comprehensive testing frameworks and mentor others in testing best practices.",
      L5: "Architects organization-wide testing strategies and quality assurance frameworks. Can lead testing initiatives across multiple teams and establish testing standards.",
      L6: "Defines industry-leading testing practices and contributes to testing frameworks. Recognized as an expert in software quality and testing methodologies."
    }
  },
  {
    id: "version-control",
    category: "Version Control (e.g., Git)",
    description: "Git operations, branching strategies, and collaborative development workflows",
    skillNumber: 6,
    categoryGroup: "technical",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Learns basic Git operations like clone, commit, push, and pull under direct supervision. Understands the concept of branching.",
      L2: "Effectively uses version control systems for managing code changes, including branching, committing, pulling, and pushing with guidance. Understands basic merging concepts.",
      L3: "Proficiently uses advanced Git features including complex branching strategies, rebasing, and conflict resolution. Can guide others in version control best practices.",
      L4: "Masters version control workflows and can establish branching strategies for teams. Can troubleshoot complex Git issues and mentor others in advanced Git techniques.",
      L5: "Architects organization-wide version control strategies and workflows. Can design and implement complex Git workflows across multiple teams and repositories.",
      L6: "Defines industry-leading version control practices and contributes to Git tooling. Recognized as an expert in version control systems and collaborative development workflows."
    }
  },
  {
    id: "development-operational-tools",
    category: "Development & Operational Tools",
    description: "IDEs, build tools, CI/CD, containerization, and development environment management",
    skillNumber: 7,
    categoryGroup: "technical",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Becomes familiar with the primary IDE used by the team. Learns to navigate project structures and run applications locally with guidance. Aware of project tracking tools.",
      L2: "Effectively uses common IDEs and development tools. Aware of build tools, API documentation tools, and basic containerization concepts. Uses project tracking tools effectively.",
      L3: "Proficiently uses advanced development tools and can set up complex development environments. Can configure CI/CD pipelines and containerization solutions.",
      L4: "Masters development toolchains and can establish development standards and tooling for teams. Can design and implement sophisticated CI/CD and deployment strategies.",
      L5: "Architects enterprise-wide development tooling strategies and platforms. Can design and implement organization-level CI/CD infrastructure and development environments.",
      L6: "Defines industry standards for development tooling and contributes to open-source development tools. Recognized as an expert in development infrastructure and toolchain optimization."
    }
  },
  {
    id: "database-data-management",
    category: "Database & Data Management",
    description: "Database design, querying, data modeling, and data management practices",
    skillNumber: 8,
    categoryGroup: "technical",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Learns about basic database concepts. Understands what a relational database is and can execute simple, pre-written SQL queries.",
      L2: "Possesses and applies basic database knowledge. Writes simple SQL queries and understands basic relational concepts. Aware of ORMs and specific database technologies.",
      L3: "Designs and implements database schemas and can write complex queries. Understands database optimization and can work with various database technologies.",
      L4: "Masters database design and can architect data solutions for complex systems. Can optimize database performance and establish data management standards.",
      L5: "Architects enterprise-wide data strategies and platforms. Can design distributed data systems and establish organization-level data governance and architecture standards.",
      L6: "Defines industry standards for data management and contributes to database technologies. Recognized as an expert in data architecture and large-scale data system design."
    }
  },
  {
    id: "security-principles-practices",
    category: "Security Principles & Practices",
    description: "Secure coding practices, authentication, authorization, and security best practices",
    skillNumber: 9,
    categoryGroup: "technical",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Develops an awareness of basic security concepts, such as the importance of protecting passwords and sensitive data. Follows explicit security guidelines provided by the team.",
      L2: "Demonstrates awareness of foundational security concepts and follows guidance on secure coding practices. Aware of security tools and basic security principles.",
      L3: "Implements security best practices and can identify and address security vulnerabilities. Understands authentication, authorization, and common security patterns.",
      L4: "Champions security practices and can design secure systems. Can conduct security reviews and establish security standards for teams.",
      L5: "Leads strategic initiatives and establishes organization-wide standards. Can architect solutions across multiple teams and drive technical excellence.",
      L6: "Defines industry-leading practices and contributes to the broader technical community. Recognized as a subject matter expert and thought leader."
    }
  },
  {
    id: "technical-documentation",
    category: "Technical Documentation",
    description: "Code documentation, API documentation, technical writing, and knowledge sharing",
    skillNumber: 10,
    categoryGroup: "technical",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Learns to read and understand basic technical documentation. May attempt to write very simple code comments with guidance.",
      L2: "Can document their work clearly including code comments, READMEs, and simple design notes. Aware of API documentation tools.",
      L3: "Creates comprehensive technical documentation and can write clear, detailed documentation for complex systems. Can review and improve others' documentation.",
      L4: "Establishes documentation standards and practices for teams. Can create architectural documentation and mentor others in effective technical writing.",
      L5: "Leads strategic initiatives and establishes organization-wide standards. Can architect solutions across multiple teams and drive technical excellence.",
      L6: "Defines industry-leading practices and contributes to the broader technical community. Recognized as a subject matter expert and thought leader."
    }
  },
  // II. Problem Solving & Execution (3 skills)
  {
    id: "analytical-critical-thinking",
    category: "Analytical & Critical Thinking",
    description: "Problem analysis, solution identification, and critical thinking skills",
    skillNumber: 11,
    categoryGroup: "problemSolving",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Asks clarifying questions to understand problems. Follows step-by-step instructions to address simple, well-defined issues. Shows curiosity and a desire to understand how things work.",
      L2: "Applies basic problem-solving skills to analyze issues and identify potential solutions for small to medium-complexity tasks. Can articulate the problem and proposed solution.",
      L3: "Demonstrates strong analytical skills and can break down complex problems into manageable components. Can evaluate multiple solutions and make informed decisions.",
      L4: "Excels at critical thinking and can solve complex, ambiguous problems. Can guide others in problem-solving approaches and establish problem-solving frameworks.",
      L5: "Leads strategic initiatives and establishes organization-wide standards. Can architect solutions across multiple teams and drive technical excellence.",
      L6: "Defines industry-leading practices and contributes to the broader technical community. Recognized as a subject matter expert and thought leader."
    }
  },
  {
    id: "task-project-execution",
    category: "Task & Project Execution",
    description: "Task management, project planning, estimation, and delivery execution",
    skillNumber: 12,
    categoryGroup: "problemSolving",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Completes assigned, small, well-defined tasks with clear instructions and frequent check-ins. Learns to manage their time for these tasks with guidance.",
      L2: "Can break down small, well-defined tasks into manageable steps and estimate effort with guidance. Actively participates in team rituals and understands CI/CD role.",
      L3: "Can independently plan and execute moderately complex projects. Provides accurate estimates and manages dependencies effectively.",
      L4: "Can lead complex projects and coordinate multiple workstreams. Excels at project planning, risk management, and stakeholder communication.",
      L5: "Leads strategic initiatives and establishes organization-wide standards. Can architect solutions across multiple teams and drive technical excellence.",
      L6: "Defines industry-leading practices and contributes to the broader technical community. Recognized as a subject matter expert and thought leader."
    }
  },
  {
    id: "technical-debt-management",
    category: "Technical Debt Management",
    description: "Identifying, prioritizing, and addressing technical debt and system improvements",
    skillNumber: 13,
    categoryGroup: "problemSolving",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Learns what technical debt means at a high level. Can identify and report obvious bugs or deviations from instructions in their own work.",
      L2: "Capable of fixing straightforward bugs independently and addressing minor, well-defined technical debt under supervision.",
      L3: "Can identify, prioritize, and address technical debt systematically. Can balance feature development with technical improvements.",
      L4: "Can establish technical debt management strategies and lead refactoring initiatives. Can make informed decisions about technical trade-offs.",
      L5: "Leads strategic initiatives and establishes organization-wide standards. Can architect solutions across multiple teams and drive technical excellence.",
      L6: "Defines industry-leading practices and contributes to the broader technical community. Recognized as a subject matter expert and thought leader."
    }
  },
  // III. Collaboration, Leadership & Impact (5 skills)
  {
    id: "communication-collaboration",
    category: "Communication & Collaboration",
    description: "Team communication, collaboration skills, and agile methodology participation",
    skillNumber: 14,
    categoryGroup: "collaboration",
    targetCL2: 2,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Listens actively and asks questions to ensure understanding. Communicates progress on tasks to their mentor or supervisor. Participates in team meetings when invited, primarily in an observational capacity.",
      L2: "Is familiar with common development methodologies as practiced by the team. Actively and appropriately participates in team rituals. Communicates task status clearly.",
      L3: "Communicates effectively with team members and stakeholders. Can facilitate discussions and contribute meaningfully to team decisions.",
      L4: "Excels at communication and can lead cross-functional collaboration. Can represent the team in stakeholder meetings and drive alignment across teams.",
      L5: "Leads strategic initiatives and establishes organization-wide standards. Can architect solutions across multiple teams and drive technical excellence.",
      L6: "Defines industry-leading practices and contributes to the broader technical community. Recognized as a subject matter expert and thought leader."
    }
  },
  {
    id: "mentorship-technical-guidance",
    category: "Mentorship & Technical Guidance",
    description: "Providing guidance, mentoring others, and knowledge sharing",
    skillNumber: 15,
    categoryGroup: "collaboration",
    targetCL2: 1,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 5,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Primarily a receiver of mentorship and guidance. Shows willingness to learn from senior team members.",
      L2: "Primarily a receiver of mentorship and guidance. Shows willingness to learn from senior team members. May assist a new L1 intern with very basic, guided tasks if an opportunity arises.",
      L3: "Can mentor junior team members and provide technical guidance. Actively shares knowledge and helps others grow their skills.",
      L4: "Excels at mentoring and can develop mentorship programs. Can guide multiple team members and contribute to their career development.",
      L5: "Leads strategic initiatives and establishes organization-wide standards. Can architect solutions across multiple teams and drive technical excellence.",
      L6: "Defines industry-leading practices and contributes to the broader technical community. Recognized as a subject matter expert and thought leader."
    }
  },
  {
    id: "technical-leadership-ownership",
    category: "Technical Leadership & Ownership",
    description: "Taking ownership, leading technical initiatives, and driving technical decisions",
    skillNumber: 16,
    categoryGroup: "collaboration",
    targetCL2: 1,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 5,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Not expected at this level. Focus is on learning and completing assigned tasks.",
      L2: "Takes ownership of assigned tasks and ensures their completion to the best of their ability with guidance. Not expected to provide technical leadership to others.",
      L3: "Can lead technical initiatives and make informed technical decisions. Takes ownership of larger features and can guide technical direction.",
      L4: "Provides strong technical leadership and can drive technical strategy. Can lead complex technical initiatives and influence technical decisions across teams.",
      L5: "Leads strategic initiatives and establishes organization-wide standards. Can architect solutions across multiple teams and drive technical excellence.",
      L6: "Defines industry-leading practices and contributes to the broader technical community. Recognized as a subject matter expert and thought leader."
    }
  },
  {
    id: "operational-excellence-system-reliability",
    category: "Operational Excellence & System Reliability",
    description: "System monitoring, reliability practices, and operational responsibilities",
    skillNumber: 17,
    categoryGroup: "collaboration",
    targetCL2: 1,
    targetCL3: 3,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 4,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Learns about the importance of system reliability through observation and team discussions. Not expected to have operational responsibilities.",
      L2: "Learns about the importance of system reliability through observation and team discussions. Follows operational procedures as instructed for their tasks.",
      L3: "Understands and contributes to system reliability practices. Can participate in on-call rotations and incident response.",
      L4: "Champions operational excellence and can establish reliability practices. Can lead incident response and drive system reliability improvements.",
      L5: "Leads strategic initiatives and establishes organization-wide standards. Can architect solutions across multiple teams and drive technical excellence.",
      L6: "Defines industry-leading practices and contributes to the broader technical community. Recognized as a subject matter expert and thought leader."
    }
  },
  {
    id: "innovation-strategic-contribution",
    category: "Innovation & Strategic Contribution",
    description: "Innovation, strategic thinking, and contributing to long-term technical vision",
    skillNumber: 18,
    categoryGroup: "collaboration",
    targetCL2: 1,
    targetCL3: 2,
    targetCL4: 4,
    targetCL5: 4,
    targetCL6: 5,
    currentLevel: null,
    icLevelDescriptions: {
      L1: "Shows curiosity and enthusiasm for new technologies or ideas presented by the team. Not expected to make strategic contributions.",
      L2: "Shows curiosity and enthusiasm for new technologies or ideas presented by the team. May ask questions that could spark ideas but not expected to make strategic contributions or drive innovation independently.",
      L3: "Can contribute innovative ideas and evaluate new technologies. Can participate in strategic technical discussions and contribute to technical roadmaps.",
      L4: "Drives innovation and strategic technical initiatives. Can evaluate emerging technologies and contribute to long-term technical vision and strategy.",
      L5: "Leads strategic initiatives and establishes organization-wide standards. Can architect solutions across multiple teams and drive technical excellence.",
      L6: "Defines industry-leading practices and contributes to the broader technical community. Recognized as a subject matter expert and thought leader."
    }
  }
]
