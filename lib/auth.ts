/**
 * NextAuth configuration
 * This file provides the auth options for NextAuth.js
 */

import { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import { db } from "@/lib/db";
import { queryCache } from "@/lib/cache";
import {
  GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET,
  ALLOWED_DOMAIN,
  NEXTAUTH_SECRET
} from "@/lib/auth-config";

/**
 * NextAuth options configuration
 */
export const authOptions: NextAuthOptions = {
  providers: [
    // Google provider for authentication
    GoogleProvider({
      clientId: GOOGLE_CLIENT_ID,
      clientSecret: GOOGLE_CLIENT_SECRET,
      // Allow any port for the callback URL
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
  ],
  callbacks: {
    async signIn({ account, profile, user }) {
      // For Google authentication, check domain if specified
      if (account?.provider === "google" && ALLOWED_DOMAIN) {
        const isAllowed = profile?.email?.endsWith(`@${ALLOWED_DOMAIN}`) ?? false

        // If allowed, update user login status
        if (isAllowed && user.email) {
          try {
            // Update user login status
            await db.collection('user_profiles').updateOne(
              { email: user.email },
              {
                $set: {
                  hasLoggedIn: true,
                  lastLogin: new Date(),
                  updatedAt: new Date()
                }
              }
            )

            // Invalidate cache for this user
            queryCache.delete(`user_profile:${user.email}`)
          } catch (error) {
            console.error('Error updating user login status:', error)
            // Still allow sign in even if tracking fails
          }
        }

        return isAllowed
      }

      return false
    },
    async session({ session, token }) {
      if (session?.user) {
        // Add user ID to session
        session.user = {
          ...session.user,
          id: token.sub || 'unknown-id'
        }
      }
      return session
    },
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  // Add cookie options to improve session handling with Cloudflare Tunnel
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: true, // Always secure for Cloudflare Tunnel
        maxAge: 30 * 24 * 60 * 60, // 30 days
      },
    },
  },
  // Trust proxy headers from Cloudflare
  trustHost: true,
  secret: NEXTAUTH_SECRET,
};
