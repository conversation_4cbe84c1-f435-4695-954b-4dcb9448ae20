import { unstable_cache } from 'next/cache';

// Define cache TTL (Time To Live) in seconds
export const CACHE_TTL = {
  SHORT: 60, // 1 minute
  MEDIUM: 300, // 5 minutes
  LONG: 3600, // 1 hour
  VERY_LONG: 86400, // 24 hours
};

// In-memory cache for server components
const memoryCache = new Map<string, { value: any; expiry: number }>();

/**
 * Get a value from the memory cache
 * @param key Cache key
 * @returns Cached value or undefined if not found or expired
 */
export function getFromMemoryCache<T>(key: string): T | undefined {
  const cached = memoryCache.get(key);
  if (!cached) return undefined;

  // Check if the cached value has expired
  if (cached.expiry < Date.now()) {
    memoryCache.delete(key);
    return undefined;
  }

  return cached.value as T;
}

/**
 * Set a value in the memory cache
 * @param key Cache key
 * @param value Value to cache
 * @param ttl Time to live in seconds
 */
export function setInMemoryCache<T>(key: string, value: T, ttl: number): void {
  memoryCache.set(key, {
    value,
    expiry: Date.now() + ttl * 1000,
  });
}

/**
 * Clear the memory cache
 * @param keyPrefix Optional key prefix to clear only matching keys
 */
export function clearMemoryCache(keyPrefix?: string): void {
  if (keyPrefix) {
    // Clear only keys that start with the prefix
    for (const key of memoryCache.keys()) {
      if (key.startsWith(keyPrefix)) {
        memoryCache.delete(key);
      }
    }
  } else {
    // Clear all keys
    memoryCache.clear();
  }
}

/**
 * Cache a function with memory cache
 * @param fn Function to cache
 * @param keyPrefix Cache key prefix
 * @param ttl Time to live in seconds
 * @returns Cached function
 */
export function cacheFunction<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  keyPrefix: string,
  ttl: number = CACHE_TTL.MEDIUM
): T {
  // Create a cached version of the function
  const cachedFn = async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    // Create a cache key based on the function name, prefix, and arguments
    const cacheKey = `${keyPrefix}:${JSON.stringify(args)}`;

    // Try to get the value from the memory cache first
    const cachedValue = getFromMemoryCache<ReturnType<T>>(cacheKey);
    if (cachedValue !== undefined) {
      return cachedValue;
    }

    // If not in memory cache, execute the function
    const result = await fn(...args);

    // Store the result in the memory cache
    setInMemoryCache(cacheKey, result, ttl);

    return result;
  };

  return cachedFn as T;
}

/**
 * Cache a function with Next.js unstable_cache
 * This is for server components and server actions
 * @param fn Function to cache
 * @param keyPrefix Cache key prefix
 * @param ttl Time to live in seconds
 * @returns Cached function
 */
export function cacheServerFunction<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  keyPrefix: string,
  ttl: number = CACHE_TTL.MEDIUM
): T {
  return unstable_cache(
    async (...args: Parameters<T>): Promise<ReturnType<T>> => {
      return await fn(...args);
    },
    [`${keyPrefix}`],
    {
      revalidate: ttl,
      tags: [`${keyPrefix}`],
    }
  ) as T;
}

/**
 * Cache a database query with both memory cache and Next.js cache
 * @param queryFn Database query function
 * @param keyPrefix Cache key prefix
 * @param ttl Time to live in seconds
 * @returns Cached query function
 */
export function cacheQuery<T extends (...args: any[]) => Promise<any>>(
  queryFn: T,
  keyPrefix: string,
  ttl: number = CACHE_TTL.MEDIUM
): T {
  // First use memory cache
  const memoryCachedFn = async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    try {
      // Create a cache key based on the function name, prefix, and a safe string representation of args
      // Use a safer approach to create a cache key to avoid circular references
      const safeArgs = args.map(arg => {
        if (typeof arg === 'object' && arg !== null) {
          // For objects, only use primitive properties to avoid circular references
          try {
            // Try to create a safe representation of the object
            const safeObj: any = {};
            for (const key in arg) {
              if (Object.prototype.hasOwnProperty.call(arg, key)) {
                const value = arg[key];
                if (
                  value === null ||
                  typeof value === 'string' ||
                  typeof value === 'number' ||
                  typeof value === 'boolean'
                ) {
                  safeObj[key] = value;
                }
              }
            }
            return safeObj;
          } catch (e) {
            // If there's an error, use a simple string representation
            return `object-${Object.keys(arg).join('-')}`;
          }
        }
        return arg;
      });

      const cacheKey = `${keyPrefix}:${JSON.stringify(safeArgs)}`;

      // Try to get the value from the memory cache first
      const cachedValue = getFromMemoryCache<ReturnType<T>>(cacheKey);
      if (cachedValue !== undefined) {
        return cachedValue;
      }

      // If not in memory cache, execute the function
      const result = await queryFn(...args);

      // Store the result in the memory cache
      setInMemoryCache(cacheKey, result, ttl);

      return result;
    } catch (error) {
      // If there's an error with caching, just execute the function directly
      console.error('Error in cache mechanism:', error);
      return await queryFn(...args);
    }
  };

  // Then use Next.js cache
  return unstable_cache(
    async (...args: Parameters<T>): Promise<ReturnType<T>> => {
      try {
        return await memoryCachedFn(...args);
      } catch (error) {
        // If there's an error with the Next.js cache, just use the memory cache
        console.error('Error in Next.js cache:', error);
        return await queryFn(...args);
      }
    },
    [`${keyPrefix}`],
    {
      revalidate: ttl,
      tags: [`${keyPrefix}`],
    }
  ) as T;
}
