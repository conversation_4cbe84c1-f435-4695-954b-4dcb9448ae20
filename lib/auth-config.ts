/**
 * Authentication configuration
 * This file provides a centralized way to manage authentication settings
 */

// Authentication is always required
export const REQUIRE_AUTH = true

// Check if a real database should be used
export const USE_DATABASE = process.env.USE_DATABASE !== 'false'

// MongoDB connection string
export const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/skills-assessment'

// NextAuth configuration
export const NEXTAUTH_URL = process.env.NEXTAUTH_URL || 'http://localhost:3000'
export const NEXTAUTH_SECRET = process.env.NEXTAUTH_SECRET || 'development-secret-do-not-use-in-production'

// Google OAuth credentials
export const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID || ''
export const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET || ''

// Allowed email domain for authentication
export const ALLOWED_DOMAIN = process.env.ALLOWED_DOMAIN || 'example.com'
