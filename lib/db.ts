import clientPromise, { getCollection, getCachedCollection } from "./mongodb"
import { cache } from "react"
import { serialize } from "./utils/serialize"
import { ObjectId } from "mongodb"

// Database connection promise
let dbPromise: Promise<any>

// Connection timestamp to track connection age
let connectionTimestamp: number = 0
const CONNECTION_MAX_AGE = 3600000 // 1 hour in milliseconds

// Cache for database name extraction
let cachedDbName: string | null = null

/**
 * Extract database name from URI or environment variable
 * This is cached to avoid repeated parsing
 */
function getDbName(): string {
  if (cachedDbName) {
    return cachedDbName
  }

  let dbName = process.env.MONGODB_DB;

  if (!dbName && process.env.MONGODB_URI) {
    try {
      // Parse the URI to extract the database name
      const uriParts = process.env.MONGODB_URI.split('/');
      // The database name is the last part of the URI, before any query parameters
      const dbNameWithParams = uriParts[uriParts.length - 1];
      // Remove any query parameters
      dbName = dbNameWithParams.split('?')[0];
    } catch (error) {
      console.warn('Failed to parse database name from URI');
    }
  }

  // Cache the result
  cachedDbName = dbName || "skills-assessment"
  return cachedDbName
}

/**
 * Get database connection with optimized caching and connection refresh
 * This version is optimized for Atlas connections
 */
export async function getDb() {
  // Check if we need to refresh the connection
  const needsRefresh = !dbPromise ||
    (connectionTimestamp > 0 && Date.now() - connectionTimestamp > CONNECTION_MAX_AGE);

  if (needsRefresh) {
    try {
      const client = await clientPromise
      const dbName = getDbName()

      // Create a new connection
      dbPromise = Promise.resolve(client.db(dbName))
      connectionTimestamp = Date.now()

      // Only log in development to reduce noise in production logs
      if (process.env.NODE_ENV === 'development') {
        console.log(`Connected to MongoDB database: ${dbName}`);
      }
    } catch (error) {
      console.error("Error connecting to MongoDB:", error);
      // If we had a previous connection, keep using it rather than failing
      if (!dbPromise) {
        throw error;
      }
    }
  }

  return dbPromise
}

// Cached version of getDb to reduce redundant database connections
export const getDbCached = cache(getDb)

/**
 * Get a collection by name using the local database connection
 * @param collectionName Collection name
 * @returns MongoDB collection
 */
async function getLocalCollection(collectionName: string) {
  const database = await getDb();
  return database.collection(collectionName);
}

// Cached version of getLocalCollection
const getLocalCollectionCached = cache(getLocalCollection);

// Use the optimized collection getter from mongodb.ts
const getCollectionCached = getCachedCollection;

/**
 * Create a cached version of findOne for a specific collection with projection support
 * @param collectionName Collection name
 * @returns Cached findOne function
 */
export function createCachedFindOne(collectionName: string) {
  const findOne = async (query: any, options?: { projection?: any }) => {
    const collection = await getCollectionCached(collectionName);

    // Use projection if provided to reduce data transfer and improve performance
    let result;
    if (options?.projection) {
      result = await collection.findOne(query, { projection: options.projection });
    } else {
      result = await collection.findOne(query);
    }

    // Serialize the result to convert ObjectIds to strings
    return result ? serialize(result) : null;
  };

  return cache(findOne);
}

/**
 * Create a cached version of find().toArray() for a specific collection
 * @param collectionName Collection name
 * @returns Cached find().toArray() function
 */
export function createCachedFindMany(collectionName: string) {
  const findMany = async (query: any, options?: { sort?: any; limit?: number; skip?: number; projection?: any }) => {
    const collection = await getCollectionCached(collectionName);
    const cursor = collection.find(query);

    if (options?.projection) {
      cursor.project(options.projection);
    }

    if (options?.sort) {
      cursor.sort(options.sort);
    }

    if (options?.skip) {
      cursor.skip(options.skip);
    }

    if (options?.limit) {
      cursor.limit(options.limit);
    }

    // Get the results and serialize them to convert ObjectIds to strings
    const results = await cursor.toArray();
    return serialize(results);
  };

  return cache(findMany);
}

/**
 * Create a cached version of countDocuments for a specific collection
 * @param collectionName Collection name
 * @returns Cached countDocuments function
 */
export function createCachedCountDocuments(collectionName: string) {
  const countDocuments = async (query: any) => {
    // If query contains ObjectId strings, convert them to ObjectId objects
    if (query && typeof query === 'object') {
      // Handle _id specifically
      if (query._id && typeof query._id === 'string' && ObjectId.isValid(query._id)) {
        query._id = new ObjectId(query._id);
      }

      // Handle other potential ObjectId fields
      for (const key in query) {
        if (key.endsWith('Id') && typeof query[key] === 'string' && ObjectId.isValid(query[key])) {
          query[key] = new ObjectId(query[key]);
        }
      }
    }

    const collection = await getCollectionCached(collectionName);
    return collection.countDocuments(query);
  };

  return cache(countDocuments);
}

/**
 * Clear cache for a specific collection
 * @param collectionName Collection name
 */
export function clearCollectionCache(collectionName: string) {
  // No-op since we're using React's cache which doesn't support clearing
  console.log(`Cache clearing not supported for collection: ${collectionName}`);
}

export const db = {
  collection: (name: string) => ({
    findOne: async (query: any, options?: { projection?: any }) => {
      // Use the cached version for read-only operations with projection support
      const cachedFindOne = createCachedFindOne(name);
      const result = await cachedFindOne(query, options);
      // Result is already serialized in createCachedFindOne
      return result;
    },
    find: (query: any) => {
      return {
        project: (projection: any) => {
          return {
            sort: (sort: any) => {
              return {
                skip: (skip: number) => {
                  return {
                    limit: async (limit: number) => {
                      const database = await getDbCached()
                      const cursor = database.collection(name).find(query)
                      if (projection) {
                        cursor.project(projection)
                      }
                      if (sort) {
                        cursor.sort(sort)
                      }
                      const results = await cursor.skip(skip).limit(limit).toArray()
                      return serialize(results)
                    },
                    toArray: async () => {
                      const database = await getDbCached()
                      const cursor = database.collection(name).find(query)
                      if (projection) {
                        cursor.project(projection)
                      }
                      if (sort) {
                        cursor.sort(sort)
                      }
                      const results = await cursor.skip(skip).toArray()
                      return serialize(results)
                    }
                  }
                },
                toArray: async () => {
                  const database = await getDbCached()
                  const cursor = database.collection(name).find(query)
                  if (projection) {
                    cursor.project(projection)
                  }
                  if (sort) {
                    cursor.sort(sort)
                  }
                  const results = await cursor.toArray()
                  return serialize(results)
                }
              }
            },
            toArray: async () => {
              const database = await getDbCached()
              const cursor = database.collection(name).find(query)
              if (projection) {
                cursor.project(projection)
              }
              const results = await cursor.toArray()
              return serialize(results)
            }
          }
        },
        sort: (sort: any) => {
          return {
            skip: (skip: number) => {
              return {
                limit: async (limit: number) => {
                  const database = await getDbCached()
                  const cursor = database.collection(name).find(query)
                  cursor.sort(sort)
                  const results = await cursor.skip(skip).limit(limit).toArray()
                  return serialize(results)
                },
                toArray: async () => {
                  const database = await getDbCached()
                  const cursor = database.collection(name).find(query)
                  cursor.sort(sort)
                  const results = await cursor.skip(skip).toArray()
                  return serialize(results)
                }
              }
            },
            toArray: async () => {
              const database = await getDbCached()
              const cursor = database.collection(name).find(query)
              cursor.sort(sort)
              const results = await cursor.toArray()
              return serialize(results)
            }
          }
        },
        skip: (skip: number) => {
          return {
            limit: async (limit: number) => {
              const database = await getDbCached()
              const cursor = database.collection(name).find(query)
              const results = await cursor.skip(skip).limit(limit).toArray()
              return serialize(results)
            },
            toArray: async () => {
              const database = await getDbCached()
              const cursor = database.collection(name).find(query)
              const results = await cursor.skip(skip).toArray()
              return serialize(results)
            }
          }
        },
        limit: async (limit: number) => {
          const database = await getDbCached()
          const cursor = database.collection(name).find(query)
          const results = await cursor.limit(limit).toArray()
          return serialize(results)
        },
        toArray: async () => {
          // Use the cached version for read-only operations
          const cachedFindMany = createCachedFindMany(name);
          const results = await cachedFindMany(query);
          // Results are already serialized in createCachedFindMany
          return results;
        }
      }
    },
    insertOne: async (doc: any) => {
      const database = await getDbCached()
      const result = await database.collection(name).insertOne(doc)
      // Clear cache for this collection
      clearCollectionCache(name)
      return result
    },
    updateOne: async (query: any, update: any) => {
      const database = await getDbCached()
      const result = await database.collection(name).updateOne(query, update)
      // Clear cache for this collection
      clearCollectionCache(name)
      return result
    },
    deleteOne: async (query: any) => {
      const database = await getDbCached()
      const result = await database.collection(name).deleteOne(query)
      // Clear cache for this collection
      clearCollectionCache(name)
      return result
    },
    countDocuments: async (query: any) => {
      // Use the cached version for read-only operations
      const cachedCountDocuments = createCachedCountDocuments(name);
      return cachedCountDocuments(query);
    },
  }),
}
