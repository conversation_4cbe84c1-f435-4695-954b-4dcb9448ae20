/**
 * Application configuration
 *
 * This file contains configuration settings that are loaded from environment variables.
 * Default values are provided for development, but should be overridden in production.
 */

// Database configuration
export const dbConfig = {
  // Whether to use the database or mock data
  useDatabase: process.env.USE_DATABASE === 'true',
  // MongoDB connection string
  mongodbUri: process.env.MONGODB_URI || 'mongodb://localhost:27017/skills-assessment',
}

// Authentication configuration
export const authConfig = {
  // Whether authentication is required
  requireAuth: process.env.REQUIRE_AUTH !== 'false',
  // Allowed email domain for authentication
  allowedDomain: process.env.ALLOWED_DOMAIN || '',
  // Admin email addresses
  adminEmails: (process.env.ADMIN_EMAILS || '<EMAIL>').split(',').map(email => email.trim()),
  // Check if a user is an admin
  isAdmin: (email: string) => {
    if (!email) return false;

    // For development, allow any email to be admin if no specific admins are configured
    if (process.env.NODE_ENV !== 'production' && (!process.env.ADMIN_EMAILS || process.env.ADMIN_EMAILS === '')) {
      console.log('Development mode: All users are admins');
      return true;
    }

    // Use the adminEmails array that's already defined above
    return authConfig.adminEmails.includes(email);
  }
}

// Assessment restrictions configuration
export const assessmentConfig = {
  // Number of months between assessments (0 = no restriction)
  intervalMonths: parseInt(process.env.ASSESSMENT_INTERVAL_MONTHS || '6', 10),
  // Whether to allow updating existing assessments
  allowUpdates: process.env.ALLOW_ASSESSMENT_UPDATES === 'true',
}
