"use server"

import { revalidate<PERSON>ath } from "next/cache"
import clientPromise from "./mongodb"
import type { skillsData } from "./skills-data"

export interface AssessmentData {
  userId: string
  name: string
  date: string
  skills: typeof skillsData
}

export async function saveAssessment(data: AssessmentData) {
  try {
    const client = await clientPromise
    const db = client.db("skills-assessment")

    // Check if assessment with this name already exists
    const existingAssessment = await db.collection("assessments").findOne({
      userId: data.userId,
      name: data.name,
    })

    if (existingAssessment) {
      // Update existing assessment
      await db.collection("assessments").updateOne(
        { userId: data.userId, name: data.name },
        {
          $set: {
            skills: data.skills,
            updatedAt: new Date(),
          },
          // Add to history array using $push with proper typing
          $push: {
            history: {
              $each: [{
                date: new Date(),
                skills: data.skills,
              }]
            },
          } as any,
        },
      )
      revalidatePath("/")
      return { success: true, message: "Assessment updated successfully" }
    } else {
      // Create new assessment
      await db.collection("assessments").insertOne({
        userId: data.userId,
        name: data.name,
        skills: data.skills,
        createdAt: new Date(),
        updatedAt: new Date(),
        history: [
          {
            date: new Date(),
            skills: data.skills,
          },
        ],
      })
      revalidatePath("/")
      return { success: true, message: "Assessment saved successfully" }
    }
  } catch (error) {
    console.error("Error saving assessment:", error)
    return { success: false, message: "Failed to save assessment" }
  }
}

export async function getUserAssessments(userId: string) {
  try {
    const client = await clientPromise
    const db = client.db("skills-assessment")

    const assessments = await db
      .collection("assessments")
      .find({ userId })
      .project({ name: 1, updatedAt: 1 })
      .sort({ updatedAt: -1 })
      .toArray()

    return {
      success: true,
      data: assessments.map((a) => ({
        name: a.name,
        date: a.updatedAt.toISOString(),
      })),
    }
  } catch (error) {
    console.error("Error fetching user assessments:", error)
    return { success: false, data: [], message: "Failed to fetch assessments" }
  }
}

export async function loadAssessment(userId: string, name: string) {
  try {
    const client = await clientPromise
    const db = client.db("skills-assessment")

    const assessment = await db.collection("assessments").findOne({
      userId,
      name,
    })

    if (!assessment) {
      return { success: false, message: "Assessment not found" }
    }

    return {
      success: true,
      data: {
        skills: assessment.skills,
        history: assessment.history || [],
        createdAt: assessment.createdAt,
        updatedAt: assessment.updatedAt,
      },
    }
  } catch (error) {
    console.error("Error loading assessment:", error)
    return { success: false, message: "Failed to load assessment" }
  }
}

export async function getSkillHistory(userId: string, name: string, skillId: string) {
  try {
    const client = await clientPromise
    const db = client.db("skills-assessment")

    const assessment = await db.collection("assessments").findOne({
      userId,
      name,
    })

    if (!assessment || !assessment.history) {
      return { success: false, data: [], message: "No history found" }
    }

    // Define types for history entries
    interface HistoryEntry {
      date: Date;
      skills: Array<{
        id: string;
        currentLevel: number | null;
        [key: string]: any;
      }>;
    }

    interface SkillHistoryEntry {
      date: Date;
      level: number | null;
    }

    // Extract the specific skill's history
    const skillHistory = assessment.history
      .map((entry: HistoryEntry) => ({
        date: entry.date,
        level: entry.skills.find((s: { id: string }) => s.id === skillId)?.currentLevel || null,
      }))
      .filter((entry: SkillHistoryEntry) => entry.level !== null)

    return { success: true, data: skillHistory }
  } catch (error) {
    console.error("Error fetching skill history:", error)
    return { success: false, data: [], message: "Failed to fetch skill history" }
  }
}

export async function getAllSkillsHistory(userId: string, name: string) {
  try {
    const client = await clientPromise
    const db = client.db("skills-assessment")

    const assessment = await db.collection("assessments").findOne({
      userId,
      name,
    })

    if (!assessment || !assessment.history) {
      return { success: false, data: [], message: "No history found" }
    }

    // Define types for history entries
    interface HistoryEntry {
      date: Date;
      skills: Array<{
        id: string;
        currentLevel: number | null;
        [key: string]: any;
      }>;
    }

    // Format the history data for visualization
    const historyData = assessment.history.map((entry: HistoryEntry) => ({
      date: entry.date,
      skills: entry.skills.reduce((acc: Record<string, number>, skill: { id: string; currentLevel: number | null }) => {
        if (skill.currentLevel) {
          acc[skill.id] = skill.currentLevel
        }
        return acc
      }, {}),
    }))

    return { success: true, data: historyData }
  } catch (error) {
    console.error("Error fetching skills history:", error)
    return { success: false, data: [], message: "Failed to fetch skills history" }
  }
}
