import clientPromise from './mongodb';

/**
 * Create an index safely, ignoring errors if the index already exists
 * @param collection MongoDB collection
 * @param keys Index keys
 * @param options Index options
 */
async function createIndexSafely(collection: any, keys: any, options: any = {}) {
  try {
    await collection.createIndex(keys, options);
    return true;
  } catch (error: any) {
    // Ignore errors if the index already exists
    if (error.code === 85 || error.codeName === 'IndexOptionsConflict') {
      console.log(`Index already exists for ${JSON.stringify(keys)}`);
      return true;
    }
    console.error(`Error creating index for ${JSON.stringify(keys)}:`, error);
    return false;
  }
}

/**
 * Create indexes for commonly queried collections to improve performance
 * This should be called during application startup
 */
export async function createDatabaseIndexes() {
  try {
    console.log('Creating database indexes...');
    const client = await clientPromise;
    const db = client.db();

    // Create indexes for user_profiles collection
    await createIndexSafely(db.collection('user_profiles'), { email: 1 }, { unique: true });
    await createIndexSafely(db.collection('user_profiles'), { managerEmail: 1 });
    await createIndexSafely(db.collection('user_profiles'), { businessUnit: 1 });
    await createIndexSafely(db.collection('user_profiles'), { careerLevel: 1 });
    await createIndexSafely(db.collection('user_profiles'), { jobRole: 1 });
    await createIndexSafely(db.collection('user_profiles'), { createdAt: -1 });
    await createIndexSafely(db.collection('user_profiles'), { businessUnit: 1, jobRole: 1 });
    console.log('Created indexes for user_profiles collection');

    // Create indexes for assessment_templates collection
    await createIndexSafely(db.collection('assessment_templates'), { businessUnit: 1 });
    await createIndexSafely(db.collection('assessment_templates'), { templateType: 1 });
    await createIndexSafely(db.collection('assessment_templates'), { businessUnit: 1, templateType: 1 });
    console.log('Created indexes for assessment_templates collection');

    // Create indexes for assessment_cycles collection
    await createIndexSafely(db.collection('assessment_cycles'), { name: 1 });
    await createIndexSafely(db.collection('assessment_cycles'), { status: 1 });
    await createIndexSafely(db.collection('assessment_cycles'), { startDate: 1 });
    await createIndexSafely(db.collection('assessment_cycles'), { endDate: 1 });
    await createIndexSafely(db.collection('assessment_cycles'), { status: 1, startDate: 1 });
    console.log('Created indexes for assessment_cycles collection');

    // One-time migration: Fix assessment cycles without status field
    const cyclesWithoutStatus = await db.collection('assessment-cycles').countDocuments({
      status: { $exists: false }
    });

    if (cyclesWithoutStatus > 0) {
      console.log(`Migrating ${cyclesWithoutStatus} assessment cycles without status field...`);
      await db.collection('assessment-cycles').updateMany(
        { status: { $exists: false } },
        {
          $set: {
            status: 'active',
            updatedAt: new Date()
          }
        }
      );
      console.log('Assessment cycle migration completed');
    }

    // Create indexes for assessments collection
    await createIndexSafely(db.collection('assessments'), { userId: 1 });
    await createIndexSafely(db.collection('assessments'), { assessorId: 1 });
    await createIndexSafely(db.collection('assessments'), { cycleId: 1 });
    await createIndexSafely(db.collection('assessments'), { status: 1 });
    await createIndexSafely(db.collection('assessments'), { userId: 1, cycleId: 1 });
    await createIndexSafely(db.collection('assessments'), { assessorId: 1, cycleId: 1 });
    console.log('Created indexes for assessments collection');

    // Create indexes for projects collection
    await createIndexSafely(db.collection('projects'), { name: 1 });
    await createIndexSafely(db.collection('projects'), { status: 1 });
    console.log('Created indexes for projects collection');

    // Create indexes for project_members collection
    await createIndexSafely(db.collection('project_members'), { projectId: 1 });
    await createIndexSafely(db.collection('project_members'), { userId: 1 });
    await createIndexSafely(db.collection('project_members'), { projectId: 1, userId: 1 }, { unique: true });
    console.log('Created indexes for project_members collection');

    console.log('All database indexes created successfully');
    return true;
  } catch (error) {
    console.error('Error creating database indexes:', error);
    return false;
  }
}
