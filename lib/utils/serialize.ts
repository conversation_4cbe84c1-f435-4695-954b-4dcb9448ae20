import { ObjectId } from 'mongodb';

/**
 * Checks if a value is a MongoDB ObjectId
 * @param value Value to check
 * @returns True if the value is an ObjectId
 */
export function isObjectId(value: any): boolean {
  return value instanceof ObjectId ||
    (value && typeof value === 'object' && value._bsontype === 'ObjectID');
}

/**
 * Checks if a value is a Date object
 * @param value Value to check
 * @returns True if the value is a Date
 */
export function isDate(value: any): boolean {
  return value instanceof Date;
}

/**
 * Serializes a MongoDB document or array of documents for use in client components
 * Converts ObjectId to string and handles nested objects and arrays
 * @param doc MongoDB document or array of documents
 * @returns Serialized document or array of documents
 */
export function serializeDocument<T>(doc: T): T {
  if (doc === null || doc === undefined) {
    return doc;
  }

  // Handle arrays
  if (Array.isArray(doc)) {
    return doc.map(item => serializeDocument(item)) as unknown as T;
  }

  // Handle ObjectId
  if (isObjectId(doc)) {
    return doc.toString() as unknown as T;
  }

  // Handle Date
  if (isDate(doc)) {
    return (doc as unknown as Date).toISOString() as unknown as T;
  }

  // Handle objects (but not Buffer or other special types)
  if (typeof doc === 'object' && doc !== null && !Buffer.isBuffer(doc)) {
    const serialized: Record<string, any> = {};

    for (const [key, value] of Object.entries(doc)) {
      // Skip functions and symbols
      if (typeof value === 'function' || typeof value === 'symbol') {
        continue;
      }

      // Handle _id specially to ensure it's always serialized
      if (key === '_id' && isObjectId(value)) {
        serialized[key] = value.toString();
      } else {
        serialized[key] = serializeDocument(value);
      }
    }

    return serialized as unknown as T;
  }

  // Return primitive values and other types as is
  return doc;
}

/**
 * Serializes a MongoDB document or array of documents for use in client components
 * This is a wrapper around serializeDocument that ensures the result is safe for client components
 * @param doc MongoDB document or array of documents
 * @returns Serialized document or array of documents
 */
export function serialize<T>(doc: T): T {
  try {
    return serializeDocument(doc);
  } catch (error) {
    console.error('Error serializing document:', error);
    // If serialization fails, try a simpler approach with JSON
    try {
      return JSON.parse(JSON.stringify(doc));
    } catch (jsonError) {
      console.error('Error JSON serializing document:', jsonError);
      throw new Error('Failed to serialize document for client component');
    }
  }
}
