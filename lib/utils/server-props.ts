import { serialize } from './serialize';

/**
 * Safely serialize data for passing from Server Components to Client Components
 * This is a wrapper around the serialize function that ensures the result is safe
 * @param data Data to serialize
 * @returns Serialized data safe for client components
 */
export function serializeForClientComponent<T>(data: T): T {
  try {
    return serialize(data);
  } catch (error) {
    console.error('Error serializing data for client component:', error);
    
    // If serialization fails, try a simpler approach with JSON
    try {
      return JSON.parse(JSON.stringify(data));
    } catch (jsonError) {
      console.error('Error JSON serializing data for client component:', jsonError);
      throw new Error('Failed to serialize data for client component');
    }
  }
}

/**
 * Create props for a client component from server data
 * This ensures that all data is properly serialized for client components
 * @param props Props object to serialize
 * @returns Serialized props safe for client components
 */
export function createClientProps<T extends Record<string, any>>(props: T): T {
  const serializedProps: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(props)) {
    serializedProps[key] = serializeForClientComponent(value);
  }
  
  return serializedProps as T;
}
