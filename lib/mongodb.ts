import 'server-only';
import { Collection, Document, MongoClient } from "mongodb"

// Function to get MongoDB URI with validation
const getMongoUri = () => {
  if (!process.env.MONGODB_URI) {
    throw new Error("Please add your MongoDB URI to .env.local")
  }
  return process.env.MONGODB_URI;
};

// Function to check if URI is an SRV connection string (MongoDB Atlas)
const isSrvUri = (uri: string): boolean => {
  return uri.includes('mongodb+srv://');
};

// Function to get MongoDB URI with minimal options for better performance
const getEnhancedUri = () => {
  // Just use the raw URI for better performance
  return getMongoUri();
};

// Get connection options based on the URI type
const getConnectionOptions = (uri: string) => {
  // Base options for all connections
  const baseOptions = {
    // Use minimal options for better performance
    family: 4, // Use IPv4, skip trying IPv6
    connectTimeoutMS: 5000, // Keep connection attempts fast
    socketTimeoutMS: 10000, // Keep socket operations fast
    serverSelectionTimeoutMS: 5000, // Keep server selection fast
    maxPoolSize: 5, // Reduced pool size for M3 Mac
    minPoolSize: 0, // No minimum to reduce overhead
    maxIdleTimeMS: 60000, // 1 minute idle time
    waitQueueTimeoutMS: 5000, // Fast queue timeout
    retryWrites: true, // Enable retry for write operations
    retryReads: true, // Enable retry for read operations
    // Disable monitoring to reduce overhead
    monitorCommands: false
  };

  // Check if using SRV URI (MongoDB Atlas)
  if (isSrvUri(uri)) {
    // For MongoDB Atlas, don't use directConnection
    return baseOptions;
  } else {
    // For local/direct MongoDB, use directConnection
    return {
      ...baseOptions,
      directConnection: true
    };
  }
};

// Lazy initialization of MongoDB client
let client: MongoClient;
let clientPromise: Promise<MongoClient>;

// Function to initialize the MongoDB client with ultra-simplified connection handling
const initMongoClient = () => {
  try {
    const uri = getEnhancedUri();
    // Get the appropriate connection options based on the URI type
    const connectionOptions = getConnectionOptions(uri);

    // Always use a global variable in development to preserve connection
    // across hot module reloads
    if (process.env.NODE_ENV === "development") {
      const globalWithMongo = global as typeof globalThis & {
        _mongoClientPromise?: Promise<MongoClient>
      }

      // Only create a new connection if one doesn't exist
      if (!globalWithMongo._mongoClientPromise) {
        // Create a new client with appropriate options
        client = new MongoClient(uri, connectionOptions);
        globalWithMongo._mongoClientPromise = client.connect();
      }

      clientPromise = globalWithMongo._mongoClientPromise;
    } else {
      // In production mode, it's best to not use a global variable.
      client = new MongoClient(uri, connectionOptions);
      clientPromise = client.connect();
    }

    return clientPromise;
  } catch (error) {
    console.error('Error initializing MongoDB client:', error);
    throw error;
  }
};

// Initialize the client promise
try {
  clientPromise = initMongoClient();
} catch (error) {
  // Handle initialization error (will be thrown again when used)
  console.error('Failed to initialize MongoDB client:', error);
  // Create a rejected promise to be returned when the client is accessed
  clientPromise = Promise.reject(error);
}

// Export a module-scoped MongoClient promise. By doing this in a
// separate module, the client can be shared across functions.
export default clientPromise

// Cache the database name to avoid repeated parsing
let cachedDbName: string | null = null;

/**
 * Get the database name from environment or connection string
 * @returns Database name
 */
function getDatabaseName(): string {
  if (cachedDbName) {
    return cachedDbName;
  }

  let dbName = process.env.MONGODB_DB;

  if (!dbName && process.env.MONGODB_URI) {
    try {
      // Parse the URI to extract the database name
      const uriParts = process.env.MONGODB_URI.split('/');
      // The database name is the last part of the URI, before any query parameters
      const dbNameWithParams = uriParts[uriParts.length - 1];
      // Remove any query parameters
      dbName = dbNameWithParams.split('?')[0];
    } catch (error) {
      console.warn('Failed to parse database name from URI');
    }
  }

  // Use a default database name if none is found
  cachedDbName = dbName || 'skills-assessment';
  return cachedDbName;
}

/**
 * Get a MongoDB collection with optimized caching
 * @param collectionName Name of the collection
 * @returns Collection object
 */
export async function getCollection<T extends Document>(collectionName: string): Promise<Collection<T>> {
  const client = await clientPromise;
  const dbName = getDatabaseName();
  const db = client.db(dbName);
  return db.collection<T>(collectionName);
}

// Global cache for collection objects to avoid repeated lookups
const collectionCache: Record<string, Collection<any>> = {};

/**
 * Get a MongoDB collection with caching for better performance
 * @param collectionName Name of the collection
 * @returns Collection object
 */
export async function getCachedCollection<T extends Document>(collectionName: string): Promise<Collection<T>> {
  // Check if the collection is already in the cache
  if (collectionCache[collectionName]) {
    return collectionCache[collectionName] as Collection<T>;
  }

  // Get the collection
  const collection = await getCollection<T>(collectionName);

  // Cache the collection
  collectionCache[collectionName] = collection;

  return collection;
}
