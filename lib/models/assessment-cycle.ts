import { ObjectId } from 'mongodb';

/**
 * Assessment relationship types
 */
export type AssessmentRelationType = 'self' | 'manager' | 'peer';

/**
 * Assessment status types
 */
export type AssessmentStatus = 'pending' | 'in_progress' | 'completed';

/**
 * Assessment cycle status
 */
export type CycleStatus = 'draft' | 'active' | 'completed';

/**
 * Peer reviewer assignment
 */
export interface PeerReviewer {
  userId: string;
  name: string;
  email: string;
  status: AssessmentStatus;
}

/**
 * Team member in an assessment cycle
 */
export interface CycleTeamMember {
  userId: string;
  name: string;
  email: string;
  businessUnit: string;
  careerLevel: string;
  assessments: {
    self: AssessmentStatus;
    manager: {
      userId: string;
      name: string;
      email: string;
      status: AssessmentStatus;
    };
    peers: PeerReviewer[];
  };
}

/**
 * Project reference in assessment cycle
 */
export interface CycleProject {
  projectId: string;
  name: string;
  description?: string;
  businessUnit?: string;
}

/**
 * Assessment cycle model
 */
export interface AssessmentCycle {
  _id?: ObjectId;
  name: string;
  description?: string;
  businessUnit?: string;
  startDate: Date;
  endDate: Date;
  status: CycleStatus;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  updatedBy?: string;
  assessmentTypes: {
    self: boolean;
    manager: boolean;
    peer: boolean;
    peerReviewsPerUser: number;
  };
  projects: CycleProject[]; // Projects included in this assessment cycle
  teamMembers: CycleTeamMember[];
  // Interval settings
  isRecurring?: boolean;
  intervalMonths?: number; // Number of months between recurring cycles
  lastAssessmentDate?: Date; // Date of the last assessment for each user
}

/**
 * Assessment model with relationship type
 */
export interface Assessment {
  _id?: ObjectId;
  cycleId: string;
  userId: string;           // Person being assessed
  reviewerId: string;       // Person doing the assessment
  relationshipType: AssessmentRelationType;
  name: string;
  date: string;
  skills: any[];            // Core skills assessment
  projectSkills: any[];     // Project skills assessment
  status: AssessmentStatus;
  feedback?: string;        // Written feedback
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Serialized assessment cycle for client
 */
export interface SerializedAssessmentCycle {
  id: string;
  name: string;
  description?: string;
  businessUnit?: string;
  startDate: string;
  endDate: string;
  status: CycleStatus;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  updatedBy?: string;
  assessmentTypes: {
    self: boolean;
    manager: boolean;
    peer: boolean;
    peerReviewsPerUser: number;
  };
  projects: CycleProject[]; // Projects included in this assessment cycle
  teamMembers: CycleTeamMember[];
  completionRate: number;
  daysRemaining: number;
  // Interval settings
  isRecurring?: boolean;
  intervalMonths?: number;
  lastAssessmentDate?: string;
  nextAssessmentDate?: string; // Calculated based on lastAssessmentDate and intervalMonths
  canTakeAssessment?: boolean; // Whether the user can take an assessment based on the interval
}

/**
 * Serialize an assessment cycle for client-side use
 */
export function serializeAssessmentCycle(cycle: AssessmentCycle): SerializedAssessmentCycle {
  // Calculate completion rate
  let totalAssessments = 0;
  let completedAssessments = 0;

  cycle.teamMembers.forEach(member => {
    if (cycle.assessmentTypes.self) {
      totalAssessments++;
      if (member.assessments.self === 'completed') {
        completedAssessments++;
      }
    }

    if (cycle.assessmentTypes.manager) {
      totalAssessments++;
      if (member.assessments.manager.status === 'completed') {
        completedAssessments++;
      }
    }

    if (cycle.assessmentTypes.peer) {
      member.assessments.peers.forEach(peer => {
        totalAssessments++;
        if (peer.status === 'completed') {
          completedAssessments++;
        }
      });
    }
  });

  const completionRate = totalAssessments > 0
    ? Math.round((completedAssessments / totalAssessments) * 100)
    : 0;

  // Calculate days remaining
  const now = new Date();
  const endDate = new Date(cycle.endDate);
  const daysRemaining = Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));

  // Calculate next assessment date if applicable
  let nextAssessmentDate = null;
  let canTakeAssessment = true;

  if (cycle.isRecurring && cycle.lastAssessmentDate && cycle.intervalMonths) {
    const lastDate = new Date(cycle.lastAssessmentDate);
    nextAssessmentDate = new Date(lastDate);
    nextAssessmentDate.setMonth(lastDate.getMonth() + cycle.intervalMonths);

    // Check if the next assessment date is in the future
    canTakeAssessment = nextAssessmentDate <= new Date();
  }

  return {
    id: cycle._id ? cycle._id.toString() : '',
    name: cycle.name,
    description: cycle.description,
    businessUnit: cycle.businessUnit,
    startDate: cycle.startDate.toISOString(),
    endDate: cycle.endDate.toISOString(),
    status: cycle.status,
    createdBy: cycle.createdBy,
    createdAt: cycle.createdAt.toISOString(),
    updatedAt: cycle.updatedAt.toISOString(),
    updatedBy: cycle.updatedBy,
    assessmentTypes: cycle.assessmentTypes,
    projects: cycle.projects || [], // Include projects or empty array if not defined
    teamMembers: cycle.teamMembers,
    completionRate,
    daysRemaining,
    // Interval settings
    isRecurring: cycle.isRecurring || false,
    intervalMonths: cycle.intervalMonths || 0,
    lastAssessmentDate: cycle.lastAssessmentDate ? cycle.lastAssessmentDate.toISOString() : undefined,
    nextAssessmentDate: nextAssessmentDate ? nextAssessmentDate.toISOString() : undefined,
    canTakeAssessment
  };
}

/**
 * Serialize an assessment for client-side use
 */
export function serializeAssessment(assessment: any) {
  return {
    id: assessment._id ? assessment._id.toString() : '',
    cycleId: assessment.cycleId,
    userId: assessment.userId,
    reviewerId: assessment.reviewerId,
    relationshipType: assessment.relationshipType,
    name: assessment.name,
    date: assessment.date,
    skills: assessment.skills || [],
    projectSkills: assessment.projectSkills || [],
    status: assessment.status,
    feedback: assessment.feedback,
    createdAt: assessment.createdAt ? assessment.createdAt.toISOString() : new Date().toISOString(),
    updatedAt: assessment.updatedAt ? assessment.updatedAt.toISOString() : new Date().toISOString()
  };
}
