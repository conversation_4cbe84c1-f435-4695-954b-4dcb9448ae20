import { ObjectId } from 'mongodb';
import type { ProjectSkill } from '@/lib/project-skills-data';

/**
 * Project Skill Template
 * Stores the skill templates for specific projects
 */
export interface ProjectSkillTemplate {
  _id?: ObjectId;
  projectId: string;
  projectName: string;
  businessUnit: string;
  skills: ProjectSkill[];
  updatedAt: Date;
  updatedBy: string; // Admin who last updated
}

/**
 * Serialized Project Skill Template for client-side use
 */
export interface SerializedProjectSkillTemplate {
  id: string;
  projectId: string;
  projectName: string;
  businessUnit: string;
  skills: ProjectSkill[];
  updatedAt: string;
  updatedBy: string;
}

/**
 * Serialize a project skill template for client-side use
 */
export function serializeProjectSkillTemplate(template: any): SerializedProjectSkillTemplate {
  if (!template) {
    // Return a default empty template if null
    return {
      id: '',
      projectId: '',
      projectName: '',
      businessUnit: '',
      skills: [],
      updatedAt: new Date().toISOString(),
      updatedBy: 'system',
    };
  }

  return {
    id: template._id ? template._id.toString() : '',
    projectId: template.projectId || '',
    projectName: template.projectName || '',
    businessUnit: template.businessUnit || '',
    skills: template.skills || [],
    updatedAt: template.updatedAt ? template.updatedAt.toISOString() : new Date().toISOString(),
    updatedBy: template.updatedBy || 'system',
  };
}
