import { ObjectId } from 'mongodb';

export interface ConfigDocument {
  _id?: ObjectId;
  key: string;
  value: any;
  description?: string;
  updatedAt: Date;
}

// Configuration keys
export const CONFIG_KEYS = {
  ADMIN_EMAILS: 'admin_emails',
  ASSESSMENT_INTERVAL_MONTHS: 'assessment_interval_months',
  ALLOW_ASSESSMENT_UPDATES: 'allow_assessment_updates',
};

// Default configuration values
export const DEFAULT_CONFIG = {
  [CONFIG_KEYS.ADMIN_EMAILS]: [],
  [CONFIG_KEYS.ASSESSMENT_INTERVAL_MONTHS]: 6,
  [CONFIG_KEYS.ALLOW_ASSESSMENT_UPDATES]: false,
};
