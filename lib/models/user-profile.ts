import { ObjectId } from "mongodb";

/**
 * User's project assignment
 */
export interface UserProject {
  projectId: string;
  projectName: string;
}

/**
 * User profile model
 */
export interface UserProfile {
  _id?: ObjectId;
  email: string
  name: string
  businessUnit: string
  careerLevel: string
  jobRole: string
  managerEmail?: string
  profileCompleted: boolean
  hasLoggedIn?: boolean
  lastLogin?: Date
  createdBy?: string // 'admin' or 'self'
  projects?: UserProject[] // Projects the user is assigned to
  createdAt: Date
  updatedAt: Date
}

export function serializeUserProfile(user: any): any {
  if (!user) return null;

  return {
    id: user._id ? user._id.toString() : null,
    email: user.email,
    name: user.name,
    // Ensure these fields are properly serialized and not undefined
    businessUnit: user.businessUnit || "",
    careerLevel: user.careerLevel || "",
    jobRole: user.jobRole || "",
    managerEmail: user.managerEmail || "",
    profileCompleted: user.profileCompleted,
    hasLoggedIn: user.hasLoggedIn,
    lastLogin: user.lastLogin ? user.lastLogin.toISOString() : null,
    createdBy: user.createdBy,
    projects: user.projects || [],
    createdAt: user.createdAt ? user.createdAt.toISOString() : null,
    updatedAt: user.updatedAt ? user.updatedAt.toISOString() : null,
  };
}

export const businessUnitOptions = ["web", "mobile", "data", "cloud", "AI", "qa"]
export const careerLevelOptions = ["cl2", "cl3", "cl4", "tm1", "tm2", "tm3", "tm4"]
