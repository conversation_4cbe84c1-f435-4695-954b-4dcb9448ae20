import { ObjectId } from 'mongodb';
import type { Skill } from '@/lib/skills-data';
import type { ProjectSkill } from '@/lib/project-skills-data';

/**
 * Business Unit Skill Template
 * Stores the skill templates for each business unit
 */
export interface BUSkillTemplate {
  _id?: ObjectId;
  businessUnit: string; // "web", "mobile", "cloud", "data", "ai", "qa"
  name: string; // Display name for the business unit
  coreSkills: Skill[];
  projectSkills: ProjectSkill[];
  projectName?: string; // Name of the project for project skills
  updatedAt: Date;
  updatedBy: string; // Admin who last updated
}

/**
 * Business Unit options - Consolidated engineering units into IC framework
 */
export const businessUnitOptions = ["engineering-ic", "qa"];

/**
 * Business Unit display names
 */
export const businessUnitNames: Record<string, string> = {
  "engineering-ic": "Engineering (IC)",
  qa: "Quality Assurance"
};

/**
 * Legacy business unit mapping for migration purposes
 */
export const legacyBusinessUnitMapping: Record<string, string> = {
  web: "engineering-ic",
  mobile: "engineering-ic",
  cloud: "engineering-ic",
  data: "engineering-ic",
  ai: "engineering-ic",
  qa: "qa"
};

/**
 * Convert a BUSkillTemplate for client-side use
 * This converts ObjectId to string and handles dates
 */
export function serializeBUSkillTemplate(template: BUSkillTemplate): any {
  return {
    id: template._id ? template._id.toString() : undefined,
    businessUnit: template.businessUnit,
    name: template.name,
    coreSkills: template.coreSkills,
    projectSkills: template.projectSkills,
    projectName: template.projectName || '',
    updatedAt: template.updatedAt ? template.updatedAt.toISOString() : null,
    updatedBy: template.updatedBy
  };
}
