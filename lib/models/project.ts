import { ObjectId } from 'mongodb';

/**
 * Project status types
 */
export type ProjectStatus = 'active' | 'completed' | 'planned';

/**
 * Project model
 */
export interface Project {
  _id?: ObjectId;
  name: string;
  description: string;
  businessUnit: string;
  status: ProjectStatus;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  updatedBy?: string;
}

/**
 * Serialized Project for client-side use
 */
export interface SerializedProject {
  id: string;
  name: string;
  description: string;
  businessUnit: string;
  status: ProjectStatus;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  updatedBy?: string;
}

/**
 * Serialize a project for client-side use
 */
export function serializeProject(project: any): SerializedProject {
  if (!project) {
    // Return a default empty project if null
    return {
      id: '',
      name: '',
      description: '',
      businessUnit: '',
      status: 'planned',
      createdBy: '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  return {
    id: project._id ? project._id.toString() : '',
    name: project.name || '',
    description: project.description || '',
    businessUnit: project.businessUnit || '',
    status: project.status || 'planned',
    createdBy: project.createdBy || '',
    createdAt: project.createdAt ? project.createdAt.toISOString() : new Date().toISOString(),
    updatedAt: project.updatedAt ? project.updatedAt.toISOString() : new Date().toISOString(),
    updatedBy: project.updatedBy,
  };
}

/**
 * Project status options
 */
export const projectStatusOptions: ProjectStatus[] = ['active', 'completed', 'planned'];
