/**
 * Types for the admin dashboard data
 */

export interface SkillSummary {
  id: string;
  category: string;
  average: number;
}

export interface UserSummary {
  id: string;
  name: string;
  email: string;
  businessUnit: string;
  careerLevel: string;
  averageSkillLevel: number;
  topSkill?: {
    id: string;
    category: string;
    level: number;
  };
}

export interface AdminDashboardData {
  // Basic stats
  totalAssessments: number;
  totalUsers: number;

  // User stats
  activeUsers: number;
  inactiveUsers: number;
  adminCreatedUsers: number;
  selfRegisteredUsers: number;

  // Skill data
  skillAverages: Record<string, number>;
  topSkills: SkillSummary[];
  improvementAreas: SkillSummary[];

  // User performance data
  topUsers: UserSummary[];
  recentlyActiveUsers: UserSummary[];

  // Distribution data
  businessUnitBreakdown: Record<string, number>;
  careerLevelBreakdown: Record<string, number>;

  // Detailed breakdowns
  skillsByBusinessUnit: Record<string, Record<string, number>>;
  skillsByCareerLevel: Record<string, Record<string, number>>;
}

export interface AdminDashboardResponse {
  success: boolean;
  message?: string;
  data: AdminDashboardData;
}
