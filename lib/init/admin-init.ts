'use server'

import { getAdminEmails, addAdminEmail } from '@/lib/services/config-service';

/**
 * Initialize admin emails from environment variables
 * This should be called during application startup
 */
export async function initializeAdminEmails(): Promise<{
  success: boolean;
  message: string;
  initialAdmins: string[];
  existingAdmins: string[];
  updatedAdmins: string[];
}> {
  try {
    // Get existing admin emails from the database
    const existingAdmins = await getAdminEmails();
    
    // Get admin emails from environment variables
    const envAdmins = (process.env.ADMIN_EMAILS || '').split(',')
      .map(email => email.trim())
      .filter(email => email);
    
    // Add admin emails from environment variables if they don't exist
    const results = await Promise.all(
      envAdmins.map(async (email) => {
        if (!existingAdmins.includes(email)) {
          return await addAdminEmail(email);
        }
        return true;
      })
    );
    
    // Get updated admin emails
    const updatedAdmins = await getAdminEmails();
    
    return {
      success: true,
      message: 'Admin emails initialized',
      initialAdmins: envAdmins,
      existingAdmins,
      updatedAdmins,
    };
  } catch (error) {
    console.error('Error initializing admin emails:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : String(error),
      initialAdmins: [],
      existingAdmins: [],
      updatedAdmins: [],
    };
  }
}
