import GoogleSignInButton from "@/components/auth/google-sign-in-button"
import { ClipboardList } from "lucide-react"

export default function SignInPage() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gradient-to-br from-background to-background/80">
      <div className="w-full max-w-md">
        <div className="rounded-xl bg-card p-8 shadow-lg border border-primary/20">
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold text-primary">Skills Assessment Tool</h1>
            <p className="mt-3 text-muted-foreground">
              Sign in with your company Google account to access the skills assessment tool.
            </p>
          </div>

          <div className="mb-8 flex justify-center">
            <div className="h-20 w-20 rounded-full bg-primary/10 flex items-center justify-center shadow-inner">
              <ClipboardList className="h-10 w-10 text-primary" />
            </div>
          </div>

          {/* Direct Google Login Button */}
          <GoogleSignInButton />

          <div className="mt-8 text-center">
            <p className="text-sm text-muted-foreground">
              Only users with authorized company email domains can access this tool.
            </p>
            <div className="mt-4 flex items-center justify-center gap-2">
              <div className="h-1.5 w-1.5 rounded-full bg-primary/40"></div>
              <div className="h-1.5 w-1.5 rounded-full bg-primary/60"></div>
              <div className="h-1.5 w-1.5 rounded-full bg-primary/80"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
