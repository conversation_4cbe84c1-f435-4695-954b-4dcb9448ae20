/* Import CSS to disable accessibility checker */
@import './disable-a11y.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Work Sans font is applied via Tailwind config */

@layer components {
  /* Assessment components with modern styling */
  .assessment-table {
    @apply w-full border border-border rounded-lg overflow-hidden shadow-sm;
  }

  .assessment-card {
    @apply bg-card border border-border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow;
  }

  .assessment-header {
    @apply bg-muted/20 p-4 border-b border-border;
  }

  .assessment-alert {
    @apply p-4 rounded-lg border;
  }

  .assessment-select {
    @apply rounded-md border border-border bg-background;
  }

  /* Modern card variations */
  .card-hover {
    @apply transition-all duration-200 hover:shadow-md hover:-translate-y-1;
  }

  .card-interactive {
    @apply cursor-pointer hover:border-primary/50;
  }

  /* Status indicators */
  .status-dot {
    @apply h-2.5 w-2.5 rounded-full inline-block;
  }

  .status-dot-success {
    @apply bg-green-500;
  }

  .status-dot-warning {
    @apply bg-yellow-500;
  }

  .status-dot-error {
    @apply bg-red-500;
  }

  .status-dot-neutral {
    @apply bg-gray-400;
  }

  /* Skill level indicators */
  .skill-level {
    @apply inline-flex items-center justify-center rounded-md px-2.5 py-0.5 text-xs font-medium;
  }

  .skill-level-below {
    @apply bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300;
  }

  .skill-level-at {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300;
  }

  .skill-level-above {
    @apply bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300;
  }

  .skill-level-none {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-800/50 dark:text-gray-300;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    /* Light mode theme */
    --background: 210 50% 98%;
    --foreground: 224 71% 4%;
    --card: 0 0% 100%;
    --card-foreground: 224 71% 4%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71% 4%;
    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222 47% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215 16% 47%;
    --accent: 221 83% 53%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 221 83% 53%;
    /* Vibrant chart colors */
    --chart-1: 221 83% 53%;
    --chart-2: 262 83% 58%;
    --chart-3: 316 70% 50%;
    --chart-4: 184 80% 45%;
    --chart-5: 130 60% 45%;
    --radius: 0.75rem;
    --sidebar-background: 210 50% 98%;
    --sidebar-foreground: 224 71% 4%;
    --sidebar-primary: 221 83% 53%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 96.1%;
    --sidebar-accent-foreground: 222 47% 11.2%;
    --sidebar-border: 214 32% 91%;
    --sidebar-ring: 221 83% 53%;
  }
  .dark {
    /* Modern neutral dark theme with high contrast buttons */
    --background: 220 17% 13%;
    --foreground: 0 0% 95%;
    --card: 220 17% 17%;
    --card-foreground: 0 0% 95%;
    --popover: 220 17% 17%;
    --popover-foreground: 0 0% 95%;
    --primary: 0 0% 95%;
    --primary-foreground: 220 17% 13%;
    --secondary: 220 17% 23%;
    --secondary-foreground: 0 0% 95%;
    --muted: 220 17% 23%;
    --muted-foreground: 220 10% 70%;
    --accent: 220 17% 23%;
    --accent-foreground: 0 0% 95%;
    --destructive: 0 70% 45%;
    --destructive-foreground: 0 0% 95%;
    --border: 220 17% 25%;
    --input: 220 17% 25%;
    --ring: 220 17% 40%;
    /* Neutral chart colors for dark mode */
    --chart-1: 0 0% 85%;
    --chart-2: 220 10% 65%;
    --chart-3: 220 10% 45%;
    --chart-4: 220 10% 25%;
    --chart-5: 220 10% 15%;
    --sidebar-background: 220 17% 10%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 0 0% 95%;
    --sidebar-primary-foreground: 220 17% 13%;
    --sidebar-accent: 220 17% 23%;
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 220 17% 25%;
    --sidebar-ring: 220 17% 40%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Add smooth transitions to interactive elements */
  button, a, input, select, textarea, .card, .dropdown-menu, .popover {
    @apply transition-all duration-200 ease-in-out;
  }

  /* Improve focus states */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Improve table styling */
  table {
    @apply w-full border-collapse;
  }

  th {
    @apply bg-muted text-muted-foreground font-medium text-sm p-3 text-left;
  }

  td {
    @apply p-3 border-b border-border;
  }

  tr:last-child td {
    @apply border-0;
  }

  tr:hover td {
    @apply bg-muted/40;
  }
}
