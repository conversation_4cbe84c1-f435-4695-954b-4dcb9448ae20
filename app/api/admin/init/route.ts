import { NextRequest, NextResponse } from 'next/server';
import { addAdminEmail, getAdminEmails } from '@/lib/services/config-service';

/**
 * Initialize admin emails from environment variables
 * This is a one-time setup endpoint that should be called during deployment
 */
export async function GET(request: NextRequest) {
  try {
    // Get the secret key from the request
    const secretKey = request.nextUrl.searchParams.get('key');
    
    // Check if the secret key is valid
    if (secretKey !== process.env.ADMIN_INIT_SECRET) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get existing admin emails
    const existingAdmins = await getAdminEmails();
    
    // Get admin emails from environment variables
    const envAdmins = (process.env.ADMIN_EMAILS || '').split(',')
      .map(email => email.trim())
      .filter(email => email);
    
    // Add admin emails from environment variables if they don't exist
    const results = await Promise.all(
      envAdmins.map(async (email) => {
        if (!existingAdmins.includes(email)) {
          return await addAdminEmail(email);
        }
        return true;
      })
    );
    
    // Get updated admin emails
    const updatedAdmins = await getAdminEmails();
    
    return NextResponse.json({
      success: true,
      message: 'Admin emails initialized',
      initialAdmins: envAdmins,
      existingAdmins,
      updatedAdmins,
      results
    });
  } catch (error) {
    console.error('Error initializing admin emails:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
