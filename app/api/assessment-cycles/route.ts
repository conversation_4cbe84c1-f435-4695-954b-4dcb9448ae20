import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { ObjectId } from 'mongodb';

/**
 * GET /api/assessment-cycles
 * Get assessment cycles with pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);

    // Pagination parameters
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const cursorId = searchParams.get('cursor');

    // Build query
    let query: any = {};

    // Filter by status if provided
    const status = searchParams.get('status');
    if (status) {
      query.status = status;
    }

    // Filter by business unit if provided
    const businessUnit = searchParams.get('businessUnit');
    if (businessUnit) {
      query.businessUnits = businessUnit;
    }

    // Add cursor-based pagination
    if (cursorId && ObjectId.isValid(cursorId)) {
      query._id = { $lt: new ObjectId(cursorId) };
    }

    // Get assessment cycles
    // Use a simpler approach to avoid type issues
    const collection = db.collection('assessment_cycles');

    // First get all matching documents
    let cycles = await collection.find(query).toArray();

    // Then sort and limit in memory
    cycles = cycles
      .sort((a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime())
      .slice(0, limit + 1);

    // Check if there are more results
    const hasMore = cycles.length > limit;
    if (hasMore) {
      cycles.pop(); // Remove the extra item
    }

    // Convert ObjectIds to strings for JSON serialization
    const serializedCycles = cycles.map(cycle => ({
      ...cycle,
      _id: cycle._id.toString()
    }));

    return NextResponse.json({
      cycles: serializedCycles,
      hasMore
    });
  } catch (error) {
    console.error('Error fetching assessment cycles:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/assessment-cycles
 * Create a new assessment cycle
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const userEmail = session.user.email;
    const user = await db.collection('user_profiles').findOne({ email: userEmail });

    if (!user || !user.isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const data = await request.json();

    // Validate required fields
    if (!data.name || !data.startDate || !data.endDate || !data.businessUnits) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Create new assessment cycle
    const newCycle = {
      name: data.name,
      description: data.description || '',
      startDate: new Date(data.startDate),
      endDate: new Date(data.endDate),
      status: data.status || 'upcoming',
      businessUnits: data.businessUnits,
      createdBy: userEmail,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await db.collection('assessment_cycles').insertOne(newCycle);

    // Get the created cycle
    const createdCycle = await db.collection('assessment_cycles').findOne({ _id: result.insertedId });

    if (!createdCycle) {
      return NextResponse.json({ error: 'Failed to retrieve created cycle' }, { status: 500 });
    }

    // Convert ObjectId to string for JSON serialization
    const serializedCycle = {
      ...createdCycle,
      _id: createdCycle._id.toString()
    };

    return NextResponse.json(serializedCycle);
  } catch (error) {
    console.error('Error creating assessment cycle:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
