import { NextRequest, NextResponse } from 'next/server';
import { ConfigDocument, CONFIG_KEYS } from '@/lib/models/config';

// Dynamic import to avoid build-time errors
const getMongoClient = async () => {
  const { default: clientPromise, getCollection } = await import('@/lib/mongodb');
  return { clientPromise, getCollection };
};

export async function GET(request: NextRequest) {
  try {
    // Dynamically import MongoDB client to avoid build-time errors
    const { clientPromise, getCollection } = await getMongoClient();

    // Test MongoDB connection
    const client = await clientPromise;
    const db = client.db(process.env.MONGODB_DB || 'skills-assessment');

    // Test collection access
    const collection = await getCollection<ConfigDocument>('config');

    // Try to get admin emails
    const config = await collection.findOne({ key: CONFIG_KEYS.ADMIN_EMAILS });

    // Get environment variables
    const envAdmins = (process.env.ADMIN_EMAILS || '').split(',')
      .map(email => email.trim())
      .filter(email => email);

    return NextResponse.json({
      success: true,
      message: 'MongoDB connection successful',
      dbInfo: {
        uri: process.env.MONGODB_URI ? 'Set' : 'Not set',
        dbName: process.env.MONGODB_DB || 'skills-assessment',
      },
      config,
      envAdmins,
    });
  } catch (error) {
    console.error('Error testing MongoDB connection:', error);
    return NextResponse.json(
      {
        error: 'MongoDB connection failed',
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      },
      { status: 500 }
    );
  }
}
