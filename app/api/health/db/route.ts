import { NextResponse } from "next/server"
import clientPromise from "@/lib/mongodb"

// Cache the connection status for 30 seconds to avoid repeated checks
let connectionStatus = {
  status: "unknown",
  lastChecked: 0,
  connectionTime: 0
}

export async function GET() {
  try {
    const now = Date.now()
    const startTime = now

    // Use cached result if it's less than 30 seconds old
    if (connectionStatus.status !== "unknown" && now - connectionStatus.lastChecked < 30000) {
      return NextResponse.json({
        status: connectionStatus.status,
        connectionTime: `${connectionStatus.connectionTime}ms`,
        cached: true,
        timestamp: new Date().toISOString()
      }, {
        status: connectionStatus.status === "connected" ? 200 : 500
      })
    }

    // Simple connection test without timeout
    const client = await clientPromise

    // Just do a simple ping - don't fetch server status which is expensive
    await client.db().command({ ping: 1 })

    const endTime = Date.now()
    const connectionTime = endTime - startTime

    // Update cache
    connectionStatus = {
      status: "connected",
      lastChecked: now,
      connectionTime
    }

    // Return success response with minimal info
    return NextResponse.json({
      status: "connected",
      connectionTime: `${connectionTime}ms`,
      timestamp: new Date().toISOString()
    }, { status: 200 })
  } catch (error) {
    console.error("Database connection error:", error)

    // Update cache
    connectionStatus = {
      status: "error",
      lastChecked: Date.now(),
      connectionTime: 0
    }

    // Return error response with minimal details
    return NextResponse.json(
      {
        status: "error",
        message: "Failed to connect to database",
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// Disable caching for this route
export const dynamic = "force-dynamic"
