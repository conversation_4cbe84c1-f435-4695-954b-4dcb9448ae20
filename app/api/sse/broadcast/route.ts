import { NextRequest, NextResponse } from 'next/server';
import { broadcastMessage } from '@/lib/sse-broadcast';

export const dynamic = 'force-dynamic';

/**
 * API endpoint to trigger SSE broadcasts
 * Used by scripts and server actions to send real-time updates
 */
export async function POST(request: NextRequest) {
  try {
    const message = await request.json();
    
    // Validate the message has required fields
    if (!message.type) {
      return NextResponse.json({ error: 'Message type is required' }, { status: 400 });
    }
    
    // Broadcast the message to all connected clients
    await broadcastMessage(message, true);
    
    return NextResponse.json({ 
      success: true, 
      message: 'Broadcast sent successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error broadcasting SSE message:', error);
    return NextResponse.json({ 
      error: 'Failed to broadcast message' 
    }, { status: 500 });
  }
}
