import { NextRequest, NextResponse } from 'next/server';
import { getConnectionCount } from '@/lib/sse-broadcast';

export const dynamic = 'force-dynamic';

/**
 * Health check endpoint for SSE connections
 * Returns the current number of active SSE connections
 */
export async function GET(request: NextRequest) {
  try {
    const connectionCount = getConnectionCount();
    
    return NextResponse.json({
      status: 'ok',
      connections: connectionCount,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error in SSE health check:', error);
    
    return NextResponse.json({
      status: 'error',
      error: 'Failed to get SSE connection status',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
