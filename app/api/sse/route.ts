import { NextRequest, NextResponse } from 'next/server';

// Import from the shared file
import '../../../lib/sse-broadcast';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    const responseStream = new TransformStream();
    const writer = responseStream.writable.getWriter();

    // Store the client connection for broadcasting
    global.connections.push(writer);

    // Remove closed connections
    const index = global.connections.length - 1;
    request.signal.addEventListener('abort', () => {
      try {
        global.connections.splice(index, 1);
        console.log(`SSE connection ${index} closed`);
      } catch (error) {
        console.error('Error removing closed SSE connection:', error);
      }
    });

    // Send initial message
    const encoder = new TextEncoder();
    await writer.write(encoder.encode(`data: ${JSON.stringify({ type: 'connected' })}\n\n`));

    // Send a heartbeat every 30 seconds to keep the connection alive
    const heartbeatInterval = setInterval(async () => {
      try {
        await writer.write(encoder.encode(`: heartbeat\n\n`));
      } catch (error) {
        // Connection likely closed, clear the interval
        clearInterval(heartbeatInterval);
      }
    }, 30000);

    // Clean up the heartbeat interval when the connection is closed
    request.signal.addEventListener('abort', () => {
      clearInterval(heartbeatInterval);
    });

    return new NextResponse(responseStream.readable, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-transform',
        'Connection': 'keep-alive',
        // Disable buffering in proxies and CDNs
        'X-Accel-Buffering': 'no',
      },
    });
  } catch (error) {
    console.error('Error setting up SSE connection:', error);
    return new NextResponse(JSON.stringify({ error: 'Failed to establish SSE connection' }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
}
