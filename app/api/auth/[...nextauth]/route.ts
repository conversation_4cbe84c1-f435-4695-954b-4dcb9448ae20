import NextAuth from "next-auth"
import GoogleProvider from "next-auth/providers/google"
import CredentialsProvider from "next-auth/providers/credentials"
import {
  REQUIRE_AUTH,
  GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET,
  ALLOWED_DOMAIN,
} from "@/lib/auth-config"
import { db } from "@/lib/db"
import { queryCache } from "@/lib/cache"

// Configure NextAuth based on environment settings
const authHandler = NextAuth({
  providers: [
    // Google provider for authentication
    GoogleProvider({
      clientId: GOOGLE_CLIENT_ID,
      clientSecret: GOOGLE_CLIENT_SECRET,
      // Allow any port for the callback URL
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
  ],
  callbacks: {
    async signIn({ account, profile, user }) {
      // For Google authentication, check domain if specified
      if (account?.provider === "google" && ALLOWED_DOMAIN) {
        const isAllowed = profile?.email?.endsWith(`@${ALLOWED_DOMAIN}`) ?? false

        // If allowed, update user login status
        if (isAllowed && user.email) {
          try {
            // Update user login status
            await db.collection('user_profiles').updateOne(
              { email: user.email },
              {
                $set: {
                  hasLoggedIn: true,
                  lastLogin: new Date(),
                  updatedAt: new Date()
                }
              }
            )

            // Invalidate cache for this user
            queryCache.delete(`user_profile:${user.email}`)
          } catch (error) {
            console.error('Error updating user login status:', error)
            // Still allow sign in even if tracking fails
          }
        }

        return isAllowed
      }

      return false
    },
    async session({ session, token }) {
      if (session?.user) {
        // Add user ID to session
        session.user = {
          ...session.user,
          id: token.sub || 'unknown-id'
        }
      }
      return session
    },
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  // Add cookie options to improve session handling
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
        maxAge: 30 * 24 * 60 * 60, // 30 days
      },
    },
  },
})

// Export the NextAuth handler directly
// The App Router in Next.js 13+ handles the request differently
export { authHandler as GET, authHandler as POST }
