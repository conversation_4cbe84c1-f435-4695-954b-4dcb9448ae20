import { NextRequest, NextResponse } from 'next/server';
import { initializeSettings } from '@/lib/actions/settings-actions';

/**
 * Initialize settings from environment variables
 * This is a one-time setup endpoint that should be called during deployment
 */
export async function GET(request: NextRequest) {
  try {
    // Get the secret key from the request
    const secretKey = request.nextUrl.searchParams.get('key');
    
    // Check if the secret key is valid
    if (secretKey !== process.env.SETTINGS_INIT_SECRET) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Initialize settings
    const success = await initializeSettings();
    
    return NextResponse.json({
      success,
      message: success ? 'Settings initialized successfully' : 'Failed to initialize settings'
    });
  } catch (error) {
    console.error('Error initializing settings:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
