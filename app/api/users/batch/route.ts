import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { ObjectId } from 'mongodb';

/**
 * GET /api/users/batch?ids=id1,id2,id3
 * Get multiple users by IDs
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user IDs from query parameters
    const { searchParams } = new URL(request.url);
    const idsParam = searchParams.get('ids');
    
    if (!idsParam) {
      return NextResponse.json({ error: 'No user IDs provided' }, { status: 400 });
    }
    
    const userIds = idsParam.split(',');
    
    // Convert string IDs to ObjectIds for valid ones
    const objectIds = userIds
      .filter(id => ObjectId.isValid(id))
      .map(id => new ObjectId(id));
    
    // Also include email lookups for IDs that aren't valid ObjectIds
    const emailIds = userIds.filter(id => !ObjectId.isValid(id));
    
    // Build query to find users by either ObjectId or email
    const query = {
      $or: [
        { _id: { $in: objectIds } },
        { email: { $in: emailIds } }
      ]
    };
    
    // Find users
    const users = await db.collection('user_profiles')
      .find(query)
      .toArray();
    
    // Convert ObjectIds to strings for JSON serialization
    const serializedUsers = users.map(user => ({
      ...user,
      _id: user._id.toString()
    }));
    
    return NextResponse.json(serializedUsers);
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
