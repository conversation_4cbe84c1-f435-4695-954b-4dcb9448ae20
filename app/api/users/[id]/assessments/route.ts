import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { ObjectId } from 'mongodb';

/**
 * GET /api/users/[id]/assessments
 * Get assessments for a user
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const { searchParams } = new URL(request.url);

    // Build query
    let query: any = {};

    // Handle special case for current user
    if (id === 'me') {
      const userEmail = session.user?.email;
      if (!userEmail) {
        return NextResponse.json({ error: 'User email not found in session' }, { status: 400 });
      }

      // Get user ID from email
      const user = await db.collection('user_profiles').findOne({ email: userEmail });
      if (!user) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }

      query.userId = user._id;
    } else if (ObjectId.isValid(id)) {
      query.userId = new ObjectId(id);
    } else {
      // If not a valid ObjectId, try to find user by email
      const user = await db.collection('user_profiles').findOne({ email: id });
      if (!user) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }

      query.userId = user._id;
    }

    // Filter by cycle if provided
    const cycleId = searchParams.get('cycleId');
    if (cycleId && ObjectId.isValid(cycleId)) {
      query.cycleId = new ObjectId(cycleId);
    }

    // Filter by status if provided
    const status = searchParams.get('status');
    if (status) {
      query.status = status;
    }

    // Filter by type if provided
    const type = searchParams.get('type');
    if (type) {
      query.type = type;
    }

    // Get assessments
    const assessments = await db.collection('assessments')
      .find(query)
      .sort({ createdAt: -1 })
      .toArray();

    // Convert ObjectIds to strings for JSON serialization
    const serializedAssessments = assessments.map((assessment: any) => ({
      ...assessment,
      _id: assessment._id.toString(),
      userId: assessment.userId.toString(),
      assessorId: assessment.assessorId.toString(),
      cycleId: assessment.cycleId.toString(),
      templateId: assessment.templateId.toString(),
      projectId: assessment.projectId ? assessment.projectId.toString() : undefined
    }));

    return NextResponse.json(serializedAssessments);
  } catch (error) {
    console.error('Error fetching user assessments:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
