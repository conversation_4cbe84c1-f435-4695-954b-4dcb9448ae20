import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { ObjectId } from 'mongodb';

/**
 * GET /api/users/[id]
 * Get a user by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    // Handle special case for current user
    if (id === 'me') {
      const userEmail = session.user?.email;
      if (!userEmail) {
        return NextResponse.json({ error: 'User email not found in session' }, { status: 400 });
      }

      const user = await db.collection('user_profiles').findOne({ email: userEmail });
      if (!user) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }

      // Convert ObjectId to string for JSON serialization
      const serializedUser = {
        ...user,
        _id: user._id.toString()
      };

      return NextResponse.json(serializedUser);
    }

    // Regular user lookup by ID
    let query = {};

    // Check if the ID is a valid ObjectId
    if (ObjectId.isValid(id)) {
      query = { _id: new ObjectId(id) };
    } else {
      // If not a valid ObjectId, try to find by email
      query = { email: id };
    }

    const user = await db.collection('user_profiles').findOne(query);

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Convert ObjectId to string for JSON serialization
    const serializedUser = {
      ...user,
      _id: user._id.toString()
    };

    return NextResponse.json(serializedUser);
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PATCH /api/users/[id]
 * Update a user by ID
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const data = await request.json();

    // Validate the request body
    if (!data || Object.keys(data).length === 0) {
      return NextResponse.json({ error: 'No data provided' }, { status: 400 });
    }

    // Remove fields that shouldn't be updated directly
    delete data._id;
    delete data.email; // Email should not be changed via this endpoint
    delete data.createdAt;

    // Add updatedAt timestamp
    data.updatedAt = new Date();

    // Update the user
    const result = await db.collection('user_profiles').updateOne(
      { _id: new ObjectId(id) },
      { $set: data }
    );

    if (result.matchedCount === 0) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get the updated user
    const updatedUser = await db.collection('user_profiles').findOne({ _id: new ObjectId(id) });

    if (!updatedUser) {
      return NextResponse.json({ error: 'Failed to retrieve updated user' }, { status: 500 });
    }

    // Convert ObjectId to string for JSON serialization
    const serializedUser = {
      ...updatedUser,
      _id: updatedUser._id.toString()
    };

    return NextResponse.json(serializedUser);
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
