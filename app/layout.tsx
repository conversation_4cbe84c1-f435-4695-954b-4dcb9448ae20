import type React from "react"
import "./globals.css"
import "./a11y-override.css"
import type { Metadata } from "next"
import { ThemeProvider } from "@/components/theme-provider"
import AuthSessionProvider from "@/components/auth/session-provider"
import { Toaster } from "@/components/ui/toaster"
import dynamic from 'next/dynamic'
import Script from 'next/script'
import { Work_Sans } from 'next/font/google'

// Configure Work Sans font
const workSans = Work_Sans({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  display: 'swap',
  variable: '--font-work-sans',
})

// Dynamically import the header with loading optimization
const Header = dynamic(() => import("@/components/header"), {
  loading: () => <div className="border-b py-4"></div>
})

// Disable font optimization for performance
export const metadata: Metadata = {
  title: "Skills Self-Assessment Tool",
  description: "Evaluate your technical and soft skills against target levels",
  generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning className={`${workSans.variable}`}>
      <head>
        {/* External CSS to disable accessibility checker */}
        {/* Using next/link instead of direct link tag */}

        {/* CSS is imported at the top of the file */}
      </head>
      <body className="font-sans">
        <ThemeProvider>
          <AuthSessionProvider>
            <div className="min-h-screen flex flex-col">
              <Header />
              <main className="flex-1">
                {children}
              </main>
            </div>
            <Toaster />
          </AuthSessionProvider>
        </ThemeProvider>

        {/* Disable Next.js accessibility checker */}
        <Script id="disable-a11y" strategy="afterInteractive">
          {`
            // Completely disable Next.js accessibility checker
            window.__NEXT_A11Y_CHECKER = false;
            window.__NEXT_A11Y_WIDGET = false;
            window.__NEXT_A11Y_AUDIT = false;
            window.__NEXT_A11Y_REPORT = false;
            window.__NEXT_A11Y_MODAL = false;
            window.__NEXT_A11Y_PANEL = false;
            window.__NEXT_A11Y_BUTTON = false;
            window.__NEXT_A11Y_ICON = false;
            window.__NEXT_A11Y_MESSAGE = false;
            window.__NEXT_A11Y_STATUS = false;
            window.__NEXT_A11Y_ERROR = false;
            window.__NEXT_A11Y_WARNING = false;
            window.__NEXT_A11Y_INFO = false;

            // Remove any existing accessibility checker elements
            if (typeof window !== 'undefined') {
              // Function to remove a11y elements
              function removeA11yElements() {
                const selectors = [
                  '[data-nextjs-a11y-checker]',
                  '[data-nextjs-a11y-widget]',
                  '[data-nextjs-a11y-audit]',
                  '[data-nextjs-a11y-report]',
                  '[data-nextjs-a11y-modal]',
                  '[data-nextjs-a11y-panel]',
                  '[data-nextjs-a11y-button]',
                  '[data-nextjs-a11y-icon]',
                  '[data-nextjs-a11y-message]',
                  '[data-nextjs-a11y-status]',
                  '[data-nextjs-a11y-error]',
                  '[data-nextjs-a11y-warning]',
                  '[data-nextjs-a11y-info]'
                ];

                selectors.forEach(selector => {
                  document.querySelectorAll(selector).forEach(el => el.remove());
                });
              }

              // Remove on load
              window.addEventListener('load', removeA11yElements);

              // Remove on DOMContentLoaded
              document.addEventListener('DOMContentLoaded', removeA11yElements);

              // Remove periodically
              setInterval(removeA11yElements, 1000);

              // Use MutationObserver to catch any new elements
              const observer = new MutationObserver(removeA11yElements);
              observer.observe(document, { childList: true, subtree: true });
            }
          `}
        </Script>
      </body>
    </html>
  )
}
