import { Metadata } from "next"
import { getServerSession } from "next-auth"
import { getAssessmentCycle } from "@/lib/actions/assessment-cycles"
import { getAllUserProfiles } from "@/lib/actions/admin"
import CycleSetupForm from "@/components/admin/cycle-setup-form"
import { notFound } from "next/navigation"

export const metadata: Metadata = {
  title: "Setup Assessment Cycle - Admin",
  description: "Configure team members and reviewers for an assessment cycle",
}

export default async function CycleSetupPage({ params }: { params: Promise<{ id: string }> }) {
  const session = await getServerSession()
  const userEmail = session?.user?.email || ""

  // Fetch the assessment cycle
  const resolvedParams = await params
  const cycleResult = await getAssessmentCycle(resolvedParams.id, userEmail)

  // Fetch all user profiles for team member selection
  // Use a large page size to get all users at once
  const usersResult = await getAllUserProfiles(userEmail, 1, 1000)

  if (!cycleResult.success || !cycleResult.data) {
    notFound()
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Setup Assessment Cycle</h1>
      </div>

      <CycleSetupForm
        cycle={cycleResult.data}
        users={usersResult.success ? usersResult.data : []}
        cycleId={resolvedParams.id}
      />
    </div>
  )
}
