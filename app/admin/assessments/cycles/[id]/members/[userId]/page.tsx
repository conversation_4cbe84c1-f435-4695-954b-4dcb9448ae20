import { Metadata } from "next"
import { getServerSession } from "next-auth"
import { getAssessmentComparison } from "@/lib/actions/assessment-cycles"
import MemberAssessmentView from "@/components/admin/member-assessment-view"
import { notFound } from "next/navigation"

export const metadata: Metadata = {
  title: "Member Assessment Details - Admin",
  description: "View assessment details for a team member",
}

export default async function MemberAssessmentPage({
  params
}: {
  params: Promise<{ id: string; userId: string }>
}) {
  const session = await getServerSession()
  const userEmail = session?.user?.email || ""

  // Fetch the assessment comparison data
  const resolvedParams = await params
  const result = await getAssessmentComparison(resolvedParams.id, resolvedParams.userId, userEmail)

  if (!result.success) {
    notFound()
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Member Assessment Details</h1>
      </div>

      <MemberAssessmentView
        comparisonData={result.data}
        cycleId={resolvedParams.id}
        userId={resolvedParams.userId}
      />
    </div>
  )
}
