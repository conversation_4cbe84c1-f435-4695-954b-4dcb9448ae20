import { Metadata } from "next"
import { getServerSession } from "next-auth"
import { getAssessmentCycle } from "@/lib/actions/assessment-cycles"
import CycleDetailsView from "@/components/admin/cycle-details-view"
import { notFound } from "next/navigation"

export const metadata: Metadata = {
  title: "Assessment Cycle Details - Admin",
  description: "View and manage an assessment cycle",
}

export default async function CycleDetailsPage({ params }: { params: Promise<{ id: string }> }) {
  const session = await getServerSession()
  const userEmail = session?.user?.email || ""

  // Fetch the assessment cycle
  const resolvedParams = await params
  const result = await getAssessmentCycle(resolvedParams.id, userEmail)

  if (!result.success || !result.data) {
    notFound()
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Assessment Cycle Details</h1>
      </div>

      <CycleDetailsView
        cycle={result.data}
        cycleId={resolvedParams.id}
      />
    </div>
  )
}
