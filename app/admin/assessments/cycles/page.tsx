import { Metadata } from "next"
import { getServerSession } from "next-auth"
import { getAllAssessmentCycles } from "@/lib/actions/assessment-cycles"
import AssessmentCyclesListWithSearch from "@/components/admin/assessment-cycles-list-with-search"

export const metadata: Metadata = {
  title: "Assessment Cycles - Admin",
  description: "Manage assessment cycles for the Skills Assessment Tool",
}

export default async function AssessmentCyclesPage() {
  const session = await getServerSession()
  const userEmail = session?.user?.email || ""

  // Fetch initial assessment cycles with pagination and search
  const result = await getAllAssessmentCycles(userEmail, 1, 10)

  // Ensure the data is properly serialized for client components
  // This prevents "Only plain objects can be passed to Client Components from Server Components" errors
  const serializedResult = {
    success: result.success,
    message: result.message,
    totalItems: result.totalItems,
    totalPages: result.totalPages,
    currentPage: result.currentPage,
    data: result.success && Array.isArray(result.data) ? result.data : []
  }

  return <AssessmentCyclesListWithSearch initialData={serializedResult} />
}
