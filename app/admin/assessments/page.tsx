import { Metadata } from "next"
import { getServerSession } from "next-auth"
import { getAllAssessments } from "@/lib/actions/assessment"
import AssessmentsList from "@/components/admin/assessments-list"

export const metadata: Metadata = {
  title: "Assessments - Admin",
  description: "Manage assessments in the Skills Assessment Tool",
}

export default async function AdminAssessmentsPage() {
  const session = await getServerSession()
  const userEmail = session?.user?.email || ""

  // Fetch initial assessments with pagination and search
  const result = await getAllAssessments(userEmail, 1, 10)

  return <AssessmentsList initialData={result} />
}
