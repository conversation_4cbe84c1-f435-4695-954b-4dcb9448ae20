import { getServerSession } from "next-auth"
import { notFound } from "next/navigation"
import { getDb } from "@/lib/db"
import { ObjectId } from "mongodb"
import { authConfig } from "@/lib/config"
import { format } from "date-fns"
import AssessmentDetail from "./assessment-detail"
import React from "react"
import { SerializedAssessment, HistoryChartData, HistoryEntry, Skill } from "./types"

export async function generateMetadata({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  return {
    title: `Assessment ${resolvedParams.id}`,
  }
}

export default async function AssessmentDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  const { id } = resolvedParams;
  const session = await getServerSession()
  const userEmail = session?.user?.email || ""

  // Check if the user is an admin
  if (!authConfig.isAdmin(userEmail)) {
    notFound()
  }

  // Fetch the assessment
  const database = await getDb()
  let assessment: any

  try {
    assessment = await database.collection("assessments").findOne({
      _id: new ObjectId(id)
    })
  } catch (error) {
    console.error("Error fetching assessment:", error)
    notFound()
  }

  if (!assessment) {
    notFound()
  }

  // Serialize the assessment data to make it safe for client components
  const serializedAssessment: SerializedAssessment = {
    id: assessment._id.toString(),
    userId: assessment.userId,
    name: assessment.name,
    userName: assessment.userName || "Unknown",
    userEmail: assessment.userEmail || "Unknown",
    businessUnit: assessment.businessUnit || "Unknown",
    careerLevel: assessment.careerLevel || "Unknown",
    jobRole: assessment.jobRole || "Unknown",
    skills: JSON.parse(JSON.stringify(assessment.skills || [])),
    createdAt: assessment.createdAt ? new Date(assessment.createdAt).toISOString() : null,
    updatedAt: assessment.updatedAt ? new Date(assessment.updatedAt).toISOString() : null,
    history: JSON.parse(JSON.stringify(assessment.history || []))
  }

  // Format history data for charts
  const historyData: HistoryChartData[] = serializedAssessment.history.map((entry: HistoryEntry) => {
    const skillLevels: Record<string, number> = {}

    entry.skills.forEach((skill: Skill) => {
      if (skill.currentLevel) {
        skillLevels[skill.id] = skill.currentLevel
      }
    })

    return {
      date: format(new Date(entry.date), "MMM d, yyyy"),
      ...skillLevels
    }
  })

  return <AssessmentDetail assessment={serializedAssessment} historyData={historyData} />
}
