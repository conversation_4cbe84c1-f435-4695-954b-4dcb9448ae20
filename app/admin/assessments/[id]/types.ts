// Define types for assessment data
export interface Skill {
  id: string;
  category: string;
  description: string;
  currentLevel: number | null;
  targetCL2: number;
  targetCL3: number;
  targetCL4: number;
  targetCL5: number;
  targetCL6: number;
}

export interface HistoryEntry {
  date: string;
  skills: Skill[];
}

export interface SerializedAssessment {
  id: string;
  userId: string;
  name: string;
  userName: string;
  userEmail: string;
  businessUnit: string;
  careerLevel: string;
  jobRole: string;
  skills: Skill[];
  createdAt: string | null;
  updatedAt: string | null;
  history: HistoryEntry[];
}

export interface HistoryChartData {
  date: string;
  [skillId: string]: string | number;
}
