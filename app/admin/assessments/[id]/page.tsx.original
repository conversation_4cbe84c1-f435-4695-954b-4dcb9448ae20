import { getServerSession } from "next-auth"
import { notFound } from "next/navigation"
import { getDb } from "@/lib/db"
import { ObjectId } from "mongodb"
import { authConfig } from "@/lib/config"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { format } from "date-fns"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts"

interface PageProps {
  params: { id: string };
  searchParams: Record<string, string | string[] | undefined>;
}

export default async function AssessmentDetailPage({ params }: PageProps) {
  const session = await getServerSession()
  const userEmail = session?.user?.email || ""

  // Check if the user is an admin
  if (!authConfig.isAdmin(userEmail)) {
    notFound()
  }

  // Fetch the assessment
  const database = await getDb()
  let assessment

  try {
    assessment = await database.collection("assessments").findOne({
      _id: new ObjectId(params.id)
    })
  } catch (error) {
    console.error("Error fetching assessment:", error)
    notFound()
  }

  if (!assessment) {
    notFound()
  }

  // Format history data for charts
  const historyData = assessment.history?.map((entry: any) => {
    const skillLevels = {} as Record<string, number>

    entry.skills.forEach((skill: any) => {
      if (skill.currentLevel) {
        skillLevels[skill.id] = skill.currentLevel
      }
    })

    return {
      date: format(new Date(entry.date), "MMM d, yyyy"),
      ...skillLevels
    }
  }) || []

  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin/assessments">Assessments</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink>{assessment.name}</BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">{assessment.name}</h1>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>User Information</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-2 gap-4">
              <div>
                <dt className="text-sm font-medium text-gray-500">Name</dt>
                <dd className="mt-1">{assessment.userName || "Unknown"}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Email</dt>
                <dd className="mt-1">{assessment.userEmail || "Unknown"}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Business Unit</dt>
                <dd className="mt-1">{assessment.businessUnit || "Unknown"}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Career Level</dt>
                <dd className="mt-1">{assessment.careerLevel || "Unknown"}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Job Role</dt>
                <dd className="mt-1">{assessment.jobRole || "Unknown"}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                <dd className="mt-1">
                  {assessment.updatedAt ? format(new Date(assessment.updatedAt), "MMM d, yyyy") : "Unknown"}
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Assessment Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-2 gap-4">
              <div>
                <dt className="text-sm font-medium text-gray-500">Skills Assessed</dt>
                <dd className="mt-1">
                  {assessment.skills ? assessment.skills.filter((s: any) => s.currentLevel).length : 0} /
                  {assessment.skills ? assessment.skills.length : 0}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Average Skill Level</dt>
                <dd className="mt-1">
                  {assessment.skills && assessment.skills.length > 0
                    ? (assessment.skills.reduce((sum: number, s: any) => sum + (s.currentLevel || 0), 0) /
                      assessment.skills.filter((s: any) => s.currentLevel).length).toFixed(1)
                    : "N/A"}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">History Entries</dt>
                <dd className="mt-1">{assessment.history ? assessment.history.length : 0}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">First Assessment</dt>
                <dd className="mt-1">
                  {assessment.createdAt ? format(new Date(assessment.createdAt), "MMM d, yyyy") : "Unknown"}
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="skills" className="space-y-4">
        <TabsList>
          <TabsTrigger value="skills">Skills</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="skills" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Skills Assessment</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Skill</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Current Level</TableHead>
                    <TableHead>Target Level</TableHead>
                    <TableHead>Gap</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {assessment.skills?.map((skill: any) => {
                    // Determine target level based on career level
                    let targetLevel = 0
                    if (assessment.careerLevel === "cl2") targetLevel = skill.targetCL2
                    else if (assessment.careerLevel === "cl3") targetLevel = skill.targetCL3
                    else if (assessment.careerLevel === "cl4") targetLevel = skill.targetCL4
                    else if (["tm1", "tm2"].includes(assessment.careerLevel)) targetLevel = skill.targetTM12
                    else if (["tm3", "tm4"].includes(assessment.careerLevel)) targetLevel = skill.targetTM34

                    const gap = (skill.currentLevel || 0) - targetLevel

                    return (
                      <TableRow key={skill.id}>
                        <TableCell className="font-medium">{skill.category}</TableCell>
                        <TableCell>{skill.description}</TableCell>
                        <TableCell>{skill.currentLevel || "Not assessed"}</TableCell>
                        <TableCell>{targetLevel}</TableCell>
                        <TableCell>
                          {skill.currentLevel ? (
                            <span className={gap >= 0 ? "text-green-600" : "text-red-600"}>
                              {gap >= 0 ? "+" : ""}{gap}
                            </span>
                          ) : (
                            "N/A"
                          )}
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Skill History</CardTitle>
            </CardHeader>
            <CardContent>
              {historyData.length > 0 ? (
                <div className="h-96">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={historyData} margin={{ top: 20, right: 30, left: 20, bottom: 10 }}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis domain={[0, 6]} ticks={[0, 1, 2, 3, 4, 5, 6]} />
                      <Tooltip />
                      <Legend />
                      {assessment.skills?.map((skill: any, index: number) => {
                        // Only show skills that have history data
                        if (historyData.some(entry => entry[skill.id])) {
                          return (
                            <Line
                              key={skill.id}
                              type="monotone"
                              dataKey={skill.id}
                              name={skill.category}
                              stroke={getLineColor(index)}
                              activeDot={{ r: 6 }}
                              connectNulls
                            />
                          )
                        }
                        return null
                      })}
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No history data available
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Helper function to get line colors
function getLineColor(index: number) {
  const colors = [
    "#8884d8", "#82ca9d", "#ffc658", "#ff8042", "#0088FE",
    "#00C49F", "#FFBB28", "#FF8042", "#a4de6c", "#d0ed57"
  ]
  return colors[index % colors.length]
}
