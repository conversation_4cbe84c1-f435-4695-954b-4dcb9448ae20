import { getServerSessionCached } from "@/lib/session-cache"
import { getSkillsAggregateData } from "@/lib/actions/admin"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, Users } from "lucide-react"
import { AdminDashboardResponse } from "@/lib/models/admin-dashboard"
import { skillsData } from "@/lib/skills-data"
import dynamic from 'next/dynamic'

// Dynamically import heavy components
const SkillBarChart = dynamic(() => import("@/components/charts").then(mod => mod.SkillBarChart), {
  loading: () => <div className="h-80 flex items-center justify-center">Loading chart...</div>
})

const DistributionPieChart = dynamic(() => import("@/components/charts").then(mod => mod.DistributionPieChart), {
  loading: () => <div className="h-80 flex items-center justify-center">Loading chart...</div>
})

const AdminDashboardStats = dynamic(() => import("@/components/admin/dashboard-stats"), {
  loading: () => <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
    {[...Array(4)].map((_, i) => (
      <div key={i} className="h-24 rounded-lg bg-muted animate-pulse"></div>
    ))}
  </div>
})

const AdminDashboardTables = dynamic(() => import("@/components/admin/dashboard-tables"), {
  loading: () => <div className="h-96 rounded-lg bg-muted animate-pulse"></div>
})
const AdminDashboardUsers = dynamic(() => import("@/components/admin/dashboard-users"), {
  loading: () => <div className="h-96 rounded-lg bg-muted animate-pulse"></div>
})

export default async function AdminDashboard() {
  const session = await getServerSessionCached()
  const userEmail = session?.user?.email || ""

  // Fetch aggregate data for the dashboard
  const aggregateData: AdminDashboardResponse = await getSkillsAggregateData(userEmail)

  // Handle error state
  if (!aggregateData.success) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {aggregateData.message || "Failed to load dashboard data. Please try again later."}
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Dashboard</h1>
      </div>

      <AdminDashboardStats data={aggregateData.data} />

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="skills">Skills Analysis</TabsTrigger>
          <TabsTrigger value="users">
            <Users className="h-4 w-4 mr-2" />
            Users
          </TabsTrigger>
          <TabsTrigger value="departments">Department Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Overview Charts */}

          <div>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="md:col-span-2">
                {aggregateData.data.topSkills && aggregateData.data.topSkills.length > 0 ? (
                  <SkillBarChart
                    title="Skill Proficiency Overview"
                    data={aggregateData.data.topSkills.map(skill => ({
                      id: skill.id,
                      name: skill.category,
                      average: skill.average
                    }))}
                  />
                ) : (
                  <Card>
                    <CardHeader>
                      <CardTitle>Skill Proficiency Overview</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="py-8 text-center text-muted-foreground">
                        No skill data available. Please complete some assessments first.
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              <div>
                {Object.entries(aggregateData.data.businessUnitBreakdown || {}).length > 0 ? (
                  <DistributionPieChart
                    title="Team Distribution"
                    data={Object.entries(aggregateData.data.businessUnitBreakdown || {})
                      .map(([name, value]) => ({ name, value }))
                      .filter(item => item.value > 0)}
                  />
                ) : (
                  <Card>
                    <CardHeader>
                      <CardTitle>Team Distribution</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="py-8 text-center text-muted-foreground">
                        No business unit data available.
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              <div>
                {Object.entries(aggregateData.data.careerLevelBreakdown || {}).length > 0 ? (
                  <DistributionPieChart
                    title="Career Level Breakdown"
                    data={Object.entries(aggregateData.data.careerLevelBreakdown || {})
                      .map(([name, value]) => ({ name, value }))
                      .filter(item => item.value > 0)}
                  />
                ) : (
                  <Card>
                    <CardHeader>
                      <CardTitle>Career Level Breakdown</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="py-8 text-center text-muted-foreground">
                        No career level data available.
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="skills" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Skills Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <AdminDashboardTables data={aggregateData.data} />

                {/* Top Skills Chart */}
                <div className="mt-8 pt-4 border-t">
                  <h3 className="text-lg font-medium mb-4">Top Skills Visualization</h3>
                  <SkillBarChart
                    title="Top Skills by Average Rating"
                    data={aggregateData.data.topSkills.map(skill => ({
                      id: skill.id,
                      name: skill.category,
                      average: skill.average
                    }))}
                  />
                </div>

                {/* Improvement Areas Chart */}
                <div className="mt-8 pt-4 border-t">
                  <h3 className="text-lg font-medium mb-4">Improvement Areas Visualization</h3>
                  <SkillBarChart
                    title="Skills Needing Improvement"
                    data={aggregateData.data.improvementAreas.map(skill => ({
                      id: skill.id,
                      name: skill.category,
                      average: skill.average
                    }))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Performance Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <AdminDashboardUsers data={aggregateData.data} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="departments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Department Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Skills by Business Unit</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2 px-4">Skill</th>
                          {Object.keys(aggregateData.data.skillsByBusinessUnit || {}).map(bu => (
                            <th key={bu} className="text-left py-2 px-4">{bu}</th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {Object.entries(aggregateData.data.skillAverages || {}).map(([skillId, _]) => {
                          // Find the skill name from skillsData
                          const skillInfo = skillsData.find(s => s.id === skillId);
                          const skillName = skillInfo?.category || skillId;

                          return (
                            <tr key={skillId} className="border-b hover:bg-gray-50">
                              <td className="py-2 px-4 font-medium">{skillName}</td>
                              {Object.keys(aggregateData.data.skillsByBusinessUnit || {}).map(bu => (
                                <td key={`${bu}-${skillId}`} className="py-2 px-4">
                                  {aggregateData.data.skillsByBusinessUnit?.[bu]?.[skillId]?.toFixed(1) || "-"}
                                </td>
                              ))}
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* Team Distribution Charts */}
                <div className="mt-8 pt-4 border-t">
                  <h3 className="text-lg font-medium mb-4">Team Distribution</h3>
                  <DistributionPieChart
                    title="Team Assessment Distribution"
                    data={Object.entries(aggregateData.data.businessUnitBreakdown || {})
                      .map(([name, value]) => ({ name, value }))
                      .filter(item => item.value > 0)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>


    </div>
  )
}
