import { Metadata } from "next"
import { getServerSessionCached } from "@/lib/session-cache"
import { getAllUserProfiles } from "@/lib/actions/admin-users"
import UsersList from "@/components/admin/users-list"

export const metadata: Metadata = {
  title: "Users - Admin",
  description: "Manage users in the Skills Assessment Tool",
}

export default async function AdminUsersPage() {
  const session = await getServerSessionCached()
  const userEmail = session?.user?.email || ""

  // Fetch initial user profiles with pagination and search
  const result = await getAllUserProfiles(userEmail, 1, 10)

  // Ensure the data is properly serialized for client components
  // This prevents "Only plain objects can be passed to Client Components from Server Components" errors
  const serializedResult = {
    success: result.success,
    message: result.message,
    totalItems: result.totalItems,
    totalPages: result.totalPages,
    currentPage: result.currentPage,
    data: result.success && Array.isArray(result.data) ?
      // Use JSON.parse(JSON.stringify()) to ensure deep serialization of all objects
      JSON.parse(JSON.stringify(result.data)) :
      []
  }

  return <UsersList initialData={serializedResult} userEmail={userEmail} />
}
