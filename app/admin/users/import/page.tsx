import { Metadata } from "next"
import { getServerSessionCached } from "@/lib/session-cache"
import UserImportForm from "@/components/admin/user-import-form"

export const metadata: Metadata = {
  title: "Import Users - Admin",
  description: "Import users from CSV or other sources",
}

export default async function UserImportPage() {
  const session = await getServerSessionCached()
  const userEmail = session?.user?.email || ""

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Import Users</h1>
      </div>

      <UserImportForm />
    </div>
  )
}
