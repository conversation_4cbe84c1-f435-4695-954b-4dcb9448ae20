import { Metadata } from "next"
import { getServerSession } from "next-auth/next"
import UserForm from "@/components/admin/user-form"

export const metadata: Metadata = {
  title: "Add User - Admin",
  description: "Add a new user to the Skills Assessment Tool",
}

export default async function AddUserPage() {
  const session = await getServerSession()
  const userEmail = session?.user?.email || ""

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Add User</h1>
      </div>

      <UserForm adminEmail={userEmail} />
    </div>
  )
}
