import { Metadata } from "next"
import { getServerSession } from "next-auth/next"
import { getUserProfileById } from "@/lib/actions/admin-users"
import UserForm from "@/components/admin/user-form"
import { notFound } from "next/navigation"

export const metadata: Metadata = {
  title: "Edit User - Admin",
  description: "Edit a user in the Skills Assessment Tool",
}

export default async function EditUserPage({ params }: { params: Promise<{ id: string }> }) {
  const session = await getServerSession()
  const userEmail = session?.user?.email || ""

  // Fetch the user profile
  const resolvedParams = await params
  const result = await getUserProfileById(resolvedParams.id, userEmail)

  if (!result.success) {
    notFound()
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Edit User</h1>
      </div>

      <UserForm adminEmail={userEmail} user={result.data} />
    </div>
  )
}
