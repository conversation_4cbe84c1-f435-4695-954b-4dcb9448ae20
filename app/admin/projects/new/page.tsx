import { Metadata } from "next"
import { getServerSession } from "next-auth/next"
import ProjectForm from "@/components/admin/project-form"

export const metadata: Metadata = {
  title: "Add Project - Admin",
  description: "Add a new project to the Skills Assessment Tool",
}

export default async function AddProjectPage() {
  const session = await getServerSession()
  const userEmail = session?.user?.email || ""

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Add Project</h1>
      </div>

      <ProjectForm adminEmail={userEmail} />
    </div>
  )
}
