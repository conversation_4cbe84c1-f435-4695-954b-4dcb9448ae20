import { Metadata } from "next"
import { getServerSession } from "next-auth/next"
import { notFound } from "next/navigation"
import Link from "next/link"
import { ArrowLeft, Users, Code } from "lucide-react"
import { getProjectById } from "@/lib/actions/projects"
import ProjectForm from "@/components/admin/project-form"
import { authConfig } from "@/lib/config"

export const metadata: Metadata = {
  title: "Edit Project - Admin",
  description: "Edit project details in the Skills Assessment Tool",
}

export default async function EditProjectPage({ params }: { params: Promise<{ id: string }> }) {
  // Ensure params is properly awaited in Next.js 15
  const { id } = await params

  const session = await getServerSession()
  const userEmail = session?.user?.email || ""

  // Check if the user is an admin
  if (!authConfig.isAdmin(userEmail)) {
    notFound()
  }

  // Fetch the project
  const result = await getProjectById(id, userEmail)

  if (!result.success) {
    notFound()
  }

  return (
    <>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Edit Project</h1>
        <Link
          href="/admin/projects"
          className="flex items-center text-blue-500 hover:text-blue-700"
        >
          <ArrowLeft className="mr-1 h-4 w-4" />
          Back to Projects
        </Link>
      </div>

      <div className="flex space-x-4 border-b mb-6 pb-2">
        <Link
          href={`/admin/projects/${id}`}
          className="flex items-center px-3 py-2 text-sm font-medium border-b-2 -mb-px border-blue-500 text-blue-600"
        >
          Details
        </Link>
        <Link
          href={`/admin/projects/${id}/users`}
          className="flex items-center px-3 py-2 text-sm font-medium border-b-2 -mb-px border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
        >
          <Users className="mr-2 h-4 w-4" />
          Team Members
        </Link>
        <Link
          href={`/admin/projects/${id}/skills`}
          className="flex items-center px-3 py-2 text-sm font-medium border-b-2 -mb-px border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
        >
          <Code className="mr-2 h-4 w-4" />
          Skills
        </Link>
      </div>

      <ProjectForm adminEmail={userEmail} project={result.data} />
    </>
  )
}
