import { Metadata } from "next"
import { getServerSession } from "next-auth/next"
import { getAllProjects } from "@/lib/actions/projects"
import ProjectsList from "@/components/admin/projects-list"

export const metadata: Metadata = {
  title: "Projects - Admin",
  description: "Manage projects in the Skills Assessment Tool",
}

export default async function AdminProjectsPage() {
  const session = await getServerSession()
  const userEmail = session?.user?.email || ""

  // Fetch initial project data with pagination and search
  const result = await getAllProjects(userEmail, 1, 10)

  // Ensure the data is properly serialized for client components
  // This prevents "Only plain objects can be passed to Client Components from Server Components" errors
  const serializedResult = {
    success: result.success,
    message: result.message,
    totalItems: result.totalItems,
    totalPages: result.totalItems ? Math.ceil(result.totalItems / 10) : 1,
    currentPage: result.currentPage,
    data: result.success && Array.isArray(result.data) ? result.data : []
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Projects</h1>
      </div>

      <ProjectsList initialData={serializedResult} userEmail={userEmail} />
    </div>
  )
}
