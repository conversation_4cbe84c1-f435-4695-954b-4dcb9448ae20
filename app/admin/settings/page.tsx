import { getServerSessionCached } from "@/lib/session-cache"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { authConfig } from "@/lib/config"
import { redirect } from "next/navigation"
import { isAdmin, getAdmins, getAssessmentSettings } from "@/lib/actions/settings-actions"
import AdminSettingsManager from "@/components/admin/admin-settings-manager"
import AssessmentSettingsManager from "@/components/admin/assessment-settings-manager"

export default async function AdminSettingsPage() {
  const session = await getServerSessionCached()

  // If user is not logged in, redirect to sign in
  if (!session?.user?.email) {
    redirect('/auth/signin');
  }

  // Check if user is an admin
  const isUserAdmin = await isAdmin(session.user.email);

  // If user is not an admin, redirect to home
  if (!isUserAdmin) {
    redirect('/');
  }

  // Get admin emails and assessment settings from the database
  const [adminEmails, assessmentSettings] = await Promise.all([
    getAdmins(),
    getAssessmentSettings()
  ]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Settings</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Application Settings</CardTitle>
          <CardDescription>
            Configure application settings and manage administrators.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Authentication</h3>

            <div className="grid gap-2">
              <Label htmlFor="requireAuth">Require Authentication</Label>
              <div className="flex items-center space-x-2">
                <Switch id="requireAuth" checked={authConfig.requireAuth} disabled />
                <Label htmlFor="requireAuth">
                  {authConfig.requireAuth ? "Enabled" : "Disabled"}
                </Label>
              </div>
              <p className="text-sm text-muted-foreground">
                When enabled, users must sign in to access the application.
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="allowedDomain">Allowed Email Domain</Label>
              <Input id="allowedDomain" value={authConfig.allowedDomain} readOnly />
              <p className="text-sm text-muted-foreground">
                Only users with email addresses from this domain can sign in.
              </p>
            </div>
          </div>

          <AdminSettingsManager
            initialAdminEmails={adminEmails}
            currentUserEmail={session.user.email}
          />

          <AssessmentSettingsManager
            initialIntervalMonths={assessmentSettings.intervalMonths}
            initialAllowUpdates={assessmentSettings.allowUpdates}
          />
        </CardContent>
      </Card>
    </div>
  )
}
