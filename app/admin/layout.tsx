import { redirect } from "next/navigation"
import AdminSidebar from "@/components/admin/sidebar"
import { isAdmin } from "@/lib/services/settings-service"
import { initializeSettings } from "@/lib/actions/settings-actions"
import { getServerSessionCached } from "@/lib/session-cache"

// Initialize settings flag
let settingsInitialized = false

// This layout will replace the root layout for admin pages
export const metadata = {
  title: "Admin Dashboard - Skills Assessment Tool",
  description: "Admin dashboard for the Skills Assessment Tool",
}

// Tell Next.js to use this layout instead of the root layout
export const dynamic = 'force-dynamic'

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Get the user session using cached version
  const session = await getServerSessionCached()

  // Check if the user is authenticated
  if (!session || !session.user || !session.user.email) {
    redirect("/auth/signin")
  }

  // Initialize settings if not already initialized
  if (!settingsInitialized) {
    try {
      await initializeSettings()
      settingsInitialized = true
      console.log('Settings initialized successfully')
    } catch (error) {
      console.error('Error initializing settings:', error)
    }
  }

  // Check if the user is an admin
  const isUserAdmin = await isAdmin(session.user.email)
  if (!isUserAdmin) {
    redirect("/")
  }

  return (
    <div className="flex min-h-screen">
      <AdminSidebar />
      <div className="flex-1 p-8">{children}</div>
    </div>
  )
}
