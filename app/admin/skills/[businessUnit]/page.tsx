import { Metadata } from "next"
import { notFound } from "next/navigation"
import { getBUSkillTemplate } from "@/lib/actions/admin-skills"
import { businessUnitNames, businessUnitOptions } from "@/lib/models/skill-template"
import SkillTemplateEditor from "@/components/admin/skill-template-editor"

export const metadata: Metadata = {
  title: "Edit Skill Template",
  description: "Edit skill template for a business unit",
}

export default async function EditSkillTemplatePage({
  params,
}: {
  params: Promise<{ businessUnit: string }>
}) {
  const { businessUnit } = await params

  // Validate business unit
  if (!businessUnitOptions.includes(businessUnit)) {
    notFound()
  }

  const result = await getBUSkillTemplate(businessUnit)

  if (!result.success) {
    return (
      <div className="container py-10">
        <div className="p-8 text-center bg-red-50 rounded-lg border border-red-200">
          <h1 className="text-2xl font-bold text-red-800 mb-2">Error</h1>
          <p className="text-red-600">{result.message || "Failed to load skill template"}</p>
        </div>
      </div>
    )
  }

  const template = result.data
  const isDefault = result.isDefault === true

  return (
    <div className="container py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">
          {isDefault ? "Create" : "Edit"} Skill Template
        </h1>
        <p className="text-muted-foreground mt-2">
          {isDefault
            ? `Create a new skill template for ${businessUnitNames[businessUnit] || businessUnit}`
            : `Edit the skill template for ${template.name}`
          }
        </p>
      </div>

      <SkillTemplateEditor
        template={template}
      />
    </div>
  )
}
