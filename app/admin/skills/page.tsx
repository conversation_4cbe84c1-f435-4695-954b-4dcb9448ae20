import { Metadata } from "next"
import { getBUSkillTemplates } from "@/lib/actions/admin-skills"
import { businessUnitNames } from "@/lib/models/skill-template"
import AdminSkillsList from "@/components/admin/skills-list"

export const metadata: Metadata = {
  title: "Admin - Core Skills Templates",
  description: "Manage core skills templates for different business units",
}

export default async function AdminSkillsPage() {
  const result = await getBUSkillTemplates()

  // Ensure the data is properly serialized for client components
  // This prevents "Only plain objects can be passed to Client Components from Server Components" errors
  const serializedTemplates = result.success ?
    JSON.parse(JSON.stringify(result.data)) :
    []

  return (
    <div className="container py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Skill Templates</h1>
        <p className="text-muted-foreground mt-2">
          Manage assessment skill templates for different business units.
        </p>
      </div>

      <AdminSkillsList templates={serializedTemplates} businessUnitNames={businessUnitNames} />
    </div>
  )
}
