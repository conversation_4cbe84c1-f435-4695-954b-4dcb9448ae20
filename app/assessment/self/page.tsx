import { getServerSessionCached } from "@/lib/session-cache"
import { redirect } from "next/navigation"
import { getUserProfile } from "@/lib/actions/user-profile"
import { REQUIRE_AUTH } from "@/lib/auth-config"
import { getActiveAssessmentCycle } from "@/lib/actions/assessment-cycles"

export default async function SelfAssessmentRedirectPage() {
  // Get the user session using cached version
  const session = await getServerSessionCached()

  // If authentication is required and user is not logged in, redirect to sign in
  if (REQUIRE_AUTH && (!session || !session.user)) {
    redirect("/auth/signin")
  }

  // Initialize user email
  let userEmail = ""

  // If user is authenticated, get their profile
  if (session?.user) {
    userEmail = session.user.email || ""

    // Try to get user profile from database
    const userProfileResult = await getUserProfile(userEmail)
    if (!userProfileResult.success || !userProfileResult.data || !userProfileResult.data.profileCompleted) {
      // If no profile exists or is incomplete, redirect to home to complete profile
      redirect("/")
    }
  }

  // Get active assessment cycle
  const cycleResult = await getActiveAssessmentCycle()

  if (cycleResult.success && cycleResult.data) {
    // For now, redirect to the regular skills assessment page
    // This allows users to create self-assessments even if they're not in a formal cycle
    redirect("/skills-assessment")
  } else {
    // If no active cycle, redirect to home
    redirect("/")
  }
}
