import { getServerSessionCached } from "@/lib/session-cache"
import { redirect } from "next/navigation"
import dynamic from 'next/dynamic'
import { Suspense } from 'react'
import { getUserProfile } from "@/lib/actions/user-profile"
import { REQUIRE_AUTH } from "@/lib/auth-config"
import { getAssessmentDetails } from "@/lib/actions/assessment-cycles"

// Dynamically import heavy components
const SkillAssessment = dynamic(() => import("@/components/skill-assessment"), {
  loading: () => <div className="p-8 text-center">Loading assessment tool...</div>,
  ssr: true
})

export default async function SelfAssessmentPage({ params }: { params: Promise<{ id: string }> }) {
  // Get the user session using cached version
  const session = await getServerSessionCached()

  // If authentication is required and user is not logged in, redirect to sign in
  if (REQUIRE_AUTH && (!session || !session.user)) {
    redirect("/auth/signin")
  }

  // Initialize user profile
  let userProfile = {
    businessUnit: "",
    careerLevel: "",
    jobRole: "",
    profileCompleted: false
  }
  let userName = ""
  let userEmail = ""
  let userId = ""

  // If user is authenticated, get their profile
  if (session?.user) {
    userName = session.user.name || "User"
    userEmail = session.user.email || ""
    userId = session.user.id || session.user.email || ""

    // Try to get user profile from database
    const userProfileResult = await getUserProfile(userEmail)
    if (userProfileResult.success && userProfileResult.data) {
      // Ensure we have the correct type structure
      userProfile = {
        businessUnit: userProfileResult.data.businessUnit || "",
        careerLevel: userProfileResult.data.careerLevel || "",
        jobRole: userProfileResult.data.jobRole || "",
        profileCompleted: userProfileResult.data.profileCompleted || false
      }
    } else {
      // If no profile exists, redirect to home to complete profile
      redirect("/")
    }
  }

  // Get assessment details
  const resolvedParams = await params
  const assessmentResult = await getAssessmentDetails(resolvedParams.id, userEmail)
  if (!assessmentResult.success || !assessmentResult.data) {
    redirect("/")
  }

  const assessment = assessmentResult.data.assessment

  // Verify this is a self assessment and the user is the reviewer
  if (assessment.relationshipType !== 'self' || assessment.reviewerId !== userId) {
    redirect("/")
  }

  return (
    <div className="container mx-auto py-8 px-4 max-w-7xl">
      <div className="mb-8 p-6 bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl border border-primary/20 shadow-md">
        <h2 className="text-2xl font-semibold mb-3 text-primary">Self Assessment</h2>
        <p className="text-muted-foreground">
          Rate your current skill level for each category and see how you compare to target levels for different roles.
        </p>
      </div>

      <Suspense fallback={
        <div className="p-8 text-center rounded-xl border border-border bg-card shadow-md">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="h-8 w-8 rounded-full border-4 border-primary/30 border-t-primary animate-spin"></div>
            <p className="text-muted-foreground">Loading assessment tool...</p>
          </div>
        </div>
      }>
        <SkillAssessment
          userId={userId}
          userName={userName}
          userEmail={userEmail}
          businessUnit={userProfile.businessUnit}
          careerLevel={userProfile.careerLevel}
          jobRole={userProfile.jobRole}
          assessmentType="self"
          cycleId={assessment.cycleId}
        />
      </Suspense>
    </div>
  )
}
