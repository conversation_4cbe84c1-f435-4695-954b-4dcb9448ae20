import { getServerSessionCached } from "@/lib/session-cache"
import { redirect } from "next/navigation"
import dynamic from 'next/dynamic'
import { Suspense } from 'react'
import { getUserProfile } from "@/lib/actions/user-profile"
import { REQUIRE_AUTH } from "@/lib/auth-config"
import SettingsInitializer from "@/components/settings-initializer"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { db } from "@/lib/db"
import { queryCache } from "@/lib/cache"
import TemplateChangeListener from "@/components/template-change-listener"
import { legacyBusinessUnitMapping } from "@/lib/models/skill-template"
import { authConfig } from "@/lib/config"

// Dynamically import heavy components
const SkillAssessment = dynamic(() => import("@/components/skill-assessment"), {
  loading: () => <div className="p-8 text-center">Loading assessment tool...</div>,
  ssr: true
})

const AssessmentDashboard = dynamic(() => import("@/components/assessment-dashboard"), {
  loading: () => <div className="p-8 text-center">Loading assessment dashboard...</div>,
  ssr: true
})

export default async function Home() {
  // Get the user session using cached version
  const session = await getServerSessionCached()

  // Debug logging
  console.log('Session check:', {
    hasSession: !!session,
    hasUser: !!session?.user,
    userEmail: session?.user?.email,
    requireAuth: REQUIRE_AUTH
  })

  // If authentication is required and user is not logged in, redirect to sign in
  if (REQUIRE_AUTH && (!session || !session.user)) {
    console.log('Redirecting to signin - no valid session')
    redirect("/auth/signin")
  }

  // Initialize user profile
  let userProfile = {
    businessUnit: "",
    careerLevel: "",
    jobRole: "",
    profileCompleted: false
  }
  let userName = ""
  let userEmail = ""
  let userId = ""

  // Check if any skill templates exist
  let hasSkillTemplates = false

  // If user is authenticated, get their profile
  if (session?.user) {
    userName = session.user.name || "User"
    userEmail = session.user.email || ""
    userId = session.user.id || session.user.email || ""

    // Try to get user profile from database
    const userProfileResult = await getUserProfile(userEmail)

    if (userProfileResult.success && userProfileResult.data) {
      // Ensure we have the correct type structure
      userProfile = {
        businessUnit: userProfileResult.data.businessUnit || "",
        careerLevel: userProfileResult.data.careerLevel || "",
        jobRole: userProfileResult.data.jobRole || "",
        profileCompleted: userProfileResult.data.profileCompleted || false
      }

      // Auto-complete the profile if it's not completed
      if (!userProfile.profileCompleted && REQUIRE_AUTH) {
        // Update the profile to mark it as completed
        await db.collection("user_profiles").updateOne(
          { email: userEmail },
          {
            $set: {
              profileCompleted: true,
              hasLoggedIn: true,
              lastLogin: new Date(),
              updatedAt: new Date(),
            },
          }
        )

        // Invalidate the cache for this user profile
        queryCache.delete(`user_profile:${userEmail}`)
      }
    } else {
      // If no profile exists, still proceed without creating a profile
      if (REQUIRE_AUTH) {
        // Just use the existing userProfile object with empty values
        // The user will still be able to access the dashboard
        userProfile = {
          businessUnit: "",
          careerLevel: "",
          jobRole: "",
          profileCompleted: true
        }

        // Invalidate the cache for this user profile
        queryCache.delete(`user_profile:${userEmail}`)
      }
    }


  }

  // Check if a skill template exists for the user's business unit
  hasSkillTemplates = false; // Default to false



  if (userProfile.businessUnit) {
    // Check for templates in the database
    const templates = await db
      .collection('skill-templates')
      .find({})
      .toArray();

    // Map user's business unit to the new consolidated business unit
    const mappedBusinessUnit = legacyBusinessUnitMapping[userProfile.businessUnit.toLowerCase()] || userProfile.businessUnit.toLowerCase();

    // Check if any template matches the mapped business unit (case insensitive)
    const matchingTemplate = templates.find((t: any) =>
      t.businessUnit?.toLowerCase() === mappedBusinessUnit.toLowerCase()
    );

    // Only show self-assessment if a matching template exists
    hasSkillTemplates = !!matchingTemplate;

    // 🚀 GOD MODE: Admin override - admins can always access skills assessment for testing
    const isAdmin = authConfig.isAdmin(userEmail);
    if (isAdmin && !hasSkillTemplates) {
      hasSkillTemplates = true; // Admin can always access for testing
    }
  } else {
    // 🚀 GOD MODE: Admin override - admins can always access skills assessment for testing
    const isAdmin = authConfig.isAdmin(userEmail);
    if (isAdmin) {
      hasSkillTemplates = true; // Admin can always access for testing
    }
  }

  // Initialize settings
  await SettingsInitializer()

  // Check if admin override was used
  const isAdmin = authConfig.isAdmin(userEmail);
  let adminOverrideActive = false;

  if (userProfile.businessUnit) {
    const templates = await db
      .collection('skill-templates')
      .find({})
      .toArray();

    const mappedBusinessUnit = legacyBusinessUnitMapping[userProfile.businessUnit.toLowerCase()] || userProfile.businessUnit.toLowerCase();
    const matchingTemplate = templates.find((t: any) =>
      t.businessUnit?.toLowerCase() === mappedBusinessUnit.toLowerCase()
    );

    adminOverrideActive = isAdmin && hasSkillTemplates && !matchingTemplate;
  }

  return (
    <TemplateChangeListener businessUnit={userProfile.businessUnit}>
      <div className="container mx-auto py-8 px-4 max-w-7xl">

        <div className="mb-8 p-6 bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl border border-primary/20 shadow-md">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-2xl font-semibold text-primary">Welcome, {userName}</h2>
            {adminOverrideActive && (
              <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 border border-purple-200">
                🚀 God Mode Active
              </div>
            )}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-card/80 p-4 rounded-lg shadow-md border border-border">
              <span className="text-xs uppercase tracking-wider text-muted-foreground block mb-1">Business Unit</span>
              <span className="font-medium text-lg">{userProfile.businessUnit.toUpperCase()}</span>
            </div>
            <div className="bg-card/80 p-4 rounded-lg shadow-md border border-border">
              <span className="text-xs uppercase tracking-wider text-muted-foreground block mb-1">Career Level</span>
              <span className="font-medium text-lg">{userProfile.careerLevel.toUpperCase()}</span>
            </div>
            <div className="bg-card/80 p-4 rounded-lg shadow-md border border-border">
              <span className="text-xs uppercase tracking-wider text-muted-foreground block mb-1">Job Role</span>
              <span className="font-medium text-lg">{userProfile.jobRole}</span>
            </div>
          </div>
        </div>

        <Tabs defaultValue="dashboard" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-8">
            <TabsTrigger value="dashboard">Assessment Dashboard</TabsTrigger>
            {hasSkillTemplates && (
              <TabsTrigger value="self-assessment">
                Self Assessment {adminOverrideActive && "🚀"}
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="dashboard">
            <Suspense fallback={
              <div className="p-8 text-center rounded-xl border border-border bg-card shadow-md">
                <div className="flex flex-col items-center justify-center space-y-4">
                  <div className="h-8 w-8 rounded-full border-4 border-primary/30 border-t-primary animate-spin"></div>
                  <p className="text-muted-foreground">Loading assessment dashboard...</p>
                </div>
              </div>
            }>
              <AssessmentDashboard
                userId={userId}
                userName={userName}
                userEmail={userEmail}
                businessUnit={userProfile.businessUnit}
              />
            </Suspense>
          </TabsContent>

          {hasSkillTemplates && (
            <TabsContent value="self-assessment">
              <Suspense fallback={
                <div className="p-8 text-center rounded-xl border border-border bg-card shadow-md">
                  <div className="flex flex-col items-center justify-center space-y-4">
                    <div className="h-8 w-8 rounded-full border-4 border-primary/30 border-t-primary animate-spin"></div>
                    <p className="text-muted-foreground">Loading assessment tool...</p>
                  </div>
                </div>
              }>
                <SkillAssessment
                  userId={userId}
                  userName={userName}
                  userEmail={userEmail}
                  businessUnit={userProfile.businessUnit}
                  careerLevel={userProfile.careerLevel}
                  jobRole={userProfile.jobRole}
                />
              </Suspense>
            </TabsContent>
          )}
        </Tabs>
      </div>
    </TemplateChangeListener>
  )
}
