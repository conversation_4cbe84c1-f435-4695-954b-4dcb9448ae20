/** @type {import('next').NextConfig} */
const nextConfig = {
  // Expose environment variables to the client
  env: {
    NEXT_PUBLIC_ADMIN_EMAILS: process.env.ADMIN_EMAILS || "",
  },
  // Enable React strict mode for better development experience
  reactStrictMode: true,

  // External packages for server components
  serverExternalPackages: ["mongodb", "@mongodb-js/zstd"],

  // Experimental features
  experimental: {
    // Disable type checking in development for faster compilation
    typedRoutes: false,
    // Optimize for faster development on M3 Mac
    optimizePackageImports: [
      'lucide-react',
      '@radix-ui/react-*',
      'react-hook-form',
      'swr',
      'date-fns',
      'recharts',
      'react-chartjs-2',
      'chart.js'
    ],
    // Optimize for faster compilation
    optimizeCss: true,
    // Disable server actions in development for faster compilation
    serverActions: {
      bodySizeLimit: '2mb',
    },
    // Disable React Server Components in development for faster compilation
    // This option is not available in Next.js 15.2.4
  },

  // Disable image optimization in development for faster compilation
  images: {
    ...(process.env.NODE_ENV === 'development' ? {
      unoptimized: true,
    } : {}),
  },

  // Disable TypeScript type checking in development for faster compilation
  typescript: {
    ignoreBuildErrors: process.env.NODE_ENV === "development",
    tsconfigPath: "tsconfig.json",
  },

  // Compiler options for better performance (only for production)
  ...(process.env.NODE_ENV === 'production' ? {
    compiler: {
      removeConsole: true,
    }
  } : {}),

  // Performance optimizations
  compress: true,
  poweredByHeader: false,
  generateEtags: true,

  // Output standalone build for better Docker support
  output: "standalone",
};

module.exports = nextConfig;
