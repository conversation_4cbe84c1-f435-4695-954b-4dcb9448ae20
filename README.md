# Skills Assessment Application

A comprehensive web application for tracking and visualizing professional skills development across engineering teams. This tool enables organizations to:

- Conduct multi-perspective skills assessments (self, manager, and peer evaluations)
- Compare current skill levels against IC (Individual Contributor) career level benchmarks (CL1-CL6)
- Track skills development over time with assessment cycles and history
- Manage project-specific skills and core competencies
- Visualize skill gaps and create development plans
- Support multiple business units with customized skill templates

## Features

### Core Assessment Features
- **Multi-Perspective Assessments**: Self, manager, and peer assessment types
- **IC Career Level Integration**: Built-in support for Individual Contributor levels CL1-CL6 (Intern/Cadet to Principal Engineer)
- **Skills Rating**: Rate skills on a 1-6 scale with IC level guidance and references
- **Assessment Cycles**: Coordinated assessment periods with configurable repeat intervals
- **Project Skills Assessment**: Evaluate skills in the context of specific projects
- **Core Skills Templates**: Organization-wide competency frameworks

### Data Management & Analytics
- **Assessment History**: Track progress over time with detailed historical data
- **Data Visualization**: Charts and summaries of skill development and gaps
- **CSV Import/Export**: Bulk data management for users, projects, and skills
- **Real-time Updates**: Server-Sent Events (SSE) for live data synchronization
- **Assessment Context Management**: Configurable contexts organized by business units

### Administration & Configuration
- **Admin Dashboard**: Comprehensive management interface for stakeholders
- **User Management**: Create, edit, and import users with role-based access
- **Business Unit Support**: Customized skill templates per business unit (Web, Mobile, Cloud, Data, AI)
- **Project Management**: Track team members and project-specific skill requirements
- **Skills Template Management**: Create and manage both core and project-specific skills

> **Note**: The application uses MongoDB for data storage and NextAuth.js for authentication. For development purposes, authentication can be configured to be bypassed using environment variables.

## Tech Stack

- **Frontend**: Next.js 15 (App Router), React, TypeScript, Tailwind CSS
- **UI Components**: Radix UI, Shadcn UI, Recharts for data visualization
- **Authentication**: NextAuth.js with Google OAuth provider
- **Database**: MongoDB with Mongoose ODM
- **Real-time**: Server-Sent Events (SSE) for live updates
- **Development**: Turbopack for fast hot reloading, Bun for build optimization
- **Deployment**: Docker, Docker Compose, MongoDB Atlas support
- **Career Framework**: IC Level matrices (CL1-CL6) with HTML reference materials

## Getting Started

### Prerequisites

- Node.js 18+ (Node.js 20+ recommended)
- npm, yarn, or Bun package manager
- Docker and Docker Compose (for database or full containerized setup)
- Google OAuth credentials (for authentication)
- MongoDB (local installation or MongoDB Atlas for production)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd skills-assessment
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env.local` file in the root directory:
```bash
cp .env.local.example .env.local
```

4. Edit the `.env.local` file with your configuration:
```
# MongoDB Connection String
MONGODB_URI=mongodb://localhost:27017/skills-assessment

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key-change-this-in-production

# Google OAuth Credentials
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

## Development Experience (DevEx)

This project offers multiple development workflows to suit different preferences and needs.

### Local Development (Recommended for Speed)

For the fastest development experience with instant hot reloading:

```bash
# Start MongoDB in Docker but run Next.js locally
./scripts/dev-local.sh
```

This will:
- Start only the MongoDB container in Docker
- Run Next.js directly on your local machine with Turbopack
- Provide instant hot reloading and much faster compilation

To stop the local development environment:

```bash
./scripts/stop-local.sh
```

### Docker Development

If you prefer a fully containerized development environment:

```bash
# Create .env file if it doesn't exist
cp .env.example .env

# Start the development environment
./scripts/dev-docker.sh
# Or run directly with Docker Compose
docker-compose up app-dev mongodb mongo-express
```

This will start:
- The Next.js application in development mode at [http://localhost:3000](http://localhost:3000)
- MongoDB database at localhost:27017
- MongoDB Express admin interface at [http://localhost:8081](http://localhost:8081)

**Note**: The Docker development environment has slower hot reloading and compilation times compared to local development.

### Production Environment

For production-like testing, you can use the production Docker Compose configuration:

```bash
# Start the production environment
docker-compose --profile prod up app-prod mongodb mongo-express
```

This will:
- Build the application using the Dockerfile
- Run it in production mode at [http://localhost:3001](http://localhost:3001)
- Require proper authentication (Google OAuth)

### Environment Variables

The Docker Compose setup uses several environment variables that can be set in your `.env` file:

- `REQUIRE_AUTH`: Set to `false` to bypass authentication (development only)
- `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET`: Required for Google authentication
- `NEXTAUTH_SECRET`: Secret key for NextAuth
- `ALLOWED_DOMAIN`: Email domain allowed for authentication

### Data Persistence

MongoDB data is stored in a Docker volume named `mongodb_data`. This ensures that your data persists even when containers are stopped or removed.

### MongoDB Admin Interface

The MongoDB Express admin interface is available at [http://localhost:8081](http://localhost:8081) with these default credentials:

- Username: `admin`
- Password: `password`

You can change these in your `.env` file by setting `MONGO_EXPRESS_USER` and `MONGO_EXPRESS_PASSWORD`.

## Project Structure

- `/app`: Next.js 15 app router components and routes
  - `/admin`: Admin dashboard pages and components
  - `/api`: API routes for data operations
    - `/sse`: Server-Sent Events endpoint for real-time updates
  - `/assessment`: Assessment pages (self, manager, peer)
  - `/auth`: Authentication pages
- `/components`: React components
  - `/ui`: Reusable UI components (Shadcn UI)
  - `/admin`: Admin-specific components
  - `/auth`: Authentication-related components
- `/lib`: Utility functions and business logic
  - `/actions`: Server actions for data operations
  - `/models`: MongoDB models and schemas
  - `/hooks`: Custom React hooks including SSE
  - `/ic-skills-data.ts`: IC career level skills definitions
  - `/sse-broadcast.ts`: Server-Sent Events broadcasting
- `/career-level`: IC career level reference matrices (HTML files)
  - `IC_L1.html` to `IC_L6.html`: Official career level skill definitions
- `/public`: Static assets
- `/scripts`: Development and utility scripts
  - `dev-local.sh`: Start local development with MongoDB in Docker
  - `dev-docker.sh`: Start full Docker development environment
  - `stop-local.sh`: Stop local development environment
- `/styles`: Global CSS styles

## Production Deployment

### Local Production Build

To build and run the production version locally:

```bash
# Build the application
npm run build

# Start the production server
npm start
```

### Docker Production Deployment

For a production-like environment using Docker:

```bash
# Build and start the production environment
docker compose --profile prod build app-prod
docker compose up app-prod mongodb mongo-express -d
```

This will:
- Build the application using the Dockerfile
- Run it in production mode at [http://localhost:3001](http://localhost:3001)
- Require proper authentication (Google OAuth)
- Use MongoDB Atlas for production database (configured in `.env.production`)

### Production Environment Variables

For production deployment, create a `.env.production` file with:

```bash
# Production MongoDB (MongoDB Atlas recommended)
MONGODB_URI=mongodb+srv://username:<EMAIL>/skills-assessment

# Production domain
NEXTAUTH_URL=https://skills-assessment.stratpoint.io

# Production secrets
NEXTAUTH_SECRET=your-production-secret-key

# Google OAuth (production credentials)
GOOGLE_CLIENT_ID=your-production-google-client-id
GOOGLE_CLIENT_SECRET=your-production-google-client-secret

# Email domain restriction
ALLOWED_DOMAIN=stratpoint.io
```

### Stopping the Production Environment

To stop the production environment:

```bash
docker compose down app-prod mongodb mongo-express
```

## IC Career Level Framework

The application is built around the Individual Contributor (IC) career level framework with six levels:

- **CL1 (IC L1)**: Intern/Cadet/Career Shifter - Foundational learning
- **CL2 (IC L2)**: Junior Engineer - Guided development
- **CL3 (IC L3)**: Engineer - Independent contribution
- **CL4 (IC L4)**: Senior Engineer - Technical leadership
- **CL5 (IC L5)**: Staff Engineer - Organizational impact
- **CL6 (IC L6)**: Principal Engineer - Strategic influence

### Skills Categories

The framework covers 18 core skills across three main categories:

#### I. Technical Proficiency & Engineering Craft
1. Core Programming & Tooling
2. Software Engineering Fundamentals
3. System Design & Architecture
4. Code Quality & Standards
5. Testing & Debugging
6. Version Control (Git)
7. Development & Operational Tools
8. Database & Data Management
9. Security Principles & Practices
10. Technical Documentation

#### II. Problem Solving & Execution
11. Analytical & Critical Thinking
12. Task & Project Execution
13. Technical Debt Management

#### III. Collaboration, Leadership & Impact
14. Communication & Collaboration
15. Mentorship & Technical Guidance
16. Technical Leadership & Ownership
17. Operational Excellence & System Reliability
18. Innovation & Strategic Contribution

### Career Level References

The `/career-level/` directory contains official HTML matrices (`IC_L1.html` to `IC_L6.html`) that define:
- Expected proficiency and behavior for each level
- Learning objectives and activities
- Key criteria for advancement
- Training and certification recommendations

## Admin Interface

The application includes a comprehensive admin interface accessible to administrators:

### Core Administration Features
1. **User Management**: Create, edit, import, and manage user profiles with business unit assignments
2. **Assessment Cycles**: Create and manage coordinated assessment periods with configurable intervals
3. **Skills Template Management**:
   - Core Skills Templates: Organization-wide competency frameworks
   - Project Skills: Project-specific skill requirements and team assignments
4. **Assessment Context Management**: Configure assessment contexts by business unit
5. **Data Analytics**: View assessment summaries and skill development across the organization

### Business Unit Support

The application supports multiple engineering business units:
- **Web**: Frontend and backend web development
- **Mobile**: Native iOS/Android and Flutter development
- **Cloud**: Infrastructure, DevOps, and cloud platforms
- **Data**: Data engineering, analytics, and data science
- **AI**: Machine learning and artificial intelligence

Each business unit can have:
- Customized core skills templates aligned with IC career levels
- Project-specific skill requirements
- Tailored assessment contexts and criteria

## Assessment Types

The application supports three types of skills assessments:

### 1. Self Assessment
- Users evaluate their own skills against IC career level benchmarks
- Provides IC level guidance based on the user's current career level
- Includes both core competencies and project-specific skills
- Accessible via `/assessment/self` or through assessment cycles

### 2. Manager Assessment
- Managers evaluate their direct reports' skills
- Uses the same skill framework with manager perspective
- Accessible via `/assessment/manager/[userId]`
- Integrated with assessment cycles for coordinated reviews

### 3. Peer Assessment
- Team members evaluate each other's skills
- Provides 360-degree feedback perspective
- Accessible via `/assessment/peer/[userId]`
- Can be configured as part of assessment cycles

### Assessment Contexts
- Assessments can be conducted within specific contexts (e.g., business unit focus)
- Contexts are configurable by administrators
- Each context can have different skill templates and criteria

## Real-time Updates

The application uses Server-Sent Events (SSE) to provide real-time updates without requiring page refreshes:

### How it works:
1. The server maintains a list of connected clients through the SSE endpoint (`/api/sse`)
2. When templates, assessments, or other data changes, the server broadcasts messages to all connected clients
3. The client-side components listen for these messages and refresh data automatically
4. Users always see the most up-to-date information without manual page refreshes

### Implementation:
- **Server-side**: `lib/sse-broadcast.ts` and `app/api/sse/route.ts`
- **Client-side**: `lib/hooks/use-sse.ts` and `components/template-change-listener.tsx`

## Troubleshooting

### MongoDB Connection Issues

If the application cannot connect to MongoDB:

1. **Local Development**: Ensure MongoDB container is running via `./scripts/dev-local.sh`
2. **Connection String**: Verify the `MONGODB_URI` in your `.env.local` file is correct
3. **MongoDB Atlas**: For production, ensure your IP is whitelisted and credentials are correct
4. **Network Settings**: Check that firewall settings allow the connection

### Authentication Issues

If you encounter authentication issues:

1. **Google OAuth Setup**:
   - Verify Google OAuth credentials in Google Cloud Console
   - Ensure authorized redirect URIs include your domain + `/api/auth/callback/google`
   - For local development: `http://localhost:3000/api/auth/callback/google`
   - For production: `https://your-domain.com/api/auth/callback/google`

2. **Environment Variables**:
   - Check `NEXTAUTH_URL` matches your current domain
   - Ensure `NEXTAUTH_SECRET` is set to a secure random string
   - Verify `ALLOWED_DOMAIN` is set correctly for your organization

3. **Development Mode**: Set `REQUIRE_AUTH=false` in `.env.local` to bypass authentication during development

### Build Issues

If you encounter build or TypeScript errors:

1. **Dependencies**: Run `npm install` to ensure all dependencies are installed
2. **Type Errors**: The project uses strict TypeScript - ensure all types are properly defined
3. **IC Skills Data**: Verify that all skills in `lib/ic-skills-data.ts` have complete L1-L6 descriptions
4. **Career Level Files**: Ensure all HTML files in `/career-level/` are present and properly formatted

### Performance Issues

If the application is running slowly:

1. **Development**: Use `./scripts/dev-local.sh` for fastest development experience with Turbopack
2. **Docker**: The Docker development environment is slower - use local development when possible
3. **Database**: Consider using MongoDB Atlas for better performance in production
4. **SSE Connections**: Monitor SSE connections - too many open connections can impact performance

## Contributing

1. Follow the existing code style and TypeScript conventions
2. Ensure all lint issues are resolved before committing (`npm run build`)
3. Test changes across different assessment types and business units
4. Update documentation when adding new features
5. Consider the impact on IC career level framework when making changes

## License

[MIT License](LICENSE)
