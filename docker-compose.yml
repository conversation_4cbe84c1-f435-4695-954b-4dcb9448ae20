services:
  # Development service with hot reloading
  app-dev:
    image: node:20-alpine
    working_dir: /app
    command: sh -c "apk add --no-cache python3 make g++ krb5-dev bash && npm install --legacy-peer-deps --force && npx next dev --hostname 0.0.0.0"
    ports:
      - "3000:3000"
    volumes:
      - .:/app:delegated
      - node_modules:/app/node_modules
      - .next-cache:/app/.next
    env_file:
      - .env.local
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongodb:27017/skills-assessment
      - NEXTAUTH_URL=http://localhost:3000
      # File watching for Docker
      - WATCHPACK_POLLING=true
      - CHOKIDAR_USEPOLLING=true
      # Performance optimizations
      - NODE_OPTIONS=--max-old-space-size=4096
      # Disable telemetry
      - NEXT_TELEMETRY_DISABLED=1
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - app-network

  # Production build service
  app-prod:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3001:3000"
    env_file:
      - .env.production
    environment:
      # Using MongoDB Atlas as configured in .env.production
      # No need to override MONGODB_URI as it's already in .env.production
      NODE_ENV: "production"
    restart: unless-stopped
    networks:
      - app-network
    profiles:
      - prod

  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - app-network
    restart: unless-stopped
    # Ultra-optimized MongoDB settings for M3 Mac
    command: >
      mongod
      --wiredTigerCacheSizeGB 0.5
      --setParameter transactionLifetimeLimitSeconds=30
      --setParameter diagnosticDataCollectionEnabled=false
      --setParameter logLevel=0
    environment:
      - MONGO_INITDB_DATABASE=skills-assessment
    # Minimal healthcheck to speed up startup
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 2s
      retries: 2
      start_period: 2s

  # MongoDB admin interface (optional)
  mongo-express:
    image: mongo-express:latest
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_SERVER=mongodb
      - ME_CONFIG_MONGODB_PORT=27017
      - ME_CONFIG_BASICAUTH_USERNAME=${MONGO_EXPRESS_USER:-admin}
      - ME_CONFIG_BASICAUTH_PASSWORD=${MONGO_EXPRESS_PASSWORD:-password}
    depends_on:
      mongodb:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - app-network

  # Cloudflare Tunnel for secure access
  tunnel:
    container_name: cloudflared-tunnel
    image: cloudflare/cloudflared
    restart: unless-stopped
    command: tunnel run
    env_file:
      - .env.production
    environment:
      - TUNNEL_TOKEN=${TUNNEL_TOKEN}
    networks:
      - app-network
    profiles:
      - prod

networks:
  app-network:
    driver: bridge

volumes:
  mongodb_data:
    driver: local
  node_modules:
    driver: local
  .next-cache:
    driver: local
