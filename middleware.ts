import { getToken } from "next-auth/jwt"
import { type NextRequest, NextResponse } from "next/server"
import { REQUIRE_AUTH, NEXTAUTH_SECRET } from "./lib/auth-config"

export async function middleware(req: NextRequest) {
  // Skip authentication check if not required
  if (!REQUIRE_AUTH) {
    return NextResponse.next()
  }

  const path = req.nextUrl.pathname

  // Define public paths that don't require authentication
  const isPublicPath = path.startsWith("/auth")

  // Get the session token
  const token = await getToken({
    req,
    secret: NEXTAUTH_SECRET,
  })

  // Redirect unauthenticated users to login page
  if (!token && !isPublicPath) {
    // Create the redirect URL using the request's origin
    const redirectUrl = new URL("/auth/signin", req.nextUrl.origin)
    return NextResponse.redirect(redirectUrl)
  }

  // Allow authenticated users to access protected routes
  const response = NextResponse.next()

  // Add header to disable Next.js accessibility checker
  response.headers.set('X-Next-A11y-Checker', 'disabled')

  return response
}

// Specify which routes to run the middleware on
export const config = {
  matcher: [
    // Apply to all routes except:
    // - API routes
    // - SSE endpoint (explicitly excluded)
    // - Next.js static files
    // - Next.js image optimization files
    // - Favicon
    "/((?!api/sse|api|_next/static|_next/image|favicon.ico).*)"
  ],
}
