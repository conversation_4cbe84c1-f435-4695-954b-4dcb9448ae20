import { ObjectId } from "mongodb";

export interface UserProfile {
  _id: string | ObjectId;
  email: string;
  name: string;
  businessUnit: string;
  careerLevel: string;
  jobRole: string;
  managerEmail?: string;
  profileCompleted: boolean;
  hasLoggedIn: boolean;
  createdBy?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

export interface UserSession {
  user: {
    id: string;
    name: string;
    email: string;
    image?: string;
    role?: string;
  };
  expires: string;
}

export interface UserWithAssessments extends UserProfile {
  assessments: {
    cycleId: string | ObjectId;
    cycleName: string;
    assessmentCount: number;
    completedCount: number;
    averageSkillLevel?: number;
  }[];
}
