import { ObjectId } from "mongodb";

export type AssessmentStatus = 'draft' | 'submitted' | 'approved' | 'rejected';
export type AssessmentType = 'self' | 'manager' | 'peer';
export type TemplateType = 'standard' | 'project';

export interface Skill {
  name: string;
  description: string;
  category: string;
  level?: number;
  comments?: string;
}

export interface AssessmentTemplate {
  _id: string | ObjectId;
  name: string;
  description: string;
  businessUnit: string;
  templateType: TemplateType;
  skills: Skill[];
  createdAt: Date | string;
  updatedAt: Date | string;
}

export interface AssessmentCycle {
  _id: string | ObjectId;
  name: string;
  description: string;
  startDate: Date | string;
  endDate: Date | string;
  status: 'active' | 'completed' | 'upcoming';
  businessUnits: string[];
  createdAt: Date | string;
  updatedAt: Date | string;
}

export interface Assessment {
  _id: string | ObjectId;
  userId: string | ObjectId;
  assessorId: string | ObjectId;
  cycleId: string | ObjectId;
  templateId: string | ObjectId;
  projectId?: string | ObjectId;
  status: AssessmentStatus;
  type: AssessmentType;
  skills: Skill[];
  overallComments?: string;
  submittedAt?: Date | string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

export interface AssessmentSummary {
  userId: string | ObjectId;
  userName: string;
  userEmail: string;
  businessUnit: string;
  careerLevel: string;
  jobRole: string;
  cycleId: string | ObjectId;
  cycleName: string;
  assessmentCount: number;
  averageSkillLevel: number;
  topSkills: {
    name: string;
    level: number;
  }[];
  weakestSkills: {
    name: string;
    level: number;
  }[];
  completionStatus: {
    self: boolean;
    manager: boolean;
    peer: number; // Number of peer assessments completed
  };
}
